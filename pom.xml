<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wosai.mc</groupId>
    <artifactId>merchant-center</artifactId>
    <packaging>pom</packaging>
    <version>1.15.12</version>
    <name>merchant-center</name>
    <description>Wosai Spring Boot Demo Project</description>

    <organization>
        <name>Wosai</name>
        <url>https://www.shouqianba.com/</url>
    </organization>

    <parent>
        <groupId>com.wosai.pantheon</groupId>
        <artifactId>uranus</artifactId>
        <version>2.0.2</version>
    </parent>
    <profiles>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.mc</groupId>
                <artifactId>merchant-center-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>vault-sdk</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>wosai-database-instrumentation-springboot2</artifactId>
                <version>5.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>apollo-client</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.25.3</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>4.1.114.Final</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <configuration>
                    <destFile>${project.build.directory}/coverage-reports/jacoco-unit.exec</destFile>
                    <dataFile>${project.build.directory}/coverage-reports/jacoco-unit.exec</dataFile>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>**/domain/**.class</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>jacoco-site</id>
                        <phase>test</phase>
                        <!-- <phase>prepare-package</phase> -->
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

    <modules>
        <module>merchant-center-api</module>
        <module>merchant-center-service</module>
    </modules>
</project>
