# 引用git.wosai-inc.com/do/gitlab-ci这个项目中的java-docker.yml作为CI模板
include:
  project: "do/gitlab-ci"
  file: "/maven.yml"

# 以下是选填项
variables:
#  MAVEN_CLI_OPTS: "-Dmaven.test.skip=true sonar:sonar -Dsonar.host.url=https://sonar.wosai-inc.com -Dsonar.login=**************************************** -Dsonar.core.codeCoveragePlugin=jacoco"
  MAVEN_CLI_OPTS: "-Dmaven.test.skip=true"
  JFROG_MVNC_OPTS: "--exclude-patterns=*service.jar"
