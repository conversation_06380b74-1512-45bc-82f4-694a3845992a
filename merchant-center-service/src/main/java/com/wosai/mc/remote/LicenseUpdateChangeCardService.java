package com.wosai.mc.remote;


import com.wosai.mc.model.dto.req.LicenseUpdateCheckBankChangeReqDTO;
import com.wosai.mc.model.resp.CheckBankAccountResp;

public interface LicenseUpdateChangeCardService {

    /**
     * 同名换卡校验接口
     * @param checkBankAccountReq 校验请求对象
     * @return 校验结果
     */
    CheckBankAccountResp sameNameCheckBankCardChange(LicenseUpdateCheckBankChangeReqDTO checkBankAccountReq);

    /**
     * 异名换卡校验接口
     * @param checkBankAccountReq 校验请求对象
     * @return 校验结果
     */
    CheckBankAccountResp diffNameCheckBankCardChange(LicenseUpdateCheckBankChangeReqDTO checkBankAccountReq);

}
