package com.wosai.mc.remote;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * Created by zhudongquan on 16/10/31.
 */
@JsonRpcService(value = "rpc/merchantconfig")
@Validated
public interface IMerchantService {

    /**
     * 更新商户管理密码(不需要原始密码)
     *
     * @param merchantId
     * @param nwPassword
     * @return
     */
    Map updateMerchantManagerPasswordWithNew(@NotBlank(message = "请输入商户id") String merchantId, @NotBlank(message = "请输入新密码") String nwPassword);


}
