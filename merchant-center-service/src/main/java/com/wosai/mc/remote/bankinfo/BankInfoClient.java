package com.wosai.mc.remote.bankinfo;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.mc.remote.bankinfo.model.LicenseDicInfo;
import com.wosai.mc.remote.bankinfo.model.LicenseTypeCode;
import com.wosai.mc.remote.bankinfo.model.PapersImageInfo;
import com.wosai.mc.remote.bankinfo.model.TradeLicenseDicInfo;
import com.wosai.upay.bank.info.api.model.BusinessLicenseDicChangeVo;
import com.wosai.upay.bank.info.api.model.Industry;
import com.wosai.upay.bank.info.api.model.LicenseDic;
import com.wosai.upay.bank.info.api.service.BusinessLicenseDicV2Service;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.bank.info.api.service.LicenseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Component
public class BankInfoClient {

    @Autowired
    private IndustryV2Service industryV2Service;
    @Autowired
    private BusinessLicenseDicV2Service businessLicenseDicV2Service;
    @Autowired
    private LicenseDicService licenseDicService;

    private final Cache<String, Object> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();


    public List<Integer> queryIndustrySupportLicenseTypes(String industryId) {
        Map<String, Object> industry = industryV2Service.getIndustry(industryId);
        List<Integer> licenseTypes = (List<Integer>) industry.get(Industry.LICENSE_TYPES);
        Collections.sort(licenseTypes);
        return licenseTypes;
    }

    public List<Integer> queryIndustrySupportTradeLicenses(String industryId) {
        Map<String, Object> industry = industryV2Service.getIndustry(industryId);
        if (!WosaiMapUtils.getBooleanValue(industry, Industry.NEED_TRADE_LICENSE)) {
            return new ArrayList<>();
        }
        List<Integer> tradeLicenseTypes = (List<Integer>) industry.get(Industry.TRADE_LICENSE_TYPES);
        Collections.sort(tradeLicenseTypes);
        return tradeLicenseTypes;
    }

    public Map<Integer, PapersImageInfo> queryPapersImageInfo() {
        try {
            return (Map<Integer, PapersImageInfo>) cache.get("papers_image", () -> {
                JSONObject jsonObject = businessLicenseDicV2Service.metaData(null);
                List<Map> papersImage = (List<Map>) jsonObject.get("papers_image");
                return papersImage.stream()
                        .collect(Collectors.toMap(r -> WosaiMapUtils.getInteger(r, "value"), r -> new PapersImageInfo()
                                .setType(WosaiMapUtils.getInteger(r, "value"))
                                .setName(WosaiMapUtils.getString(r, "name"))
                                .setPhotoNum(WosaiMapUtils.getInteger(r, "photoNum"))
                                .setFront(WosaiMapUtils.getString(r, "front"))
                                .setBack(WosaiMapUtils.getString(r, "back"))
                                .setFrontImg(WosaiMapUtils.getString(r, "frontImg"))
                                .setBackImg(WosaiMapUtils.getString(r, "backImg"))));
            });
        } catch (ExecutionException e) {
            return new HashMap<>();
        }

    }

    public Map<Integer, LicenseDicInfo> queryAllLicenseDic() {
        try {
            return (Map<Integer, LicenseDicInfo>) cache.get("all_license", () -> {
                List<BusinessLicenseDicChangeVo> businessLicenseDicChangeVos = businessLicenseDicV2Service.allBusinessLicenseDic(null);
                return businessLicenseDicChangeVos.stream().collect(Collectors.toMap(BusinessLicenseDicChangeVo::getId,
                        r -> new LicenseDicInfo()
                                .setType(r.getId())
                                .setName(r.getName())
                                .setLegalPersonTypes(r.getLegal_person_types())
                                .setTypeCodes(r.getType_codes().stream().map(t -> new LicenseTypeCode().setType(t.getType()).setCode(t.getCode())).collect(Collectors.toList()))
                ));
            });
        } catch (ExecutionException e) {
            return new HashMap<>();
        }
    }

    public Map<Integer, TradeLicenseDicInfo> queryAllTradeLicenseDic() {
        try {
            return (Map<Integer, TradeLicenseDicInfo>) cache.get("all_trade_license", () -> {
                List<LicenseDic> licenseDics = licenseDicService.allLicenseDic(null);
                return licenseDics.stream().collect(Collectors.toMap(r -> r.getId().intValue(),
                        r -> new TradeLicenseDicInfo()
                                .setType(r.getId().intValue())
                                .setName(r.getName())
                ));
            });
        } catch (ExecutionException e) {
            return new HashMap<>();
        }
    }
}
