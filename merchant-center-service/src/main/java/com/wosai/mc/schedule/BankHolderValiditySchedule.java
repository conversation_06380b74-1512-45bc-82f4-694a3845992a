package com.wosai.mc.schedule;

import com.alibaba.fastjson.JSON;
import com.shouqianba.workflow.bean.dto.AuditPreStartDto;
import com.shouqianba.workflow.service.AuditService;
import com.wosai.aop.gateway.model.ClientSideSmsSendModel;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.biz.CommonBiz;
import com.wosai.mc.biz.RedisLock;
import com.wosai.mc.entity.BankHolderIdValidity;
import com.wosai.mc.mapper.BankHolderIdValidityMapper;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
@Slf4j
@Component
public class BankHolderValiditySchedule {

    @Autowired
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private UcUserAccountService ucUserAccountService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private ClientSideSmsService clientSideSmsService;

    @Autowired
    private AuditService spWorkflowService;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private BankHolderIdValidityMapper validityMapper;

    @Value("${merchant-center.dev_code}")
    private String devCode;

    @Value("${holder.near_expire.notice}")
    private String nearExpireNoticeTemplate;

    @Value("${holder.near_expire.message}")
    private String nearExpireMessageTemplate;

    @Value("${holder.already_expire.notice}")
    private String alreadyExpireNoticeTemplate;

    @Value("${holder.already_expire.message}")
    private String alreadyExpireMessageTemplate;

    @Value("${holder.audit_template_id}")
    private Long auditTemplateId;


    /**
     * 扫描银行卡持有人证件过期的商户
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void bankHolderValidity() {
        String key = "bank-holder-validity";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, 300)) {
            return;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate today = LocalDate.now();
            String pt = today.format(formatter);
            List<BankHolderIdValidity> bankHolderIdValidities = validityMapper.selectByPt(pt);
            if (WosaiCollectionUtils.isEmpty(bankHolderIdValidities)) {
                log.info("bank-holder-validity {} 为空", pt);
                return;
            }
            for (BankHolderIdValidity idValidity : bankHolderIdValidities) {
                doBankHolderValidity(idValidity, today);
            }
        } catch (Exception e) {
            log.error("处理银行卡持有人证件过期异常", e);
        } finally {
            redisLock.unlock(key, value);
        }

    }

    public void doBankHolderValidity(BankHolderIdValidity bankHolderIdValidity, LocalDate today) {
        try {
            Map<String, Object> map = commonBiz.baseJudge(bankHolderIdValidity.getMerchant_id());
            if (map == null) {
                return;
            }
            UcMerchantUserSimpleInfo superAdminSimpleInfo = (UcMerchantUserSimpleInfo) map.get("merchantUserInfo");
            MerchantBankAccount merchantBankAccount = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(bankHolderIdValidity.getMerchant_id());
            MerchantBusinessLicense license = bankBusinessLicenseService.getBusinessLicenseByMerchantId(bankHolderIdValidity.getMerchant_id());
            // 这里判断一下，因为数仓有可能不准，有些非小微商户的法人和结算人实际上一样,但是因为有一个没加密导致数仓查出来是不一样的。
            if (license != null && license.getType() > 0 && Objects.equals(merchantBankAccount.getIdentity(), license.getLegal_person_id_number())) {
                log.error("非小微个人结算账户:{}", bankHolderIdValidity.getMerchant_id());
                return;
            }
            String idValidity = merchantBankAccount.getId_validity();
            int day = Integer.parseInt(idValidity.split("-")[1]);
            LocalDate statisticsDay = LocalDate.of(day / 10000, day % 10000 / 100, day % 100);
            long result = ChronoUnit.DAYS.between(today, statisticsDay);
            String merchantSn = BeanUtil.getPropString(map, "merchant.sn");
            if (result == 60 || result == 7) {
                String url = createSettlementUpdateAuditUrl(merchantBankAccount, merchantSn);
                sendNoticeAndMessage(superAdminSimpleInfo, nearExpireNoticeTemplate, nearExpireMessageTemplate, url, result);
                log.info("bank-holder-validity 临近过期发送成功,商户id:{}, 过期时间:{}", bankHolderIdValidity.getMerchant_id(), result);
            } else  if (result == 0) {
                String url = createSettlementUpdateAuditUrl(merchantBankAccount, merchantSn);
                sendNoticeAndMessage(superAdminSimpleInfo, alreadyExpireNoticeTemplate, alreadyExpireMessageTemplate, url, 0);
                log.info("bank-holder-validity 过期发送成功,商户id:{}, 过期时间:{}", bankHolderIdValidity.getMerchant_id(), result);
            } else {
                log.error("该银行卡持有人证件过期时间不符合,merchant_id:{}", bankHolderIdValidity.getMerchant_id());
            }
        } catch (Exception e) {
            log.error("处理银行卡持有人证件过期异常,merchant_id:{}", bankHolderIdValidity.getMerchant_id(), e);
        }
    }

    private void sendNoticeAndMessage(UcMerchantUserSimpleInfo superAdminSimpleInfo, String noticeTemplate, String messageTemplate, String url, long day) {
        MerchantUserNoticeSendModel sendModel = null;
        try {
            sendModel = new MerchantUserNoticeSendModel();
            sendModel.setDevCode(devCode);
            sendModel.setTemplateCode(noticeTemplate);
            sendModel.setMerchantUserId(superAdminSimpleInfo.getMerchant_user_id());
            sendModel.setTimestamp(System.currentTimeMillis());
            // 跳转地址为审批填写页面
            sendModel.setData(CollectionUtil.hashMap("text1", getSendText1ByUrl(url)));
            clientSideNoticeService.sendToMerchantUser(sendModel);
        } catch (Exception e) {
            log.error("发送银行卡持有人证件临近过期或过期通知异常:{}", JSON.toJSONString(sendModel), e);
        }

        ClientSideSmsSendModel smsSendModel = new ClientSideSmsSendModel();
        try {
            UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(superAdminSimpleInfo.getUc_user_id());
            if (ucUserInfo == null) {
                log.error("商户老板对应的uc_user不存在,merchant_id:{}", superAdminSimpleInfo.getMerchant_id());
                return;
            }
            smsSendModel.setDevCode(devCode);
            smsSendModel.setTemplateCode(messageTemplate);
            smsSendModel.setAccountId(ucUserInfo.getUc_user_id());
            smsSendModel.setAccountType(1);
            smsSendModel.setTerminalCode("TERMINALAPP");
            smsSendModel.setTimestamp(System.currentTimeMillis());
            if (day != 0) {
                smsSendModel.setData(CollectionUtil.hashMap("key1", day));
                clientSideSmsService.send(smsSendModel);
            } else {
                clientSideSmsService.send(smsSendModel);
            }
        } catch (Exception e) {
            log.error("发送银行卡持有人证件临近过期或过期短信异常:{}", JSON.toJSONString(smsSendModel));
        }
    }

    private String createSettlementUpdateAuditUrl(MerchantBankAccount merchantBankAccount, String merchantSn) {
        String merchantId = merchantBankAccount.getMerchant_id();
        try {
            return spWorkflowService.preStartAuditForInner("app",
                    AuditPreStartDto.builder().auditTemplateId(auditTemplateId)
                            .platform("app")
                            .operator(merchantId)
                            .extra(CollectionUtil.hashMap(
                                    "merchant_sn", merchantSn,
                                    "holder", merchantBankAccount.getHolder(),
                                    "identity", merchantBankAccount.getIdentity(),
                                    "id_type", merchantBankAccount.getId_type()
                            )).build());
        } catch (Exception e) {
            log.error("生成审批连接失败,商户id:{}", merchantId, e);
        }
        return "";
    }

    private static final Pattern URL_PATTERN = Pattern.compile("https://.+?/");

    /**
     * aop 不能配置整个路径
     * 从https://sp-appredirect-api.wosai.cn/api/pre...中获取api/pre等
     * @param url 统一资源定位符
     * @return 路径
     */
    private String getSendText1ByUrl(String url) {
        if (WosaiStringUtils.isEmpty(url)) {
            return url;
        }
        Matcher matcher = URL_PATTERN.matcher(url);
        return matcher.replaceAll("");
    }
}
