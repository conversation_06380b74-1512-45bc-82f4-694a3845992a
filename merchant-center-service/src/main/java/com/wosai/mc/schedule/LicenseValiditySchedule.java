package com.wosai.mc.schedule;

import com.alibaba.fastjson.JSON;
import com.wosai.aop.gateway.model.ClientSideSmsSendModel;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.biz.CommonBiz;
import com.wosai.mc.biz.RedisLock;
import com.wosai.mc.entity.LicenseLegalPersonIdValidity;
import com.wosai.mc.entity.LicenseValidity;
import com.wosai.mc.mapper.LicenseLegalPersonIdValidityMapper;
import com.wosai.mc.mapper.LicenseValidityMapper;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
@Slf4j
@Component
public class LicenseValiditySchedule {

    @Autowired
    private MerchantBusinessLicenseService licenseService;

    @Autowired
    private UcUserAccountService ucUserAccountService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private ClientSideSmsService clientSideSmsService;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private CommonBiz commonBiz;

    @Autowired
    private LicenseValidityMapper licenseValidityMapper;

    @Autowired
    private LicenseLegalPersonIdValidityMapper idValidityMapper;

    @Value("${merchant-center.dev_code}")
    private String devCode;

    @Value("${license.near_expire.notice}")
    private String nearExpireNoticeTemplate;

    @Value("${license.near_expire.message}")
    private String nearExpireMessageTemplate;

    @Value("${license.already_expire.notice}")
    private String alreadyExpireNoticeTemplate;

    @Value("${license.already_expire.message}")
    private String alreadyExpireMessageTemplate;

    private static final String LICENSE_EXPIRE = "营业证照";
    /**
     * 个体工商户
     */
    private static final String INDIVIDUAL_ID_CARD = "经营者证件";
    /**
     * 其他营业执照类型
     */
    private static final String OTHER_ID_CARD = "法人代表证件";

    /**
     * 扫描营业执照过期的商户
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void businessLicenseValidity() {
        String key = "license-validity";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, 60)) {
            return;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate today = LocalDate.now();
            String pt = today.format(formatter);
            List<LicenseValidity> licenseValidities = licenseValidityMapper.selectByPt(pt);
            if (WosaiCollectionUtils.isEmpty(licenseValidities)) {
                log.info("license-validity {} 为空", pt);
                return;
            }
            for (LicenseValidity licenseValidity : licenseValidities) {
                doLicenseValidityBusiness(licenseValidity, today);
            }
        } catch (Exception e) {
            log.error("处理营业执照过期商户异常", e);
        } finally {
            redisLock.unlock(key, value);
        }

    }

    public void doLicenseValidityBusiness(LicenseValidity licenseValidity, LocalDate today) {
        try {
            Map<String, Object> map = commonBiz.baseJudge(licenseValidity.getMerchant_id());
            if (map == null) {
                return;
            }
            UcMerchantUserSimpleInfo superAdminSimpleInfo = (UcMerchantUserSimpleInfo) map.get("merchantUserInfo");
            Map<String, Object> licenseInfo = licenseService.getBusinessLicenseByMerchantId(licenseValidity.getMerchant_id());
            String validity = BeanUtil.getPropString(licenseInfo, MerchantBusinessLicence.VALIDITY);
            int day = Integer.parseInt(validity.split("-")[1]);
            LocalDate statisticsDay = LocalDate.of(day / 10000, day % 10000 / 100, day % 100);
            long result = ChronoUnit.DAYS.between(today, statisticsDay);
            if (result == 60 || result == 7) {
                sendNoticeAndMessage(superAdminSimpleInfo, nearExpireNoticeTemplate, nearExpireMessageTemplate, LICENSE_EXPIRE, result);
                log.info("license-validity 临近过期发送成功,商户id:{}, 过期时间:{}", licenseValidity.getMerchant_id(), result);
            } else  if (result == 0) {
                sendNoticeAndMessage(superAdminSimpleInfo, alreadyExpireNoticeTemplate, alreadyExpireMessageTemplate, LICENSE_EXPIRE, result);
                log.info("license-validity 过期发送成功,商户id:{}, 过期时间:{}", licenseValidity.getMerchant_id(), result);
            } else {
                log.error("该营业执照过期时间不符合,merchant_id:{}", licenseValidity.getMerchant_id());
            }
        } catch (Exception e) {
            log.error("处理营业执照过期异常,merchant_id:{}", licenseValidity.getMerchant_id(), e);
        }
    }

    /**
     * 扫描营业执照法人证件信息过期的商户
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void legalPersonIdValidity() {
        String key = "legal-person-id-validity";
        String value = UUID.randomUUID().toString();
        if (!redisLock.lock(key, value, 60)) {
            return;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate today = LocalDate.now();
            String pt = today.format(formatter);
            List<LicenseLegalPersonIdValidity> legalPersonIdValidities = idValidityMapper.selectByPt(pt);
            if (WosaiCollectionUtils.isEmpty(legalPersonIdValidities)) {
                log.info("legal-person-id-validity {} 为空", pt);
                return;
            }
            for (LicenseLegalPersonIdValidity idValidity : legalPersonIdValidities) {
                doLegalPersonIdValidity(idValidity, today);
            }
        } catch (Exception e) {
            log.error("处理营业执照法人证件过期异常", e);
        } finally {
            redisLock.unlock(key, value);
        }

    }

    public void doLegalPersonIdValidity(LicenseLegalPersonIdValidity legalPersonIdValidity, LocalDate today) {
        try {
            Map<String, Object> map = commonBiz.baseJudge(legalPersonIdValidity.getMerchant_id());
            if (map == null) {
                return;
            }
            UcMerchantUserSimpleInfo superAdminSimpleInfo = (UcMerchantUserSimpleInfo) map.get("merchantUserInfo");
            Map<String, Object> licenseInfo = licenseService.getBusinessLicenseByMerchantId(legalPersonIdValidity.getMerchant_id());
            // 通知或短信中嵌入的文字
            String message = BeanUtil.getPropInt(licenseInfo, MerchantBusinessLicence.TYPE) == 1 ? INDIVIDUAL_ID_CARD : OTHER_ID_CARD;
            String idValidity = BeanUtil.getPropString(licenseInfo, MerchantBusinessLicence.ID_VALIDITY);
            int day = Integer.parseInt(idValidity.split("-")[1]);
            LocalDate statisticsDay = LocalDate.of(day / 10000, day % 10000 / 100, day % 100);
            long result = ChronoUnit.DAYS.between(today, statisticsDay);
            if (result == 60 || result == 7) {
                sendNoticeAndMessage(superAdminSimpleInfo, nearExpireNoticeTemplate, nearExpireMessageTemplate, message, result);
                log.info("legal-person-id-validity 临近过期发送成功,商户id:{}, 过期时间:{}", legalPersonIdValidity.getMerchant_id(), result);
            } else  if (result == 0) {
                sendNoticeAndMessage(superAdminSimpleInfo, alreadyExpireNoticeTemplate, alreadyExpireMessageTemplate, message, result);
                log.info("legal-person-id-validity 过期发送成功,商户id:{}, 过期时间:{}", legalPersonIdValidity.getMerchant_id(), result);
            } else {
                log.error("该营业执照法人证件过期时间不符合,merchant_id:{}", legalPersonIdValidity.getMerchant_id());
            }
        } catch (Exception e) {
            log.error("处理营业执照法人证件过期异常,merchant_id:{}", legalPersonIdValidity.getMerchant_id(), e);
        }
    }

    private void sendNoticeAndMessage(UcMerchantUserSimpleInfo superAdminSimpleInfo, String noticeTemplate, String messageTemplate, String message, long day) {
        MerchantUserNoticeSendModel sendModel = null;
        try {
            sendModel = new MerchantUserNoticeSendModel();
            sendModel.setDevCode(devCode);
            sendModel.setTemplateCode(noticeTemplate);
            sendModel.setMerchantUserId(superAdminSimpleInfo.getMerchant_user_id());
            sendModel.setTimestamp(System.currentTimeMillis());
            sendModel.setData(CollectionUtil.hashMap("content1", message));
            clientSideNoticeService.sendToMerchantUser(sendModel);
        } catch (Exception e) {
            log.error("发送证件临近过期或过期通知异常:{}", JSON.toJSONString(sendModel), e);
        }

        ClientSideSmsSendModel smsSendModel = new ClientSideSmsSendModel();
        try {
            UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(superAdminSimpleInfo.getUc_user_id());
            if (ucUserInfo == null) {
                log.error("商户老板对应的uc_user不存在,merchant_id:{}", superAdminSimpleInfo.getMerchant_id());
                return;
            }
            smsSendModel.setDevCode(devCode);
            smsSendModel.setTemplateCode(messageTemplate);
            smsSendModel.setAccountId(ucUserInfo.getUc_user_id());
            smsSendModel.setAccountType(1);
            smsSendModel.setTerminalCode("TERMINALAPP");
            smsSendModel.setTimestamp(System.currentTimeMillis());
            if (day != 0) {
                smsSendModel.setData(CollectionUtil.hashMap("key1", message, "key2", day));
                clientSideSmsService.send(smsSendModel);
            } else {
                smsSendModel.setData(CollectionUtil.hashMap("key1", message));
                clientSideSmsService.send(smsSendModel);
            }
        } catch (Exception e) {
            log.error("发送证件临近过期或过期短信异常:{}", JSON.toJSONString(smsSendModel));
        }
    }

}
