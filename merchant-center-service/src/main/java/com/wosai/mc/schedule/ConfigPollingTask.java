package com.wosai.mc.schedule;

import com.wosai.mc.biz.DataProcessingTask;
import com.wosai.mc.biz.RedisLock;
import com.wosai.mc.utils.ApolloParamsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class ConfigPollingTask {
    @Autowired
    private ApolloParamsConfig apolloParamsConfig;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private DataProcessingTask dataProcessingTask;
    private Thread taskThread;

    public static final String APO_KEY = "merge_store_schedule";
    public static final String APO_KEY_WAIT_TIME = "delay";
    public static final String APO_KEY_SWITCH = "is_open";


    private static final String LOCK_KEY = "data-processing-lock";
    private static final String LOCK_VALUE = "data-processing-lock-value";
    private static final String LOCK_RUNNING_KEY = "merchant_center_merge_store_schedule_running";
    private static final String LOCK_RUNNING_VALUE = "running";
    private static final long LOCK_TIMEOUT = 300; // 5 minutes


//    @Scheduled(fixedRate = 5000) // 每隔5秒执行一次
    public void pollConfig() {

        Map config = apolloParamsConfig.getMap(APO_KEY, "{}");
        //目前配置的开关标识
        boolean isOpen = MapUtils.getBooleanValue(config, APO_KEY_SWITCH, false);

        //如果需要开始,且已有任务在执行,则返回
        if (isOpen && redisLock.hasKey(LOCK_RUNNING_KEY)) {
            return;
        }

        if (isOpen && redisLock.lock(LOCK_KEY, LOCK_VALUE, LOCK_TIMEOUT)) {
            try {

                if (taskThread == null || !taskThread.isAlive()) {
                    taskThread = new Thread(dataProcessingTask);
                    taskThread.start();
                    redisLock.lock(LOCK_RUNNING_KEY, LOCK_RUNNING_VALUE, Integer.MAX_VALUE);
                }

            } catch (Exception e) {
                log.error("开始任务失败:", e);
            } finally {
                redisLock.unlock(LOCK_KEY, LOCK_VALUE);
            }
        }

        //关闭任务
        if (!isOpen) {
            if (taskThread != null && taskThread.isAlive()) {
                taskThread.interrupt();
                redisLock.unlock(LOCK_RUNNING_KEY, LOCK_RUNNING_VALUE);
            } else {
                if (redisLock.hasKey(LOCK_RUNNING_KEY)) {
                    redisLock.unlock(LOCK_RUNNING_KEY, LOCK_RUNNING_VALUE);
                }
            }
        }
    }
}
