package com.wosai.mc.config;

import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.SerializationException;

/**
 * 解决反序列化失败，整个消费者卡住的问题
 *
 * <AUTHOR>
 * @date 2019-08-23
 */
@Slf4j
public class MyKafkaAvroDeserializer extends KafkaAvroDeserializer {

    @Override
    public Object deserialize(String s, byte[] bytes) {
        try {
            return this.deserialize(bytes);
        } catch (SerializationException e) {
            log.error("error deserialize skip {}", e.getMessage());
            return null;
        }
    }
}
