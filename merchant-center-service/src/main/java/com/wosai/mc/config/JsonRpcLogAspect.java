package com.wosai.mc.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.wosai.common.utils.WosaiStringUtils;
import net.logstash.logback.marker.Markers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-03-11
 */
@Component
public class JsonRpcLogAspect implements InvocationListener {

    private static final Logger logger = LoggerFactory.getLogger(JsonRpcLogAspect.class);

    @Override
    public void willInvoke(Method method, List<JsonNode> arguments) {
    }

    @Override
    public void didInvoke(Method method, List<JsonNode> arguments, Object result, Throwable t, long duration) {
        try {
            Map<String, Object> toAppendEntriesMap = new HashMap<>();
            toAppendEntriesMap.put("method", method.getName());
            toAppendEntriesMap.put("request", arguments);
            toAppendEntriesMap.put("response", result);
            toAppendEntriesMap.put("duration", duration);
            if (t != null) {
                toAppendEntriesMap.put("error", WosaiStringUtils.defaultIfEmpty(t.getMessage(), t.getCause().getMessage()));
                logger.error(Markers.appendEntries(toAppendEntriesMap), "");
                logger.error("method {} request {}", method.getName(), arguments, t);
            } else {
                logger.info(Markers.appendEntries(toAppendEntriesMap), "");
            }
        } catch (Exception e) {
        }
    }
}
