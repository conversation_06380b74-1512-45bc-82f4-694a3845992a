package com.wosai.mc.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Role;

/**
 * <AUTHOR>
 * @date 2020-03-27
 */
@Configuration
public class JsonRpcExportConfig {

    @Bean
    @Role(BeanDefinition.ROLE_INFRASTRUCTURE)
    public AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter(
            ErrorResolver errorResolver,
            ObjectMapper objectMapper,
            InvocationListener invocationListener
    ) {
        AutoJsonRpcServiceImplExporter exporter =
                new AutoJsonRpcServiceImplExporter();
        exporter.setErrorResolver(errorResolver);
        exporter.setObjectMapper(objectMapper);
        exporter.setInvocationListener(invocationListener);
        return exporter;
    }
}
