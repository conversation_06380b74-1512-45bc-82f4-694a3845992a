package com.wosai.mc.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.googlecode.jsonrpc4j.ErrorData;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.wosai.common.exception.*;
import com.wosai.mc.config.exception.McException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.ConnectException;
import java.net.UnknownHostException;
import java.util.List;
import java.util.StringJoiner;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @date 2020-03-11
 */
@Component
public class JsonRpcErrorResolver implements ErrorResolver {
    @Override
    public JsonError resolveError(Throwable throwable, Method method, List<JsonNode> list) {
        //valid错误
        String throwableName = throwable.getClass().getName();
        ErrorData defaultErrorData = new ErrorData(throwableName, throwable.getMessage());
        CommonException commonException = null;
        try {
            if (throwable instanceof ConstraintViolationException) {
                ConstraintViolationException e = (ConstraintViolationException) throwable;
                StringJoiner message = new StringJoiner(";");
                for (ConstraintViolation<?> constraintViolation : e.getConstraintViolations()) {
                    message.add(constraintViolation.getMessage());
                }
                return new JsonError(CommonException.CODE_INVALID_PARAMETER, message.toString(), new ErrorData(CommonInvalidParameterException.class.getName(), message.toString()));
            } else if (throwable instanceof InvalidFormatException) {
                return new JsonError(CommonException.CODE_INVALID_PARAMETER, CommonException.getCodeDesc(CommonException.CODE_INVALID_PARAMETER), defaultErrorData);
            } else if (throwable instanceof CannotGetJdbcConnectionException) {
                commonException = new CommonDataAccessException(CommonException.getCodeDesc(CommonException.CODE_CANNOT_GET_JDBC_CONNECTION));
            } else if (throwable instanceof DuplicateKeyException) {
                commonException = new CommonDatabaseDuplicateKeyException(CommonException.getCodeDesc(CommonException.CODE_DATABASE_DUPLICATE_KEY));
            } else if (throwable instanceof DataAccessException) {
                commonException = new CommonDataAccessException(CommonException.getCodeDesc(CommonException.CODE_DATA_ACCESS_EXCEPTION));
            } else if (throwable instanceof UnknownHostException) {
                commonException = new CommonUnknownHostException(CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_HOST_EXCEPTION));
            } else if (throwable instanceof ConnectException) {
                commonException = new CommonNetConnectErrorException(CommonException.getCodeDesc(CommonException.CODE_NET_CONNECT_ERROR));
            } else if (throwable instanceof IOException) {
                commonException = new CommonIOException(CommonException.getCodeDesc(CommonException.CODE_IO_EXCEPTION));
            } else if (throwable instanceof NullPointerException) {
                commonException = new CommonNullPointerException(CommonException.getCodeDesc(CommonException.CODE_NULL_POINTER_EXCEPTION));
            } else if (throwable instanceof CommonException) {
                commonException = (CommonException) throwable;
            } else if (throwable instanceof McException) {
                McException mcException = (McException) throwable;
                return new JsonError(Integer.parseInt(mcException.getErrorCode().getCode()),mcException.getMessage(), defaultErrorData);
            } else {
                commonException = new CommonUnknownException(CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_ERROR));
            }
        } catch (Exception e) {
        }
        if (commonException != null) {
            return new JsonError(commonException.getCode(), commonException.getMessage(), defaultErrorData);
        }
        return new JsonError(CommonException.CODE_UNKNOWN_ERROR, CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_ERROR), defaultErrorData);
    }
}
