package com.wosai.mc.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.service.MerchantContractService;
import com.shouqianba.workflow.service.AuditService;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.backend.api.service.CommonConfig;
import com.wosai.app.service.ManagerPasswordService;
import com.wosai.app.service.MultiMerchantService;
import com.wosai.app.service.VideoService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.core.crypto.service.CryptoService;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.mc.remote.*;
import com.wosai.mc.service.rpc.BankPayBusinessConfigurationService;
import com.wosai.mc.service.rpc.JobBusinessLicenceTaskService;
import com.wosai.mc.utils.JsonRpcUtil;
import com.wosai.sales.merchant.business.service.common.CommonAppConfigService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.sales.service.StorePoiService;
import com.wosai.sales.service.SubmitPoiRecordService;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.service.WechatAuthService;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.tools.service.BusinessCheckService;
import com.wosai.upay.bank.info.api.service.*;
import com.wosai.upay.bank.service.BankAccountService;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.clearance.service.ClearanceService;
import com.wosai.upay.core.service.RMQService;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.service.*;
import com.wosai.upay.job.service.task.BusinessLicenceTaskService;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import com.wosai.upay.merchant.audit.api.service.*;
import facade.ICustomerRelationFacade;
import facade.ICustomerRelationValidateFacade;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JsonRpcConfig {

    @Value("${jsonrpc.core-business}")
    private String coreBusiness;
    @Value("${jsonrpc.merchant-user}")
    private String merchantUser;
    @Value("${jsonrpc.merchant-audit}")
    private String merchantAudit;
    @Value("${jsonrpc.app-backend}")
    private String appBackendUrl;
    @Value("${jsonrpc.sales-system-poi}")
    private String salesSystemPoiUrl;
    @Value("${jsonrpc.video-service}")
    private String videoServiceUrl;
    @Value("${jsonrpc.crow-service}")
    private String crowServiceUrl;
    @Value("${jsonrpc.bank-info}")
    private String bankInfoServiceUrl;
    @Value("${jsonrpc.merchant-bank}")
    private String merchantBankUrl;
    @Value("${jsonrpc.aop-gateway}")
    private String aopGateway;
    @Value("${jsonrpc.sp-workflow-service}")
    private String spWorkflowService;
    @Value("${jsonrpc.merchant-contract-job}")
    private String merchantContractJob;
    @Value("${jsonrpc.crm-customer-relation}")
    private String crmCustomerRelation;
    @Value("${jsonrpc.merchant-contract-activity}")
    private String contractActivity;
    @Value("${jsonrpc.business-logstash}")
    private String businessLogstash;
    @Value("${jsonrpc.core-crypto}")
    private String cryptoService;
    @Value("${jsonrpc.merchant-business-open}")
    private String merchantBusinessOpen;
    @Value("${jsonrpc.shouqianba-tools-service}")
    private String shouqianbaToolsService;
    @Value("${jsonrpc.clearance-service}")
    private String clearanceServiceUrl;

    @Value("${jsonrpc.merchant-contract-access}")
    private String merchantContractAccessServiceUrl;

    @Value("${jsonrpc.trade-manage-service}")
    private String tradeManageServiceUrl;
    @Value("${jsonrpc.merchant-api}")
    private String merchantApiUrl;
    @Value("${jsonrpc.withdraw-service}")
    private String withdrawServiceUrl;

    @Bean
    public JsonProxyFactoryBean clearanceService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(clearanceServiceUrl + "/rpc/clearance", ClearanceService.class);
    }

    @Bean
    public JsonProxyFactoryBean snGenerator() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/sn", SnGenerator.class);
    }

    @Bean
    public JsonProxyFactoryBean storeBusinessLicenseService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/store_business_license", StoreBusinessLicenseService.class);
    }

    @Bean
    public JsonProxyFactoryBean licenseService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/license", LicenseService.class);
    }

    @Bean
    public JsonProxyFactoryBean rmq() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/rmq", RMQService.class);

    }


    @Bean
    public JsonProxyFactoryBean businssCommonService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/common", BusinssCommonService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/merchant", com.wosai.upay.core.service.MerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeConfigService() {
        JsonProxyFactoryBean bean = new JsonProxyFactoryBean();
        bean.setServiceInterface(TradeConfigService.class);
        bean.setServiceUrl(coreBusiness + "/rpc/tradeConfig");
        return bean;
    }

    @Bean
    public JsonProxyFactoryBean storeService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/store", com.wosai.upay.core.service.StoreService.class);

    }

    @Bean
    public JsonProxyFactoryBean merchantBusinessLicenseService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/merchant_business_license", com.wosai.upay.core.service.MerchantBusinessLicenseService.class);

    }

    @Bean
    public JsonProxyFactoryBean storeExtService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/storeExt", com.wosai.upay.core.service.StoreExtService.class);

    }

    @Bean
    public JsonProxyFactoryBean photoInfoService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/photoInfo", com.wosai.upay.core.service.PhotoInfoService.class);

    }

    @Bean
    public JsonProxyFactoryBean mcPreService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/mc_pre", com.wosai.upay.core.service.McPreService.class);

    }

    @Bean
    public JsonProxyFactoryBean merchantUserServiceV2() {

        return JsonRpcUtil.getJsonProxyFactoryBean(merchantUser + "/rpc/merchantuserV2", MerchantUserServiceV2.class);

    }

    @Bean
    public JsonProxyFactoryBean managerPasswordService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantUser + "/rpc/managerPassword", ManagerPasswordService.class);
    }

    @Bean
    public JsonProxyFactoryBean multiMerchantService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantUser + "/rpc/multiMerchant", MultiMerchantService.class);

    }

    @Bean
    public JsonProxyFactoryBean ucUserAccountService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantUser + "/rpc/ucUser", UcUserAccountService.class);

    }

    @Bean
    public JsonProxyFactoryBean merchantAudit() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantAudit + "/rpc/merchantAudit", MerchantAuditService.class);

    }

    @Bean
    public JsonProxyFactoryBean merchantBusinessLicenseAuditService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantAudit + "/rpc/businessLicenseAudit", MerchantBusinessLicenseAuditService.class);

    }

    @Bean
    public JsonProxyFactoryBean indirectPayAuditService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantAudit + "/rpc/indirectPayAudit", IndirectPayAuditService.class);

    }

    @Bean
    public JsonProxyFactoryBean merchantSpeechCraftAuditService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantAudit + "/rpc/merchantSpeechCraftAudit", MerchantSpeechCraftAuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean takeAwayAuditService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantAudit + "/rpc/takeAwayAudit", TakeAwayAuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean iMerchantService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(appBackendUrl + "/rpc/merchantconfig", IMerchantService.class);

    }

    @Bean
    public JsonProxyFactoryBean commonConfig() {
        return JsonRpcUtil.getJsonProxyFactoryBean(appBackendUrl + "/rpc/common_config", CommonConfig.class);

    }

    @Bean
    public JsonProxyFactoryBean storePoiService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(salesSystemPoiUrl + "/rpc/storePoi", StorePoiService.class);

    }

    @Bean
    public JsonProxyFactoryBean videoService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(videoServiceUrl + "/rpc/video", VideoService.class);

    }

    @Bean
    public JsonProxyFactoryBean districtsServiceV2() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/districtsv2", DistrictsServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean appBankInfoService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/v4/bankinfo", AppBankInfoService.class);

    }

    @Bean
    public JsonProxyFactoryBean bankNamesService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/bankNames", BankNamesService.class);
    }

    @Bean
    public JsonProxyFactoryBean tagIngestService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(crowServiceUrl + "/rpc/tag_ingests", TagIngestService.class);

    }

    @Bean
    public JsonProxyFactoryBean bankBusinessLicenseService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantBankUrl + "/rpc/bank_business_license", BankBusinessLicenseService.class);

    }

    @Bean
    public JsonProxyFactoryBean bankAccountService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantBankUrl + "/rpc/bankAccount", BankAccountService.class);

    }

    @Bean
    public JsonProxyFactoryBean licenseUpdateChangeCardService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantBankUrl + "/rpc/license/change-card", LicenseUpdateChangeCardService.class, 5000, 5000);
    }

    @Bean
    public JsonProxyFactoryBean payBusinessConfigurationService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/pay_business_configuration", PayBusinessConfigurationService.class);

    }

    @Bean
    public JsonProxyFactoryBean bankPayBusinessConfigurationService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/pay_business_configuration", BankPayBusinessConfigurationService.class);

    }

    @Bean
    public JsonProxyFactoryBean industryV2Service() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/industry_v2", IndustryV2Service.class);

    }

    @Bean
    public JsonProxyFactoryBean businessLicenseDicV2Service() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/business_license_dic_v2", BusinessLicenseDicV2Service.class);
    }

    @Bean
    public JsonProxyFactoryBean licenseDicService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/licenses", LicenseDicService.class);

    }

    @Bean
    public JsonProxyFactoryBean clientSideNoticeService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(aopGateway + "/rpc/clientSide/notice", ClientSideNoticeService.class);
    }

    @Bean
    public JsonProxyFactoryBean clientSideSmsService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(aopGateway + "/rpc/clientSide/sms", ClientSideSmsService.class);
    }

    @Bean
    public JsonProxyFactoryBean auditService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(spWorkflowService + "/rpc/audit", AuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean contractStatusService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/contractstatus", ContractStatusService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantProviderParamsService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/merchantProviderParams", MerchantProviderParamsService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantUpgradeService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/merchantUpgrade", MerchantUpgradeService.class);
    }

    @Bean
    public JsonProxyFactoryBean acquirerService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/acquirer", AcquirerService.class);
    }

    @Bean
    public JsonProxyFactoryBean accessAcquirerService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractAccessServiceUrl + "/rpc/acquirer", com.shouqianba.service.AcquirerService.class);
    }

    @Bean
    public JsonProxyFactoryBean providerTerminalSerivce() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/terminal", ProviderTerminalSerivce.class);
    }

    @Bean
    public JsonProxyFactoryBean contractWeixinService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/contractWeixin", ContractWeixinService.class);
    }

    @Bean
    public JsonProxyFactoryBean customerRelationValidateFacade() {
        return JsonRpcUtil.getJsonProxyFactoryBean(crmCustomerRelation + "/rpc/relationvalidate", ICustomerRelationValidateFacade.class);
    }

    @Bean
    public JsonProxyFactoryBean wechatAuthService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(contractActivity + "/rpc/wechatAuth", WechatAuthService.class);
    }

    @Bean
    public JsonProxyFactoryBean gaoDeService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(salesSystemPoiUrl + "/rpc/gaoDe", GaoDeService.class);
    }

    @Bean
    public JsonProxyFactoryBean submitPoiRecordService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(salesSystemPoiUrl + "/rpc/submitPoiRecord", SubmitPoiRecordService.class);
    }

    @Bean
    public JsonProxyFactoryBean businessOpLogService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(businessLogstash + "/rpc/businessOpLog", BusinessOpLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean cryptoService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(cryptoService + "/rpc/crypto", CryptoService.class);
    }

    @Bean
    public JsonProxyFactoryBean commonAppInfoService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantBusinessOpen + "/rpc/common/appInfo", CommonAppInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean commonAppConfigService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantBusinessOpen + "/rpc/common/appConfig", CommonAppConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean commonFieldService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantBusinessOpen + "/rpc/common/field", CommonFieldService.class, 5000, 5000);
    }

    @Bean
    public JsonProxyFactoryBean businessCheckService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(shouqianbaToolsService + "/rpc/businessCheck", BusinessCheckService.class);
    }

    @Bean
    public JsonProxyFactoryBean errorCodeManageService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/error-code-manage", ErrorCodeManageService.class);
    }


    @Bean
    public JsonProxyFactoryBean accessMerchantContractService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractAccessServiceUrl + "/rpc/merchant/contract", MerchantContractService.class);
    }

    @Bean
    public JsonProxyFactoryBean accessContractTaskService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractAccessServiceUrl + "/rpc/contract/task", com.shouqianba.service.ContractTaskService.class);
    }

    @Bean
    public JsonProxyFactoryBean t9Service() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/t9", T9Service.class);
    }

    @Bean
    public JsonProxyFactoryBean businessLicenceTaskService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/task/licence", BusinessLicenceTaskService.class);
    }

    @Bean
    public JsonProxyFactoryBean jobBusinessLicenceTaskService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantContractJob + "/rpc/task/licence", JobBusinessLicenceTaskService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeManageMicroUpgradeCheck() {
        return JsonRpcUtil.getJsonProxyFactoryBean(tradeManageServiceUrl + "/rpc/merchantUpdate", TradeManageMicroUpgradeCheck.class, 10000, 5000);
    }

    @Bean
    public JsonProxyFactoryBean merchantBankService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(coreBusiness + "/rpc/merchantBank", MerchantBankService.class);
    }

    @Bean
    public JsonProxyFactoryBean storeRemoteService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(merchantApiUrl + "/rpc/merchant/store", StoreRemoteService.class);
    }

    @Bean
    public JsonProxyFactoryBean withdrawService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(withdrawServiceUrl + "/rpc/withdraw", WithdrawService.class);
    }

    @Bean
    public JsonProxyFactoryBean withdrawConfigService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(withdrawServiceUrl + "/rpc/withdrawConfig", WithdrawConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean customerRelationFacade() {
        return JsonRpcUtil.getJsonProxyFactoryBean(crmCustomerRelation + "/rpc/relation", ICustomerRelationFacade.class);
    }

    @Bean
    public JsonProxyFactoryBean identificationCertificateManagementService() {
        return JsonRpcUtil.getJsonProxyFactoryBean(bankInfoServiceUrl + "/rpc/identification-certificate", IdentificationCertificateManagementService.class);
    }
}
