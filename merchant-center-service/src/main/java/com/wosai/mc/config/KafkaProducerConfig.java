package com.wosai.mc.config;

import io.confluent.kafka.serializers.KafkaAvroSerializer;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/16
 */
@Configuration
public class KafkaProducerConfig {

    @Value("${spring.kafka.old.producer.registry-servers}")
    private String registryServers;

    @Value("${spring.kafka.old.producer.group-id}")
    private String groupId;

    @Value("${spring.kafka.old.producer.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${spring.kafka.ali.producer.registry-servers}")
    private String aliRegistryServers;

    @Value("${spring.kafka.ali.producer.group-id}")
    private String aliGroupId;

    @Value("${spring.kafka.ali.producer.bootstrap-servers}")
    private String aliBootstrapServers;

    @Bean
    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.CLIENT_ID_CONFIG, groupId + "-kafka-old");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryServers);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 524288);
        return props;
    }

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate() {
        return new KafkaTemplate(producerFactory());
    }

    @Bean
    public Map<String, Object> dataCenterProducerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, aliBootstrapServers);
        props.put(ProducerConfig.CLIENT_ID_CONFIG, aliGroupId + "-datacenter");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, aliRegistryServers);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 524288);
        return props;
    }

    @Bean
    public ProducerFactory<String, String> dataCenterProducerFactory() {
        return new DefaultKafkaProducerFactory<>(dataCenterProducerConfigs());
    }

    @Bean
    public KafkaTemplate<String, Object> dataCenterKafkaTemplate() {
        return new KafkaTemplate(dataCenterProducerFactory());
    }

    @Bean
    public Map<String, Object> aliAvroProducerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, aliBootstrapServers);
        props.put(ProducerConfig.CLIENT_ID_CONFIG, aliGroupId + "-aliAvro");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, aliRegistryServers);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 524288);
        return props;
    }

    @Bean
    public ProducerFactory<String, Object> aliAvroProducerFactory() {
        return new DefaultKafkaProducerFactory<>(aliAvroProducerConfigs());
    }

    @Bean
    public KafkaTemplate<String, Object> aliAvroKafkaTemplate() {
        return new KafkaTemplate(aliAvroProducerFactory());
    }
}
