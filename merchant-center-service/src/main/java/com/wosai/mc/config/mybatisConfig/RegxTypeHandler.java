//package com.wosai.mc.config.mybatisConfig;
//
//import org.apache.ibatis.type.BaseTypeHandler;
//import org.apache.ibatis.type.JdbcType;
//import org.springframework.stereotype.Component;
//
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
//@Component
//public class RegxTypeHandler extends BaseTypeHandler<String> {
//
//    private static final String TARGET_COLUMN = "regx";
//
//    @Override
//    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
//        ps.setString(i, parameter);
//    }
//
//    @Override
//    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        if (TARGET_COLUMN.equals(columnName)) {
//            String regx = rs.getString(columnName);
//            return processRegx(regx);
//        }
//        return rs.getString(columnName);
//    }
//
//    @Override
//    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        String columnName = rs.getMetaData().getColumnName(columnIndex);
//        if (TARGET_COLUMN.equals(columnName)) {
//            String regx = rs.getString(columnIndex);
//            return processRegx(regx);
//        }
//        return rs.getString(columnIndex);
//    }
//
//    @Override
//    public String getNullableResult(java.sql.CallableStatement cs, int columnIndex) throws SQLException {
//        String columnName = cs.getMetaData().getColumnName(columnIndex);
//        if (TARGET_COLUMN.equals(columnName)) {
//            String regx = cs.getString(columnIndex);
//            return processRegx(regx);
//        }
//        return cs.getString(columnIndex);
//    }
//
//    private String processRegx(String regx) {
//        if (regx == null) {
//            return null;
//        }
//        return regx.replaceAll("\\\\\\\\", "\\\\");
//    }
//}
