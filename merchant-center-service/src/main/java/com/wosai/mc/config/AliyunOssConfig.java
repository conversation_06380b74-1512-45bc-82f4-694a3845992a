package com.wosai.mc.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.wosai.data.util.StringUtil;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> Date: 2021/9/10 Time: 6:25 下午
 */
@Configuration
@EnableConfigurationProperties({AliyunOssConfig.OssProperties.class})
@Slf4j
public class AliyunOssConfig {

    private final OssProperties ossProperties;

    public AliyunOssConfig(OssProperties ossProperties) {
        this.ossProperties = ossProperties;
    }


    @Bean
    public CredentialsProvider credentialsProvider(Vault vault) {
        try {
            return new DynamicCredentialsProvider(vault);
        } catch (Exception e) {
            log.error("初始化oss失败", e);
            throw new RuntimeException("初始化oss失败", e);
        }
    }

    @Bean
    public OSS ossClient(Vault vault) {
        return new OSSClientBuilder().build(ossProperties.getEndpoint(), credentialsProvider(vault));
    }


    @Data
    @ConfigurationProperties(prefix = "aliyun.oss")
    public static class OssProperties {
        private String endpoint;
        private String groupName;
    }

    @Bean
    public Vault vault(OssProperties ossProperties) throws VaultException {
        if (!StringUtil.empty(ossProperties.getGroupName())) {
            return Vault.load(ossProperties.getGroupName(), null);
        }
        return Vault.autoload();
    }
}
