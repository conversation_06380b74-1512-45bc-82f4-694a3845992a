package com.wosai.mc.biz;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.entity.NingboMchPool;
import com.wosai.mc.mapper.NingboMchPoolMapper;
import com.wosai.upay.core.model.Merchant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/27
 */
@Component
public class NingboBiz {

    private static final List<String> DISTRICT_1 = Arrays.asList("宁海县", "象山县");

    @Autowired
    private NingboMchPoolMapper mapper;

    public NingboMchPool getMappingMch(Map merchant) {
        //  宁海、象山映射台州（天台县、临海市、三门县）,  其他地区映射绍兴（去掉诸暨）
        String district = BeanUtil.getPropString(merchant, Merchant.DISTRICT);
        String mappingCity = DISTRICT_1.contains(district) ? "台州市" : "绍兴市";
        NingboMchPool mch = mapper.selectOneByCity(mappingCity);
        mapper.deleteByPrimaryKey(mch.getId());
        return mch;
    }
}
