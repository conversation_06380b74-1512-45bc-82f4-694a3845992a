package com.wosai.mc.biz;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.apolloBeans.model.BusinessLicenseRuleConfig;
import com.wosai.mc.apolloBeans.model.RuleModel;
import com.wosai.mc.apolloBeans.model.TradeLicenseRuleConfig;
import com.wosai.mc.remote.bankinfo.BankInfoClient;
import com.wosai.mc.remote.bankinfo.model.LicenseDicInfo;
import com.wosai.mc.remote.bankinfo.model.LicenseTypeCode;
import com.wosai.mc.utils.IdentityCardUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/25
 */
@Component
public class FieldValidBiz {

    @Autowired
    private BankInfoClient bankInfoClient;

    @Autowired
    private CommonApolloConfigs commonApolloConfigs;

    public void validBusinessLicenseName(String name) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getName();
        ruleValid(ruleModel.getRule(), name, ruleModel.getMessage());
    }

    public void validBusinessLicenseType(Integer type, String industryId) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getType();
        // 行业是否支持
        List<Integer> licenseTypes = bankInfoClient.queryIndustrySupportLicenseTypes(industryId);
        if (!licenseTypes.contains(type)) {
            throw new CommonInvalidParameterException(ruleModel.getMessage());
        }
    }

    public void validBusinessLicenseNumber(Integer type, String number) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        LicenseDicInfo licenseDicInfo = bankInfoClient.queryAllLicenseDic().get(type);
        List<String> starts = licenseDicInfo.getTypeCodes().stream().filter(r -> r.getType() == 1).map(LicenseTypeCode::getCode).collect(Collectors.toList());
        if (type == 1) {
            RuleModel ruleModel = businessLicense.getNumber().get("person");
            String failMsg = String.format(ruleModel.getMessage(), String.join(",", starts));
            ruleValid(ruleModel.getRule(), number, failMsg);
            if (number.length() == 18 && starts.stream().noneMatch(number::startsWith)) {
                throw new CommonInvalidParameterException(failMsg);
            }
        } else {
            RuleModel ruleModel = businessLicense.getNumber().get("others");
            String failMsg = String.format(ruleModel.getMessage(), String.join(",", starts));
            ruleValid(ruleModel.getRule(), number, failMsg);
            if (starts.stream().noneMatch(number::startsWith)) {
                throw new CommonInvalidParameterException(failMsg);
            }
        }
    }

    public void validBusinessLicenseValidity(String validity) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getValidity();
        ruleValid(ruleModel.getRule(), validity, ruleModel.getMessage());
        boolean check = IdentityCardUtils.checkValidity(validity);
        if (!check) {
            throw new CommonInvalidParameterException(ruleModel.getMessage());
        }
    }

    public void validBusinessLicensePhoto(String photo) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getPhoto();
        ruleValid(ruleModel.getRule(), photo, ruleModel.getMessage());
    }

    public void validBusinessLicenseAddress(String address) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getAddress();
        ruleValid(ruleModel.getRule(), address, ruleModel.getMessage());
    }

    public void validBusinessLicenseLegalPersonIdType(Integer businessLicenseType, Integer legalPersonIdType) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getLegalPersonIdType();
        Map<Integer, LicenseDicInfo> licenseDicInfoMap = bankInfoClient.queryAllLicenseDic();
        LicenseDicInfo licenseDicInfo = licenseDicInfoMap.get(businessLicenseType);
        if (Objects.isNull(licenseDicInfo) || !licenseDicInfo.getLegalPersonTypes().contains(legalPersonIdType)) {
            throw new CommonInvalidParameterException(ruleModel.getMessage());
        }
    }

    public void validBusinessLicenseLegalPersonIdNumber(Integer legalPersonIdType, String idNumber) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getLegalPersonIdNumber().get(legalPersonIdType);
        ruleValid(ruleModel.getRule(), idNumber, ruleModel.getMessage());
        if (legalPersonIdType == 1) {
            IdentityCardUtils.verifyByIdTypeAndRegex(1, idNumber);
        }
    }

    public void validBusinessLicenseLegalPersonName(Integer legalPersonIdType, String legalPersonName) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getLegalPersonName().get(legalPersonIdType);
        ruleValid(ruleModel.getRule(), legalPersonName, ruleModel.getMessage());
    }

    public void validBusinessLicenseIdValidity(Integer legalPersonIdType, String idValidity) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getIdValidity();
        ruleValid(ruleModel.getRule(), idValidity, ruleModel.getMessage());
        boolean check = IdentityCardUtils.checkIdValidity(legalPersonIdType, idValidity);
        if (!check) {
            throw new CommonInvalidParameterException(ruleModel.getMessage());
        }
    }

    public void validBusinessLicenseLegalPersonFrontPhoto(Integer legalPersonIdType, String photo) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getLegalPersonIdCardFrontPhoto().get(legalPersonIdType);
        ruleValid(ruleModel.getRule(), photo, ruleModel.getMessage());
    }

    public void validBusinessLicenseLegalPersonBackPhoto(Integer legalPersonIdType, String photo) {
        BusinessLicenseRuleConfig businessLicense = commonApolloConfigs.getFieldRuleConfig().getBusinessLicense();
        RuleModel ruleModel = businessLicense.getLegalPersonIdCardBackPhoto().get(legalPersonIdType);
        ruleValid(ruleModel.getRule(), photo, ruleModel.getMessage());
    }

    public void validTradeLicensePhoto(String photo) {
        TradeLicenseRuleConfig tradeLicense = commonApolloConfigs.getFieldRuleConfig().getTradeLicense();
        RuleModel ruleModel = tradeLicense.getLicensePhoto();
        ruleValid(ruleModel.getRule(), photo, ruleModel.getMessage());
    }

    public void validTradeLicenseName(String name) {
        TradeLicenseRuleConfig tradeLicense = commonApolloConfigs.getFieldRuleConfig().getTradeLicense();
        RuleModel ruleModel = tradeLicense.getLicenseName();
        ruleValid(ruleModel.getRule(), name, ruleModel.getMessage());
    }

    public void validTradeLicenseNumber(String number) {
        TradeLicenseRuleConfig tradeLicense = commonApolloConfigs.getFieldRuleConfig().getTradeLicense();
        RuleModel ruleModel = tradeLicense.getLicenseNumber();
        ruleValid(ruleModel.getRule(), number, ruleModel.getMessage());
    }

    public void validTradeLicenseValidity(String validity) {
        TradeLicenseRuleConfig tradeLicense = commonApolloConfigs.getFieldRuleConfig().getTradeLicense();
        RuleModel ruleModel = tradeLicense.getLicenseValidity();
        ruleValid(ruleModel.getRule(), validity, ruleModel.getMessage());
        boolean check = IdentityCardUtils.checkValidity(validity);
        if (!check) {
            throw new CommonInvalidParameterException(ruleModel.getMessage());
        }
    }

    private void ruleValid(List<String> rules, String value, String message) {
        for (String rule : rules) {
            if (!Pattern.matches(rule, value)) {
                throw new CommonInvalidParameterException(message);
            }
        }
    }

}
