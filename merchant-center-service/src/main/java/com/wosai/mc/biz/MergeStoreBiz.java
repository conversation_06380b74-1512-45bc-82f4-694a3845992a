package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.merchant.request.StoreInfoChangeRequest;
import com.wosai.market.merchant.dto.store.StoreMergeInfo;
import com.wosai.mc.constants.MergeStoreConstant;
import com.wosai.mc.entity.StoreListHandleSuccess;
import com.wosai.mc.entity.StoreListHandleSuccessExample;
import com.wosai.mc.entity.StoreListWaitHandle;
import com.wosai.mc.mapper.StoreListHandleSuccessMapper;
import com.wosai.mc.mapper.StoreListWaitHandleMapper;
import com.wosai.mc.model.app.req.ChangeDataReq;
import com.wosai.mc.model.app.resp.MergeStoreDataResp;
import com.wosai.mc.utils.DingUtil;
import com.wosai.mc.volcengine.dataCenter.DataCenterProducer;
import com.wosai.mc.volcengine.dto.DataCenterMessageBody;
import com.wosai.mc.volcengine.enums.MessageTypeEnum;
import com.wosai.sales.model.gaoDe.GetPoiDetailByPoiReq;
import com.wosai.sales.model.gaoDe.PoiDetail;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.mc.biz.DataProcessingTask.compareCoordinates;
import static com.wosai.mc.constants.MergeStoreConstant.*;

@Component
@Slf4j
public class MergeStoreBiz {
    @Autowired
    private StoreListWaitHandleMapper waitHandleMapper;
    @Autowired
    private StoreListHandleSuccessMapper handleSuccessMapper;
    @Autowired
    private StoreService storeService;
    @Autowired
    private StoreRemoteService storeRemoteService;
    @Autowired
    private SideNoticeBiz sideNoticeBiz;
    @Autowired
    private DistrictsServiceV2 districtsServiceV2;
    @Autowired
    private GaoDeService gaoDeService;
    @Autowired
    private DingUtil dingUtil;
    @Autowired
    private DataCenterProducer dataCenterProducer;
    @Value("${datacenter.appid.bmerchant}")
    private String bMerchantAppId;
    private static final String STORE_INFO_MERGE_EVENT = "StoreInfoCheck";


    public MergeStoreDataResp addData(List<StoreListWaitHandle> waitHandleList) {
        if (WosaiCollectionUtils.isEmpty(waitHandleList)) {
            return null;
        }
        MergeStoreDataResp mergeStoreDataResp = new MergeStoreDataResp();
        List<MergeStoreDataResp.DataDetails> data = waitHandleList.stream().map(this::convert).collect(Collectors.toList());
        mergeStoreDataResp.setData(data);
        return mergeStoreDataResp;
    }

    private MergeStoreDataResp.DataDetails convert(StoreListWaitHandle storeListWaitHandle) {
        MergeStoreDataResp.DataDetails dataDetails = new MergeStoreDataResp.DataDetails();
        String storeId = storeListWaitHandle.getStore_id();
        Map centerStore = storeService.getStoreByStoreId(storeId);
        StoreMergeInfo storeMergeInfo = storeRemoteService.getStoreMergeInfo(storeId);

        //组数据,门店id
        dataDetails.setStore_id(storeId);
        dataDetails.setName(Arrays.asList(storeMergeInfo.getStoreName(), MapUtils.getString(centerStore, Store.NAME)));
        dataDetails.setTakeout_active(Objects.equals(storeListWaitHandle.getIs_takeout_active(), TAKEOUT_ACTIVE));
        //门店名称或门店经纬度（前五位）是否不同
        String longitude = storeMergeInfo.getLongitude();
        String latitude = storeMergeInfo.getLatitude();
        String centerLongitude = MapUtils.getString(centerStore, Store.LONGITUDE);
        String centerLatitude = MapUtils.getString(centerStore, Store.LATITUDE);
        boolean diff_itude = !compareCoordinates(longitude, centerLongitude) || !compareCoordinates(latitude, centerLatitude);
        dataDetails.setAddress(Arrays.asList(getAddress(storeMergeInfo), getCenterAddress(centerStore)));
        dataDetails.setDiff_itude(diff_itude);
        return dataDetails;
    }

    private MergeStoreDataResp.Address getCenterAddress(Map centerStore) {
        MergeStoreDataResp.Address address = new MergeStoreDataResp.Address();
        address.setPoi_name(MapUtils.getString(centerStore, Store.POI_NAME));
        address.setPoi_simple_address(MapUtils.getString(centerStore, Store.POI_SIMPLE_ADDRESS));
        address.setStreet_address(MapUtils.getString(centerStore, Store.STREET_ADDRESS));
        address.setStreet_address_desc(MapUtils.getString(centerStore, Store.STREET_ADDRESS_DESC));
        Map extra = (Map) centerStore.get("extra");
        if (MapUtils.isNotEmpty(extra)) {
            address.setPoi_address(MapUtils.getString(extra, "poi_address"));
        }
        return address;
    }


    private MergeStoreDataResp.Address getAddress(StoreMergeInfo storeMergeInfo) {
        MergeStoreDataResp.Address address = new MergeStoreDataResp.Address();
        address.setPoi_name(storeMergeInfo.getPoiName());
        address.setPoi_simple_address(storeMergeInfo.getPoiSimpleAddress());
        address.setStreet_address(storeMergeInfo.getStreetAddress());
        return address;
    }


    public void changeData(List<ChangeDataReq.Req> reqs, String merchantId) {
        for (ChangeDataReq.Req req : reqs) {
            StoreListWaitHandle waitHandle = waitHandleMapper.selectByStoreId(req.getStore_id());
            if (waitHandle == null) {
                continue;
            }
            Map centerStore = storeService.getStoreByStoreId(req.getStore_id());
            StoreMergeInfo storeMergeInfo = storeRemoteService.getStoreMergeInfo(req.getStore_id());


            Map updateParams = new HashMap<>();
            updateParams.put(DaoConstants.ID, req.getStore_id());
            StoreListWaitHandle waitHandleUpdate = new StoreListWaitHandle().setId(waitHandle.getId()).setIs_merged_name_address(MERGED);

            if (req.getChoosed_name() != null && req.getChoosed_name().equals(MergeStoreConstant.SMART)) {
                updateParams.put(Store.NAME, storeMergeInfo.getStoreName());
                waitHandleUpdate.setChoosed_name(MergeStoreConstant.SMART);
            } else if (req.getChoosed_name() != null && req.getChoosed_name().equals(MergeStoreConstant.CENTER)) {
                updateParams.put(Store.NAME, MapUtils.getString(centerStore, Store.NAME));
                waitHandleUpdate.setChoosed_name(MergeStoreConstant.CENTER);
            }


            if (Objects.equals(req.getChoosed_address(), MergeStoreConstant.SMART)) {
                chooseSmartAddress(storeMergeInfo, centerStore, updateParams, waitHandleUpdate);

            } else if (Objects.equals(req.getChoosed_address(), MergeStoreConstant.CENTER)) {
                chooseCenterAddress(waitHandleUpdate, centerStore);
            }
            waitHandleUpdate.setUpdate_params_name_address(JSON.toJSONString(updateParams));
            waitHandleUpdate.setOriginal_name_address(JSON.toJSONString(getCenterOriginalAddressData(centerStore)));
            storeService.updateStore(updateParams);
            waitHandleMapper.updateByPrimaryKeySelective(waitHandleUpdate);
            StoreInfoChangeRequest request = new StoreInfoChangeRequest();
            request.setStoreId(req.getStore_id());
            request.setAddressChange(true);
            request.setPoiChange(true);
            request.setStoreNameChange(true);
            request.setContactPhoneChange(false);
            storeRemoteService.afterStoreInfoChange(request);
        }
        sideNoticeBiz.noticeStoreMergedNum(merchantId);

        //每次确认完,查一下有没有还需要处理的,没有则发送事件
        List<StoreListWaitHandle> storeListWaitHandles = waitHandleMapper.selectByMerchantIdNotMergeAddress(merchantId);
        if (WosaiCollectionUtils.isNotEmpty(storeListWaitHandles)) {
            return;
        }
        //发送完成合并的事件
        dataCenterProducer.publish(new DataCenterMessageBody()
                .setMessage_type(MessageTypeEnum.EVENT.name())
                .setApp_id(bMerchantAppId)
                .setUser_unique_id(merchantId)
                .setEvent_name(STORE_INFO_MERGE_EVENT)
                .setEvent_params(CollectionUtil.hashMap(
                                "check_operate", "1"
                        )
                ));

    }

    public void chooseCenterAddress(StoreListWaitHandle waitHandleUpdate, Map centerStore) {
        //把查回来的原值记录一下即可
        waitHandleUpdate.setChoosed_address(MergeStoreConstant.CENTER);
        //原表的也需要检查下是否失效
        checkDistrictCode(MapUtils.getString(centerStore, Store.DISTRICT_CODE), centerStore);
    }

    public void chooseSmartAddress(StoreMergeInfo storeMergeInfo, Map centerStore, Map updateParams, StoreListWaitHandle waitHandleUpdate) {
        // Map extra = null;
//        String poiAddressValue = storeMergeInfo.getPoiName() + "(" + storeMergeInfo.getPoiSimpleAddress() + ")";
//
//        if (centerStore.get(Store.EXTRA) != null && centerStore.get(Store.EXTRA) instanceof Map) {
//            extra = (Map) centerStore.get(Store.EXTRA);
//            extra.put("poi_address", poiAddressValue);
//        } else {
//            extra = new HashMap();
//            extra.put("poi_address", poiAddressValue);
//        }
        updateParams.put(Store.LONGITUDE, storeMergeInfo.getLongitude());
        updateParams.put(Store.LATITUDE, storeMergeInfo.getLatitude());
        updateParams.put(Store.POI_NAME, storeMergeInfo.getPoiName());
        updateParams.put(Store.POI_SIMPLE_ADDRESS, storeMergeInfo.getPoiSimpleAddress());
        //coreB会更新省市区
        updateParams.put(Store.DISTRICT_CODE, storeMergeInfo.getDistrictCode());
        updateParams.put(Store.STREET_ADDRESS, storeMergeInfo.getStreetAddress());
        updateParams.put(Store.STREET_ADDRESS_DESC, null);
//        if (MapUtils.isNotEmpty(extra)) {
//            updateParams.put(Store.EXTRA, extra);
//        }
        //需要检查下地区码,是否合法
        checkDistrictCode(storeMergeInfo.getDistrictCode(), updateParams);

        waitHandleUpdate.setChoosed_address(MergeStoreConstant.SMART);
    }

    public void checkDistrictCode(String districtCode, Map updateParams) {
        if (districtCode == null) {
            districtCode = "";
        }
        //先检查下当前code是否有效
        District district = districtsServiceV2.getDistrict(CollectionUtil.hashMap(
                "code", districtCode
        ));
        if (Objects.isNull(district) || Objects.equals(0, district.getStatus())) {
            //无效状态,根据经纬度反查地区码,
            String longitude = BeanUtil.getPropString(updateParams, Store.LONGITUDE);
            String latitude = BeanUtil.getPropString(updateParams, Store.LATITUDE);
            if (!WosaiStringUtils.isEmptyAny(longitude, latitude) && !Objects.equals("0.000000", longitude) && !Objects.equals("0.000000", latitude)) {
                GetPoiDetailByPoiReq poiReq = new GetPoiDetailByPoiReq(longitude, latitude);
                poiReq.setBusiness("支付业务");
                poiReq.setScene("进件");
                PoiDetail poiDetail = gaoDeService.getPoiDetailByPoi(poiReq);

                District districtFromGaoDe = districtsServiceV2.getDistrict(CollectionUtil.hashMap(
                        "code", poiDetail.getDistrict_code()
                ));

                if (Objects.isNull(districtFromGaoDe) || Objects.equals(0, districtFromGaoDe.getStatus())) {
                    //查询失败,需要人工介入
                    StringBuilder message = new StringBuilder();
                    message.append("门店id ").append(MapUtils.getString(updateParams, DaoConstants.ID))
                            .append(" 地区码无效,反查高德失败. 原地区码:").append(districtCode)
                            .append(" 高德返回地区码:").append(poiDetail.getDistrict_code());
                    dingUtil.sendMessage(message.toString());
                } else {
                    //修正数据
                    updateParams.put(Store.DISTRICT_CODE, districtFromGaoDe.getCode());
                }
            } else {
                StringBuilder message = new StringBuilder();
                message.append("门店id ").append(MapUtils.getString(updateParams, DaoConstants.ID))
                        .append(" 智慧经营经纬度无效,longitude: ").append(longitude)
                        .append(" ,latitude: ").append(latitude);
                log.info("发送告警: {}", message);
                dingUtil.sendMessage(message.toString());
            }

        }
    }

    public Map getCenterOriginalAddressData(Map centerStore) {
        Map params = new HashMap();
        params.put(Store.NAME, MapUtils.getString(centerStore, Store.NAME));
        params.put(Store.LONGITUDE, MapUtils.getString(centerStore, Store.LONGITUDE));
        params.put(Store.LATITUDE, MapUtils.getString(centerStore, Store.LATITUDE));
        params.put(Store.POI_NAME, MapUtils.getString(centerStore, Store.POI_NAME));
        params.put(Store.POI_SIMPLE_ADDRESS, MapUtils.getString(centerStore, Store.POI_SIMPLE_ADDRESS));
        params.put(Store.DISTRICT_CODE, MapUtils.getString(centerStore, Store.DISTRICT_CODE));
        params.put(Store.STREET_ADDRESS, MapUtils.getString(centerStore, Store.STREET_ADDRESS));
        params.put(Store.STREET_ADDRESS_DESC, MapUtils.getString(centerStore, Store.STREET_ADDRESS_DESC));
        params.put(Store.EXTRA, MapUtils.getString(centerStore, Store.EXTRA));
        return params;
    }

    /**
     * @param id waitHande 记录的id
     */
    public void moveWaitHandleToSuccess(Long id) {
        StoreListWaitHandle waitHandle = waitHandleMapper.selectByPrimaryKey(id);
        if (waitHandle == null) {
            return;
        }
        if (MergeStoreConstant.MERGED_OR_NO_NEED_MERGE.contains(waitHandle.getIs_merged_name_address()) && Objects.equals(waitHandle.getIs_contract_name_phone_merged(), MERGED)) {
            StoreListHandleSuccess success = new StoreListHandleSuccess();
            BeanUtils.copyProperties(waitHandle, success);
            handleSuccessMapper.insert(success);
            waitHandleMapper.deleteByPrimaryKey(id);
        }
    }

    public boolean isMerged(String merchantId) {
        StoreListHandleSuccessExample example = new StoreListHandleSuccessExample();
        example.or().andMerchant_idEqualTo(merchantId);
        List<StoreListHandleSuccess> storeListHandleSuccesses = handleSuccessMapper.selectByExample(example);
        //是否合并过门店名称和地址
        return storeListHandleSuccesses.stream().anyMatch(s -> Objects.equals(s.getIs_merged_name_address(), MERGED));
    }
}
