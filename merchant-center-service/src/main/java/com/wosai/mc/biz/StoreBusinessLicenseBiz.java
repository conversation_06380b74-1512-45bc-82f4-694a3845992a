package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.config.exception.McException;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.StoreBusinessLicenseInfo;
import com.wosai.mc.model.req.UpdateStoreBusinessLicenseReq;
import com.wosai.mc.service.LicenseService;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.StoreBusinessLicenseService;
import com.wosai.upay.core.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class StoreBusinessLicenseBiz {

    @Autowired
    private McPreBiz mcPreBiz;
    @Autowired
    private StoreBusinessLicenseService storeBusinessLicenseService;

    @Autowired
    private StoreService storeService;
    @Autowired
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;
    @Autowired
    private LicenseService licenseService;

    public StoreBusinessLicenseInfo getStoreBusinessLicenseByStoreId(String storeId, String devCode, boolean needHandlePhotos) {
        Map<String, Object> storeBusinessLicense = null;
        if (StringUtils.isNotEmpty(devCode)) {
            storeBusinessLicense = mcPreBiz.getMcPreData(storeId, devCode, TableNameEnum.STORE_BUSINESS_LICENSE.getTableName());
            if (MapUtils.isNotEmpty(storeBusinessLicense) && needHandlePhotos) {
                CommonUtils.handPhotos(storeBusinessLicense, "photo");
                CommonUtils.handPhotos(storeBusinessLicense, "legal_person_id_card_front_photo");
                CommonUtils.handPhotos(storeBusinessLicense, "legal_person_id_card_back_photo");
            }
            //中间表有营业执照数据(非复用)再去查中间表许可证并返回
            if (WosaiMapUtils.isNotEmpty(storeBusinessLicense)) {
                //复用情况下,返回商户维度营业执照数据(优先走中间表)
                if (MapUtils.getBoolean(storeBusinessLicense, "use_merchant_business_license", false)) {
                    MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(MapUtils.getString(storeBusinessLicense, MerchantBusinessLicence.MERCHANT_ID), "getStoreBusinessLicenseByStoreId", true);
                    StoreBusinessLicenseInfo storeBusinessLicenseInfo = JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);

                    storeBusinessLicenseInfo.setUse_merchant_business_license(true);
                    BeanUtils.copyProperties(merchantBusinessLicense, storeBusinessLicenseInfo);
                    return storeBusinessLicenseInfo;
                }
                //非复用情况下
                List<Map> mc_licenses = licenseService.getLicenseByBusinessLicenseId((String) storeBusinessLicense.get("id"), devCode);
                storeBusinessLicense.put("trade_license_list", mc_licenses);
                return JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);
            }
        }

        //中间表无营业执照,直接原表查返回
        storeBusinessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeId);

        //原表也没有数据,就默认复用商户营业执照的
//        if (MapUtils.isEmpty(storeBusinessLicense)) {
//            //创建门店营业执照(标识为复用)
//            createStoreBusinessLicense(storeId);
//            storeBusinessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeId);
//        }
        if (storeBusinessLicense == null) {
            return null;
        }
        //是否复用
        if ((boolean) storeBusinessLicense.get("use_merchant_business_license")) {
            //再走中间表
            if (WosaiStringUtils.isNotEmpty(devCode)) {
                MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(MapUtils.getString(storeBusinessLicense, MerchantBusinessLicence.MERCHANT_ID), "getStoreBusinessLicenseByStoreId2", true);
                StoreBusinessLicenseInfo storeBusinessLicenseInfo = JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);

                storeBusinessLicenseInfo.setUse_merchant_business_license(true);
                BeanUtils.copyProperties(merchantBusinessLicense, storeBusinessLicenseInfo);
                return storeBusinessLicenseInfo;
            }
            //原表
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId((String) storeBusinessLicense.get("merchant_id"), null, false);

            StoreBusinessLicenseInfo storeBusinessLicenseInfo = JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);

            storeBusinessLicenseInfo.setUse_merchant_business_license(true);
            BeanUtils.copyProperties(merchantBusinessLicense, storeBusinessLicenseInfo);
            return storeBusinessLicenseInfo;
        }

        return JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);

    }

    public void updateStoreBusinessLicense(UpdateStoreBusinessLicenseReq req, String devCode) {
        Map storeBusinessLicense = MyBeanUtil.toMap(req);
        //不根据id进行操作,有store_id即可
        storeBusinessLicense.remove("id");
        Map<String, Object> storeBusinessLicenseByStoreId = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(req.getStoreId());
        if (StringUtils.isNotEmpty(devCode)) {
            if (storeBusinessLicenseByStoreId == null) {
                log.error("更新门店营业执照异常,未查到指定营业执照信息,req : {}", req);
                throw new McException("未查到指定门店营业执照信息");
            }
            //去掉默认返回的许可证信息
            storeBusinessLicenseByStoreId.remove("trade_license_list");
            storeBusinessLicenseByStoreId.putAll(storeBusinessLicense);
            mcPreBiz.recordMcPre(TableNameEnum.STORE_BUSINESS_LICENSE.getTableName(), req.getStoreId(), devCode, storeBusinessLicenseByStoreId);
        } else {
            storeBusinessLicenseService.updateStoreBusinessLicense(storeBusinessLicense);
            //检查中间表
            Map mcPre = mcPreBiz.findMcPre(TableNameEnum.STORE_BUSINESS_LICENSE.getTableName(), req.getStoreId());
            if (WosaiMapUtils.isNotEmpty(mcPre)) {
                if (storeBusinessLicenseByStoreId == null) {
                    return;
                }
                Map data = CommonUtils.beanToTargetObj(mcPre.get("data"), Map.class);
                if (WosaiMapUtils.isNotEmpty(data)) {
                    data.putAll(storeBusinessLicense);
                    mcPreBiz.recordMcPre(TableNameEnum.STORE_BUSINESS_LICENSE.getTableName(), req.getStoreId(), "updateOrigin", data);
                }
            }
        }
    }

    /**
     * @return 所有认证的字段
     */
    public static List<String> getAllAuditProp() {
        List<String> storeBusinessLicenseData = new ArrayList<>(GetRealObject.STORE_BUSINESS_LICENSE_DATA);
        storeBusinessLicenseData.removeAll(Arrays.asList("id", "merchant_id", "store_id", "ctime", "mtime", "deleted", "verify_status", "extra", "use_merchant_business_license"));
        return storeBusinessLicenseData;
    }

    private void createStoreBusinessLicense(String storeId) {
        Map store = storeService.getStoreByStoreId(storeId);
        if (MapUtils.isNotEmpty(store)) {
            //创建一个门店营业执照
            storeBusinessLicenseService.saveStoreBusinessLicense(CollectionUtil.hashMap("merchant_id", store.get("merchant_id"), "store_id", storeId, "use_merchant_business_license", true));
        }
    }
}
