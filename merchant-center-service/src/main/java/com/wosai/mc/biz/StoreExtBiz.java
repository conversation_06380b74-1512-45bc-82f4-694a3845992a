package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.wosai.app.model.GetPlayInfoReq;
import com.wosai.app.service.VideoService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.entity.converter.StotreExtInfoAndPicturesConverter;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.req.StoreExtReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.PhotoInfoService;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.service.StoreExtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/18
 */
@Slf4j
@Component
public class StoreExtBiz {

    @Autowired
    private StoreExtService coreStoreExtService;

    @Autowired
    private McPreBiz mcPreBiz;

    @Autowired
    private PhotoInfoService photoInfoService;

    @Autowired
    private VideoService videoService;

    @Autowired
    private com.wosai.upay.core.service.PhotoInfoService corePhotoInfoService;

    public void updateStoreExt(StoreExtReq storeExtReq, String devCode) {
        Map<String, String> map = JSON.parseObject(JSON.toJSONString(storeExtReq), Map.class);
        Map<String, String> req = MyBeanUtil.getUnderscoreFromLowerCameL(map);
        Map storeExt;
        if (StringUtils.isNotEmpty(devCode)) {
            if (storeExtReq.getId() != null) {
                storeExt = findStoreExtMap(String.valueOf(storeExtReq.getId()), devCode);
            } else {
                storeExt = findStoreExtMapByStoreId(storeExtReq.getStoreId(), devCode);
            }
            if (MapUtils.isEmpty(storeExt)) {
                //以前没有环境信息,触发创建
                storeExt = new HashMap<>();
                storeExt.putAll(req);
                coreStoreExtService.createStoreExt(storeExt);
                return;
            }
            storeExt.putAll(req);
            mcPreBiz.recordMcPre(TableNameEnum.STORE_EXT.getTableName(), storeExt.get("id").toString(), devCode, storeExt);
        } else {
            if (storeExtReq.getId() == null) {
                storeExt = coreStoreExtService.findStoreExtByStoreId(storeExtReq.getStoreId());
                if (MapUtils.isEmpty(storeExt)) {
                    //以前没有环境信息,触发创建
                    coreStoreExtService.createStoreExt(req);
                } else {
                    req.put("id", storeExt.get("id").toString());
                    coreStoreExtService.updateStoreExt(req);
                }

            } else {
                coreStoreExtService.updateStoreExt(req);
            }

            //检查是否有中间表
            Map mcPre = mcPreBiz.findMcPre(TableNameEnum.STORE_EXT.getTableName(), MapUtils.getString(req, DaoConstants.ID));
            if (WosaiMapUtils.isNotEmpty(mcPre)) {
                Map data = CommonUtils.beanToTargetObj(mcPre.get("data"), Map.class);
                if (WosaiMapUtils.isNotEmpty(data)) {
                    data.putAll(req);
                    mcPreBiz.recordMcPre(TableNameEnum.STORE_EXT.getTableName(), MapUtils.getString(req, DaoConstants.ID), "updateOrigin", data);
                }
            }
        }
    }

    public void updateStoreExtPictureIds(Map storeExt, String devCode) {
        if (StringUtils.isNotEmpty(devCode)) {
            Map storeExtOld = this.findStoreExtMap(String.valueOf(storeExt.get("id")), devCode);
            storeExtOld.putAll(storeExt);
            mcPreBiz.recordMcPre(TableNameEnum.STORE_EXT.getTableName(), storeExt.get("id").toString(), devCode, storeExtOld);
        } else {
            coreStoreExtService.updateStoreExt(storeExt);
        }
    }


    public Map findStoreExtMap(String id, String devCode) {
        Map storeExt = null;
        if (StringUtils.isNotEmpty(devCode)) {
            storeExt = mcPreBiz.getMcPreData(id, devCode, TableNameEnum.STORE_EXT.getTableName());
        }
        if (MapUtils.isEmpty(storeExt)) {
            storeExt = coreStoreExtService.findStoreExt(id);
        }
        return MapUtils.isEmpty(storeExt) ? null : storeExt;
    }

    public StoreExtInfo findStoreExt(String id, String devCode) {
        Map storeExt = findStoreExtMap(id, devCode);
        return MapUtils.isEmpty(storeExt) ? null : JSON.parseObject(JSON.toJSONString(storeExt), StoreExtInfo.class);
    }

    public Map findStoreExtMapByStoreId(String storeId, String devCode) {
        Map storeExtByStoreId = coreStoreExtService.findStoreExtByStoreId(storeId);
        if (MapUtils.isEmpty(storeExtByStoreId)) {
            return null;
        }
        Map storeExt = null;
        if (StringUtils.isNotEmpty(devCode)) {
            storeExt = mcPreBiz.getMcPreData(String.valueOf(storeExtByStoreId.get("id")), devCode, TableNameEnum.STORE_EXT.getTableName());
        }
        if (MapUtils.isEmpty(storeExt)) {
            storeExt = coreStoreExtService.findStoreExtByStoreId(storeId);
        }
        return MapUtils.isEmpty(storeExt) ? null : storeExt;
    }

    public StoreExtInfo findStoreExtByStoreId(String storeId, String devCode) {
        Map storeExt = findStoreExtMapByStoreId(storeId, devCode);
        return MapUtils.isEmpty(storeExt) ? null : JSON.parseObject(JSON.toJSONString(storeExt), StoreExtInfo.class);
    }

    public StoreExtInfo findLastStoreExtByStoreId(String storeId) {
        Map storeExt = coreStoreExtService.findStoreExtByStoreId(storeId);
        if (MapUtils.isEmpty(storeExt)) {
            return null;
        }
        storeExt = mcPreBiz.mergeMcPre(String.valueOf(storeExt.get("id")), storeExt, TableNameEnum.STORE_EXT.getTableName());
        return JSONObject.parseObject(JSONObject.toJSONString(storeExt), StoreExtInfo.class);
    }

    public StotreExtInfoAndPictures findStoreExtAndPicturesByStoreId(String storeId, String devCode) {
        Map storeExtByStoreId = coreStoreExtService.findStoreExtByStoreId(storeId);
        if (MapUtils.isNotEmpty(storeExtByStoreId)) {
            //抽出一个属性类循环处理更好，对方不存pre表不需要查询pre表
            StoreExtInfo storeExtInfo = findStoreExt(String.valueOf(storeExtByStoreId.get("id")), devCode);
            StotreExtInfoAndPictures pictures = this.buildPicturesByStoreExtInfo(storeExtInfo, devCode);
            return pictures;
        }
        return null;
    }

    public StotreExtInfoAndPictures findLastStoreExtAndPicturesByStoreId(String storeId) {
        StoreExtInfo storeExtInfo = this.findLastStoreExtByStoreId(storeId);
        if (storeExtInfo == null) {
            return null;
        }
        return this.buildPicturesByStoreExtInfo(storeExtInfo, null);
    }

    public List<PhotoInfo> findStoreOtherPhotos(String storeId, String devCode) {
        StoreExtInfo storeExtInfo = this.findStoreExtByStoreId(storeId, devCode);
        if (storeExtInfo == null || WosaiStringUtils.isEmpty(storeExtInfo.getStoreId())) {
            return new ArrayList<>();
        }
        return Optional.ofNullable(getPhotoInfos(null, storeExtInfo.getOtherPhotoId())).orElse(new ArrayList<>());
    }

    /**
     * 根据storeExt里面存储的照片id和dev_code去查找照片
     * devCode == null 根据id去查询photo_info表中的数据
     * devCode != null 根据id和dev_code查询photo_info表中的数据
     *
     * @param storeExtInfo 门店环境信息
     * @param devCode      业务方标识
     * @return 照片等信息
     */
    private StotreExtInfoAndPictures buildPicturesByStoreExtInfo(StoreExtInfo storeExtInfo, String devCode) {
        StotreExtInfoAndPictures stotreExtInfoAndPictures = StotreExtInfoAndPicturesConverter.INSTANCE.do2dto(storeExtInfo);
        // 室内物料照
        String indoorMaterialPhotoId = storeExtInfo.getIndoorMaterialPhotoId();
        PhotoInfo indoorMaterialPhoto = getPhotoInfo(devCode, indoorMaterialPhotoId);
        stotreExtInfoAndPictures.setIndoorMaterialPhoto(indoorMaterialPhoto);
        // 内景照
        String indoorOnlyScenePhotoId = storeExtInfo.getIndoorOnlyScenePhotoId();
        PhotoInfo indoorOnlyScenePhoto = getPhotoInfo(devCode, indoorOnlyScenePhotoId);
        stotreExtInfoAndPictures.setIndoorOnlyScenePhoto(indoorOnlyScenePhoto);
        // 其他照片
        String otherPhotoId = storeExtInfo.getOtherPhotoId();
        List<PhotoInfo> otherPhotoList = getPhotoInfos(devCode, otherPhotoId);
        stotreExtInfoAndPictures.setOtherPhoto(otherPhotoList);
        // 点单价目表
        String orderPricePhotoId = storeExtInfo.getOrderPricePhotoId();
        List<PhotoInfo> orderPhotoList = getPhotoInfos(devCode, orderPricePhotoId);
        stotreExtInfoAndPictures.setOrderPricePhoto(orderPhotoList);
        // 室外物料照
        String outdoorMaterialPhotoId = storeExtInfo.getOutdoorMaterialPhotoId();
        PhotoInfo outdoorMaterialPhoto = getPhotoInfo(devCode, outdoorMaterialPhotoId);
        stotreExtInfoAndPictures.setOutdoorMaterialPhoto(outdoorMaterialPhoto);
        // 外景照
        String outdoorOnlyScenePhotoId = storeExtInfo.getOutdoorOnlyScenePhotoId();
        PhotoInfo outdoorOnlyScenePhoto = getPhotoInfo(devCode, outdoorOnlyScenePhotoId);
        stotreExtInfoAndPictures.setOutdoorOnlyScenePhoto(outdoorOnlyScenePhoto);
        // 门头合照
        String brandPhotoId = storeExtInfo.getBrandPhotoId();
        PhotoInfo brandPhoto = getPhotoInfo(devCode, brandPhotoId);
        stotreExtInfoAndPictures.setBrandPhoto(brandPhoto);
        // 门头照
        String brandOnlyScenePhotoId = storeExtInfo.getBrandOnlyScenePhotoId();
        PhotoInfo brandOnlyScenePhoto = getPhotoInfo(devCode, brandOnlyScenePhotoId);
        stotreExtInfoAndPictures.setBrandOnlyScenePhoto(brandOnlyScenePhoto);
        // 商品价目表
        String productPriceId = storeExtInfo.getProductPriceId();
        PhotoInfo productPrice = getPhotoInfo(devCode, productPriceId);
        stotreExtInfoAndPictures.setProductPrice(productPrice);
        // 审批截图
        String auditPictureId = storeExtInfo.getAuditPictureId();
        PhotoInfo auditPicture = getPhotoInfo(devCode, auditPictureId);
        stotreExtInfoAndPictures.setAuditPicture(auditPicture);
        // 视频
        if (StringUtil.isNotEmpty(storeExtInfo.getVideo())) {
            GetPlayInfoReq getPlayInfoReq = new GetPlayInfoReq();
            getPlayInfoReq.setVideoId(storeExtInfo.getVideo());
            getPlayInfoReq.setApp("sqb");
            GetPlayInfoResponse playInfo = null;
            try {
                playInfo = videoService.getPlayInfo(getPlayInfoReq);
            } catch (Exception e) {
                log.error("获取视频异常,门店id : {} storeExtId:{}", storeExtInfo.getStoreId(), storeExtInfo.getId(), e);
            }
            stotreExtInfoAndPictures.setVideoInfo(playInfo);
        }
        return stotreExtInfoAndPictures;
    }

    private List<PhotoInfo> getPhotoInfos(String devCode, String productPrice) {
        if (StringUtils.isNotEmpty(productPrice)) {
            String[] ids = productPrice.split(",");
            List<PhotoInfo> pictures = new ArrayList();
            for (String id : ids) {
                PhotoInfo photoInfoByIdAndDevcode = photoInfoService.getPhotoInfoByIdAndDevcode(id, devCode);
                if (photoInfoByIdAndDevcode != null) {
                    if (WosaiStringUtils.isNotEmpty(photoInfoByIdAndDevcode.getUrl())) {
                        //链接授权
                        String encryptUrl = CommonUtils.encryptUrl(photoInfoByIdAndDevcode.getUrl());
                        photoInfoByIdAndDevcode.setUrl(encryptUrl);
                    }

                    pictures.add(photoInfoByIdAndDevcode);
                }
            }
            return pictures;
        }
        return null;
    }

    private PhotoInfo getPhotoInfo(String devCode, String productPrice) {
        List<PhotoInfo> photoInfos = getPhotoInfos(devCode, productPrice);
        if (CollectionUtils.isNotEmpty(photoInfos)) {
            return photoInfos.get(0);
        }
        return null;

    }

    public Map<String, String> getBrandOnlyScenePhotos(List<String> storeIds) {
        return coreStoreExtService.getBrandOnlyScenePhotoBatchByStoreIds(storeIds);
    }
}
