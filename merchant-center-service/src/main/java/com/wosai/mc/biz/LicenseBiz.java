package com.wosai.mc.biz;

import com.google.common.collect.Lists;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.upay.core.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class LicenseBiz {
    @Autowired
    private LicenseService coreBLicenseService;
    @Autowired
    private McPreBiz mcPreBiz;

    /**
     * 对单个许可证进行认证
     *
     * @param id            许可证id
     * @param verify_status 认证状态
     * @param verifyParams  认证字段
     */
    public int verifyLicense(String id, String verify_status, List<String> verifyParams) {
        List<String> verifyStatuss = Lists.newArrayList("1", "2", "3");
        Map<String, Object> license = coreBLicenseService.getLicenseById(id);

        Map<String, List<String>> extra = (Map<String, List<String>>) license.get("extra");
        extra = Optional.ofNullable(extra).orElse(new HashMap<String, List<String>>(16));
        for (String verifyStatus : verifyStatuss) {
            if (verify_status.equals(verifyStatus)) {
                extra.put(verifyStatus, verifyParams);
                verifyStatuss.remove(verifyStatus);
                for (String status : verifyStatuss) {
                    if (extra.get(status) != null) {
                        extra.get(status).removeAll(verifyParams);
                    }
                }
                break;
            }
        }
        license.put("extra", extra);
        license.put("verify_status", Integer.parseInt(verify_status));
        coreBLicenseService.updateLicense(license);
        return 1;
    }

    /**
     * 可对单个许可证中间表进行更新[暂时废弃]
     * @param bizId
     * @param devCode
     * @param data
     */
    public void updateToMcPre(String bizId, String devCode, Map data) {
        mcPreBiz.recordLicenseMcPre(bizId, devCode, data);
    }

    /**
     * 许可证数据完整更新
     */
    public void updateToMcPreComplete(String bizId, String devCode, List<Map> data) {
        mcPreBiz.recordMcPreForList(TableNameEnum.LICENSE.getTableName(), bizId, devCode, data);
    }

    public static List<String> getAllAuditProp() {
        List<String> licenseData = new ArrayList<>(GetRealObject.LICENSE_DATA);
        licenseData.removeAll(Arrays.asList("id", "business_license_id", "verify_status", "extra", "ctime", "mtime", "deleted"));
        return licenseData;
    }
}
