package com.wosai.mc.biz;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.vdurmont.emoji.EmojiManager;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.merchant.request.StoreInfoChangeRequest;
import com.wosai.market.merchant.dto.store.StoreMergeInfo;
import com.wosai.mc.constants.MergeStoreConstant;
import com.wosai.mc.entity.MerchantIdList;
import com.wosai.mc.entity.StoreListWaitHandle;
import com.wosai.mc.mapper.MerchantIdListMapper;
import com.wosai.mc.mapper.StoreListHandleSuccessMapper;
import com.wosai.mc.mapper.StoreListWaitHandleMapper;
import com.wosai.mc.utils.ApolloParamsConfig;
import com.wosai.mc.utils.DingUtil;
import com.wosai.mc.utils.TimeUtil;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.StoreService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.wosai.mc.constants.MergeStoreConstant.*;
import static com.wosai.mc.schedule.ConfigPollingTask.*;


@Slf4j
@Component
public class DataProcessingTask implements Runnable {
    @Autowired
    private DataProcessingTask dataProcessingTask;
    @Autowired
    private ApolloParamsConfig apolloParamsConfig;
    @Autowired
    private CommonFieldService commonFieldService;
    @Autowired
    private StoreRemoteService storeRemoteService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private MergeStoreBiz mergeStoreBiz;
    @Autowired
    private SideNoticeBiz sideNoticeBiz;
    @Autowired
    private DingUtil dingUtil;
    @Autowired
    private MerchantIdListMapper merchantIdListMapper;
    @Autowired
    private StoreListWaitHandleMapper waitHandleMapper;
    @Autowired
    private StoreListHandleSuccessMapper successMapper;


    public String CENTER_STORE_NAME_REG = "^[\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF\\u4e00-\\u9fa5A-Za-z0-9\\s()（）\\-·~_<>《》&——_.;；‘’:：㛊' “” \" +]{2,40}$";
    public String PHONE_REG = "^1[3-9]\\d{9}$";
    private final Pattern compile_store_name = Pattern.compile(CENTER_STORE_NAME_REG);
    private final Pattern compile_phone = Pattern.compile(PHONE_REG);

    @SneakyThrows
    @Override
    public void run() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                MerchantIdList merchantIdList = merchantIdListMapper.selectMinId();
                if (merchantIdList == null) {
                    throw new InterruptedException("数据清洗完成");
                }
                Long minId = merchantIdList.getId();
                List<MerchantIdList> waitHandles = null;
                do {
                    Map config = apolloParamsConfig.getMap(APO_KEY, "{}");
                    long wait = MapUtils.getLongValue(config, APO_KEY_WAIT_TIME, 3000L);
                    boolean isOpen = MapUtils.getBooleanValue(config, APO_KEY_SWITCH, false);
                    if (!isOpen) {
                        throw new InterruptedException("任务开关已关闭");
                    }
                    waitHandles = merchantIdListMapper.selectOrderByIdAsc(minId);
                    if (!waitHandles.isEmpty()) {
                        log.info("正在处理商户Id表,开始id范围:{} 结束id范围:{}", minId, waitHandles.get(waitHandles.size() - 1).getId());
                        //下次轮询的id
                        minId = waitHandles.get(waitHandles.size() - 1).getId() + 1;
                    }
                    for (MerchantIdList waitHandle : waitHandles) {
                        List<StoreListWaitHandle> storeListWaitHandles = fetchDataBatchAndCheckNotice(waitHandle);
                        if (WosaiCollectionUtils.isEmpty(storeListWaitHandles)) {
                            //没数据说明此商户已处理完成,进行下一个商户处理
                            continue;
                        }
                        //处理某一商户的门店
                        try {
                            dataProcessingTask.processBatch(storeListWaitHandles);
                        } catch (Exception e) {
                            log.error("处理商户门店列表异常,商户id:{}", waitHandle.getMerchant_id(), e);
                            dingUtil.sendMessage("处理商户门店列表异常,商户id:" + waitHandle.getMerchant_id() + e.getMessage());
                            //下一个商户
                            continue;
                        }
                        // 休眠一段时间,控制频率
                        Thread.sleep(wait);
                    }

                } while (WosaiCollectionUtils.isNotEmpty(waitHandles));

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("任务被取消:", e);
                throw e;
            } catch (Exception ex) {
                log.error("任务执行异常:", ex);
            }
        }

    }


    private List<StoreListWaitHandle> fetchDataBatchAndCheckNotice(MerchantIdList merchantIdList) {
        List<StoreListWaitHandle> storeListWaitHandles = waitHandleMapper.selectByMerchantId(merchantIdList.getMerchant_id());

        if (WosaiCollectionUtils.isEmpty(storeListWaitHandles)) {
            //说明洗完了,删除这个商户的记录
            merchantIdListMapper.deleteByPrimaryKey(merchantIdList.getId());
            //检查下是否合并过数据,合并过则发送一个合并完成的通知
            if (mergeStoreBiz.isMerged(merchantIdList.getMerchant_id())) {
                //发送最终通知
                sideNoticeBiz.noticeMerchantAfterAllMerged(merchantIdList.getMerchant_id());
            }

        }
        return storeListWaitHandles;
    }

    @Transactional(rollbackFor = Exception.class)
    public void processBatch(List<StoreListWaitHandle> batch) {
        List<Long> allIds = batch.stream().map(StoreListWaitHandle::getId).collect(Collectors.toList());
        //商户下面的门店都先轮询一遍,再发通知,先发通知,再点进来会出现统计有误
        boolean needSendAddressNotice = false;
        String merchantId = batch.get(0).getMerchant_id();
        // 处理一批数据的逻辑
        for (StoreListWaitHandle storeListWaitHandle : batch) {
            StoreMergeInfo storeMergeInfo = storeRemoteService.getStoreMergeInfo(storeListWaitHandle.getStore_id());
            Map store = storeService.getStoreByStoreId(storeListWaitHandle.getStore_id());

            //是否已经合并过门店名称+地址
            if (storeListWaitHandle.getIs_merged_name_address() != null && MERGED_OR_NO_NEED_MERGE.contains(storeListWaitHandle.getIs_merged_name_address())) {
                StoreListWaitHandle waitHandleUpdate = new StoreListWaitHandle().setId(storeListWaitHandle.getId()).setIs_contract_name_phone_merged(MERGED);
                Map updateParams = null;
                Map originParams = null;
                //合并过则进行合并联系人信息,只合并联系电话
                if (WosaiStringUtils.isEmpty(storeMergeInfo.getContactCellPhone()) || !compile_phone.matcher(storeMergeInfo.getContactCellPhone()).find()) {
                    //以商户中心为准,只保存下当时参数
                    updateParams = CollectionUtil.hashMap(Store.CONTACT_CELLPHONE, MapUtils.getString(store, Store.CONTACT_CELLPHONE));
                } else {
                    //以智慧经营为准
                    updateParams = CollectionUtil.hashMap(Store.CONTACT_CELLPHONE, storeMergeInfo.getContactCellPhone());
                }
                originParams = CollectionUtil.hashMap(Store.CONTACT_CELLPHONE, MapUtils.getString(store, Store.CONTACT_CELLPHONE));
                waitHandleUpdate.setUpdate_params_contract_name_phone(JSON.toJSONString(updateParams));
                waitHandleUpdate.setOriginal_contract_name_phone(JSON.toJSONString(originParams));
                Map updateStore = CollectionUtil.hashMap(DaoConstants.ID, storeListWaitHandle.getStore_id());
                updateStore.putAll(updateParams);
                storeService.updateStore(updateStore);
                waitHandleMapper.updateByPrimaryKeySelective(waitHandleUpdate);
                StoreInfoChangeRequest request = new StoreInfoChangeRequest();
                request.setStoreId(storeListWaitHandle.getStore_id());
                request.setContactPhoneChange(true);
                storeRemoteService.afterStoreInfoChange(request);
                //合并到这一步,全部完成. 开始转移到历史表
                mergeStoreBiz.moveWaitHandleToSuccess(storeListWaitHandle.getId());
            } else {
                //门店是否有申请单
                ListResult infos = commonFieldService.findFieldAppInfos(
                        new PageInfo(1, 1),
                        CollectionUtil.hashMap("store_id", storeListWaitHandle.getStore_id(), "merchant_id", storeListWaitHandle.getMerchant_id(),
                                //含义:只查门店申请单
                                "field_main_type", 1
                        )
                );

                if (infos.getTotal() > 0) {
                    waitHandleMapper.updateByPrimaryKeySelective(new StoreListWaitHandle().setId(storeListWaitHandle.getId()).setIs_merged_name_address(NO_NEED_MERGE_HAD_APPLY));
                    continue;
                }


                //门店名称是否有emoji
                if (EmojiManager.containsEmoji(storeMergeInfo.getStoreName())) {
                    waitHandleMapper.updateByPrimaryKeySelective(new StoreListWaitHandle().setId(storeListWaitHandle.getId()).setIs_merged_name_address(NO_NEED_MERGE_HAD_EMOJI));
                    continue;
                }

                //是否符合正则规则
                if (!compile_store_name.matcher(storeMergeInfo.getStoreName()).find()) {
                    waitHandleMapper.updateByPrimaryKeySelective(new StoreListWaitHandle().setId(storeListWaitHandle.getId()).setIs_merged_name_address(NO_NEED_MERGE_REG_FAIL));
                    continue;
                }


                //智慧门店经纬度有空,不洗
                if (WosaiStringUtils.isEmpty(storeMergeInfo.getLongitude()) || WosaiStringUtils.isEmpty(storeMergeInfo.getLatitude())) {
                    waitHandleMapper.updateByPrimaryKeySelective(new StoreListWaitHandle().setId(storeListWaitHandle.getId()).setIs_merged_name_address(NO_NEED_MERGE_EMPTY_ITUDE));
                    continue;
                }
                //门店名称或门店经纬度（前五位）不一致?
                String longitude = storeMergeInfo.getLongitude();
                String latitude = storeMergeInfo.getLatitude();
                String centerLongitude = MapUtils.getString(store, Store.LONGITUDE);
                String centerLatitude = MapUtils.getString(store, Store.LATITUDE);

                if (!WosaiStringUtils.equals(storeMergeInfo.getStoreName(), MapUtils.getString(store, Store.NAME)) ||
                        !compareCoordinates(longitude, centerLongitude) || !compareCoordinates(latitude, centerLatitude)) {
                    //有需要选择的门店,需要向商户发送选择通知
                    if (storeListWaitHandle.getName_address_notice_time() != null) {
                        //已经发送过通知
                        Date nameAddressNoticeTime = storeListWaitHandle.getName_address_notice_time();

                        //没有发过10天催促 && 已有10天
                        if (storeListWaitHandle.getUrge_notice_10days_later() == null && TimeUtil.isDateBeyondDays(10, nameAddressNoticeTime)) {
                            //发送10天的催促,还有20天
                            sideNoticeBiz.noticeMerchantMergeStore(storeListWaitHandle.getMerchant_id(), 20);
                            //更新时间
                            waitHandleMapper.update10DaysNoticeTime(allIds, new Date());
                            return;
                        }
                        //没有发过20天催促 && 已有20天
                        if (storeListWaitHandle.getUrge_notice_20days_later() == null && TimeUtil.isDateBeyondDays(20, nameAddressNoticeTime)) {
                            //发送20天的催促,还有10天
                            sideNoticeBiz.noticeMerchantMergeStore(storeListWaitHandle.getMerchant_id(), 10);
                            //更新时间
                            waitHandleMapper.update20DaysNoticeTime(allIds, new Date());
                            return;
                        }
                        //如果已超30天,系统自动合并
                        if (TimeUtil.isDateBeyondDays(30, nameAddressNoticeTime)) {
                            Map updateParams = new HashMap<>();
                            updateParams.put(DaoConstants.ID, storeListWaitHandle.getStore_id());
                            StoreListWaitHandle waitHandleUpdate = new StoreListWaitHandle().setId(storeListWaitHandle.getId())
                                    .setIs_merged_name_address(MERGED);
                            //需要判断外卖是否活跃
                            if (storeListWaitHandle.getIs_takeout_active() != null && Objects.equals(storeListWaitHandle.getIs_takeout_active(), TAKEOUT_ACTIVE)) {
                                updateParams.put(Store.NAME, storeMergeInfo.getStoreName());
                                mergeStoreBiz.chooseSmartAddress(storeMergeInfo, store, updateParams, waitHandleUpdate);
                            } else if (storeListWaitHandle.getIs_takeout_active() != null && Objects.equals(storeListWaitHandle.getIs_takeout_active(), TAKEOUT_NOT_ACTIVE)) {
                                mergeStoreBiz.chooseCenterAddress(waitHandleUpdate, store);
                            }
                            waitHandleUpdate.setUpdate_params_name_address(JSON.toJSONString(updateParams));
                            waitHandleUpdate.setOriginal_name_address(JSON.toJSONString(mergeStoreBiz.getCenterOriginalAddressData(store)));
                            storeService.updateStore(updateParams);
                            waitHandleMapper.updateByPrimaryKeySelective(waitHandleUpdate);
                            StoreInfoChangeRequest request = new StoreInfoChangeRequest();
                            request.setStoreId(storeListWaitHandle.getStore_id());
                            request.setAddressChange(true);
                            request.setPoiChange(true);
                            request.setStoreNameChange(true);
                            request.setContactPhoneChange(false);
                            storeRemoteService.afterStoreInfoChange(request);
                        }


                    } else {
                        //任一需要发通知
                        needSendAddressNotice = true;
                        continue;
                    }
                } else {
                    //无需合并
                    waitHandleMapper.updateByPrimaryKeySelective(new StoreListWaitHandle().setId(storeListWaitHandle.getId()).setIs_merged_name_address(NO_NEED_MERGE));
                    continue;
                }
            }
        }
        if (needSendAddressNotice) {
            //发通知
            sideNoticeBiz.noticeMerchantMergeStore(merchantId, 30);
            //发通知后,更新目前所有记录的发通知时间
            waitHandleMapper.updateNameAddressNoticeTime(allIds, new Date());
        }
    }


    /**
     * 比较双方经纬度是否一致,至多到小数点前5位
     *
     * @param itude1
     * @param itude2
     * @return
     */
    public static boolean compareCoordinates(String itude1, String itude2) {
        // 处理空数据
        if (itude1 == null && itude2 == null) {
            return true;
        }
        if (itude1 == null || itude2 == null) {
            return false;
        }

        try {
            // 去除空格
            itude1 = itude1.trim();
            itude2 = itude2.trim();

            // 查找小数点位置
            int decimalIndex1 = itude1.indexOf('.');
            int decimalIndex2 = itude2.indexOf('.');

            // 如果没有小数点，直接比较整体字符串
            if (decimalIndex1 == -1 || decimalIndex2 == -1) {
                return itude1.equals(itude2);
            }

            // 确定前五位的范围
            int length1 = Math.min(decimalIndex1 + 6, itude1.length());
            int length2 = Math.min(decimalIndex2 + 6, itude2.length());

            // 获取前五位字符
            String subCoord1 = itude1.substring(0, length1);
            String subCoord2 = itude2.substring(0, length2);

            return subCoord1.equals(subCoord2);
        } catch (Exception e) {
            log.error("比较经纬度异常: ", e);
            // 捕获所有可能的异常
            return false;
        }
    }

}
