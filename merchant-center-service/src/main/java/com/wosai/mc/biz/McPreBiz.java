package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.McPreService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.xml.crypto.Data;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: l<PERSON><PERSON>qiang
 * @Date: 2020-08-27
 * @Description:
 */
@Component
public class McPreBiz {


    @Autowired
    private McPreService mcPreService;
    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    public void recordMcPre(String tableName, String bizId, String devCode, Map data) {
        String province = (String) data.get(Store.PROVINCE);
        String city = (String) data.get(Store.CITY);
        String district = (String) data.get(Store.DISTRICT);
        //都不为空才处理
        if (WosaiStringUtils.isNotBlank(province) &&
                WosaiStringUtils.isNotBlank(city) &&
                WosaiStringUtils.isNotBlank(district)) {

            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(province).append(" ").append(city).append(" ").append(district);
            District districtFull = districtsServiceV2.getCodeByName(stringBuffer.toString());
            if (districtFull != null) {
                data.put(Store.DISTRICT_CODE, districtFull.getCode());
            }
        }

        Map mcPre = new HashMap(16);
        mcPre.put("table_name", tableName);
        mcPre.put("biz_id", bizId);
        mcPre.put("dev_code", devCode);
        mcPre.put("data", JSON.toJSONString(data));
        //devCode不再有独立数据记录,只用来判断数据写入方向。是否含有devCode需要在调用前判断
        //Map beForeMcPre = mcPreService.findMcPre(devCode, tableName, bizId);
        Map beForeMcPre = mcPreService.findMcPre(tableName, bizId);
        if (MapUtils.isNotEmpty(beForeMcPre)) {
            mcPre.put("id", beForeMcPre.get("id"));
            mcPreService.updateMcPre(mcPre);
        } else {
            mcPreService.saveMcPre(mcPre);
        }
    }

    public void recordMcPreForList(String tableName, String bizId, String devCode, List<Map> data) {
        Map mcPre = new HashMap(16);
        mcPre.put("table_name", tableName);
        mcPre.put("biz_id", bizId);
        mcPre.put("dev_code", devCode);
        mcPre.put("data", JSON.toJSONString(data));

        Map beForeMcPre = mcPreService.findMcPre(tableName, bizId);
        if (MapUtils.isNotEmpty(beForeMcPre)) {
            mcPre.put("id", beForeMcPre.get("id"));
            mcPreService.updateMcPre(mcPre);
        } else {
            mcPreService.saveMcPre(mcPre);
        }
    }

    public void recordLicenseMcPre(String bizId, String devCode, Map data) {
        if (Objects.nonNull(data.get("id"))) {
            Map mcPre = findMcPre(TableNameEnum.LICENSE.getTableName(), bizId);
            if (WosaiMapUtils.isNotEmpty(mcPre)) {
                mcPre.put("dev_code", devCode);
                List<Map> list = JSONObject.parseArray(mcPre.get("data").toString(), Map.class);

                List<Map> alreadyExistLicense = list.stream().filter((map) -> WosaiStringUtils.equals((String) map.get("id"), (String) data.get("id"))).collect(Collectors.toList());
                if (WosaiCollectionUtils.isNotEmpty(alreadyExistLicense)) {
                    Map currentMap = alreadyExistLicense.get(0);
                    list.remove(currentMap);
                    currentMap.putAll(data);
                    list.add(currentMap);
                } else {
                    list.add(data);
                }
                mcPre.put("data", JSON.toJSONString(list));
                mcPreService.updateMcPre(mcPre);
            } else {
                mcPre = new HashMap(16);
                mcPre.put("table_name", TableNameEnum.LICENSE.getTableName());
                mcPre.put("biz_id", bizId);
                mcPre.put("dev_code", devCode);
                List<Map> list = Arrays.asList(data);
                mcPre.put("data", JSON.toJSONString(list));
                mcPreService.saveMcPre(mcPre);
            }

        }

    }

    public Map getMcPreDataWithMcId(String bizId, String devCode, String tableName) {
        if (StringUtils.isNotEmpty(devCode)) {
            //Map mcPre = mcPreService.findMcPre(devCode, tableName, bizId);
            Map mcPre = findMcPre(tableName, bizId);
            if (MapUtils.isNotEmpty(mcPre)) {
                Map data = JSONObject.parseObject(mcPre.get("data").toString(), Map.class);
                data.put("mcId", mcPre.get("id"));
                return data;
            }
        }
        return Maps.newHashMap();
    }


    public Map getMcPreData(String bizId, String devCode, String tableName) {
        if (StringUtils.isNotEmpty(devCode)) {
            //Map mcPre = mcPreService.findMcPre(devCode, tableName, bizId);
            Map mcPre = findMcPre(tableName, bizId);
            if (MapUtils.isNotEmpty(mcPre)) {
                return JSONObject.parseObject(mcPre.get("data").toString(), Map.class);
            }
        }
        return Maps.newHashMap();
    }

    /**
     * 许可证进入中间表后,一个 bizId 下 data字段有多个记录
     */
    public Map getMcPreListDataWithMcId(String bizId, String devCode, String tableName) {
        if (StringUtils.isNotEmpty(devCode)) {
            Map mcPre = mcPreService.findMcPre(tableName, bizId);

            if (MapUtils.isNotEmpty(mcPre)) {
                return mcPre;
            }

        }
        return null;
    }

    public List<Map> getMcPreDataList(String bizId, String devCode, String tableName) {
        Map mcPre = findMcPre(tableName, bizId);
        if (MapUtils.isNotEmpty(mcPre)) {
            return JSONObject.parseArray(mcPre.get("data").toString(), Map.class);
        }

        return new ArrayList<>();
    }

    /**
     * @param data 原表数据
     * @return 合并后的数据
     */
    public List<Map> mergeMcLicenseList(String bizId, List<Map> data, String tableName) {
        if (StringUtils.isNotEmpty(bizId)) {
            Map mcPreMap = mcPreService.findMcPre(tableName, bizId);
            if (WosaiMapUtils.isNotEmpty(mcPreMap)) {
                List<Map> mcPreList = JSONObject.parseArray(mcPreMap.get("data").toString(), Map.class);
                if (WosaiCollectionUtils.isNotEmpty(data)) {

                    for (Map map : data) {
//                        for (Map mcMap : mcPreList) {
//                            if (WosaiStringUtils.equals((String) map.get("id"), (String) mcMap.get("id"))) {
//                                map.putAll(mcMap);
//                                mcPreList.remove(mcMap);
//                            }
//                        }
                        Iterator<Map> iteratorMc = mcPreList.iterator();
                        while (iteratorMc.hasNext()) {
                            Map mcMap = iteratorMc.next();
                            //不为 null 才认为是之前的老数据,可以进行覆盖,否则认为是全新数据
                            if (WosaiStringUtils.equals((String) map.get("id"), (String) mcMap.get("id")) && mcMap.get("id") != null) {
                                map.putAll(mcMap);
                                iteratorMc.remove();
                            }
                        }
                    }
                    if (WosaiCollectionUtils.isNotEmpty(mcPreList)) {
                        data.addAll(mcPreList);
                    }
                } else {
                    data = mcPreList;
                }
            }
        }
        return data;
    }

    public Map mergeMcPre(String id, Map map, String tableName) {
        if (StringUtils.isNotEmpty(id)) {
            Map mcPre = findMcPre(tableName, id);
            if (MapUtils.isNotEmpty(mcPre)) {
                if (MapUtils.isNotEmpty(map)) {
                    Map data = JSONObject.parseObject(mcPre.get("data").toString(), Map.class);
                    map.putAll(data);
                } else {
                    map = mcPre;
                }
            }
        }
        return map;
    }

    public Map findMcPre(String tableName, String bizId) {
        return mcPreService.findMcPre(tableName, bizId);
    }

}