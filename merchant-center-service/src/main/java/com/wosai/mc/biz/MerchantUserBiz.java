package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.V2.*;
import com.wosai.app.dto.multi.req.*;
import com.wosai.app.dto.multi.resp.NaturalPersonResp;
import com.wosai.app.service.MultiMerchantService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.entity.converter.AccountReqConverter;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.UpdateMerchantComplete;
import com.wosai.mc.model.resp.BindAccountResp;
import com.wosai.mc.remote.IMerchantService;
import com.wosai.mc.utils.EnrolmentUtil;
import com.wosai.mc.utils.SmsBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.wosai.mc.constants.MerchantConstant.SUPER_ADMIN;
import static com.wosai.upay.core.model.user.Account.CELLPHONE;
import static com.wosai.upay.core.model.user.Account.PASSWORD;

/**
 * <AUTHOR>
 * @date 2021/4/15
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantUserBiz {

    final MerchantUserServiceV2 merchantUserServiceV2;

    final IMerchantService iMerchantService;

    final CommonApolloConfigs commonApolloConfigs;

    final UcUserAccountService ucUserAccountService;

    final MultiMerchantService multiMerchantService;

    final TagIngestService tagIngestService;

    final MerchantBusinessLicenseBiz licenseBiz;

    final SmsBiz sendSms;

    @Value("${tag.id}")
    private String tagId;

    public UcMerchantUserInfo createSuperAdmin(String platform, CreateMerchantReq merchant, AccountReq account, MerchantBusinessLicenseInfo merchantBusinessLicense, Boolean isLakala, Boolean needSendSms) {
        UcMerchantUserInfo superAdmin = null;
        String name = WosaiStringUtils.isNotEmpty(merchant.getBusinessName()) ? merchant.getBusinessName() : merchant.getName();
        try {
            boolean needSendSmsFlag = Objects.isNull(needSendSms) || Boolean.TRUE.equals(needSendSms);
            if (WosaiStringUtils.isEmpty(account.getUcUserId())) {
                String cellphone = account.getIdentifier();
                UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(cellphone);
                if (ucUserInfo != null) {
                    ucUserAccountService.deleteUcUserByCellphone(cellphone);
                }
                CreateUcUserAccountReq createUcUserAccountReq = AccountReqConverter.INSTANCE.do2dto(account);
                String loginPassword = EnrolmentUtil.randomEdge(6);
                createUcUserAccountReq.setPassword(loginPassword);
                createUcUserAccountReq.setStatus(-1);
                CreateMerchantUserV2Req createMerchantUserV2Req = new CreateMerchantUserV2Req();
                createMerchantUserV2Req.setUcUser(createUcUserAccountReq);
                createMerchantUserV2Req.setMerchant_id(merchant.getId());
                createMerchantUserV2Req.setName(name);
                createMerchantUserV2Req.setRole(SUPER_ADMIN);
                //TODO
                //此处调用merchant-user新增的接口createMerchantUserNoCheckBlackList,接口逻辑与createMerchantUser相同，区别就是没有校验手机号黑名单
                superAdmin = merchantUserServiceV2.createMerchantUserNoCheckBlackList(createMerchantUserV2Req);
                if (commonApolloConfigs.isForceRealNameTag() && WosaiStringUtils.isNotEmpty(merchantBusinessLicense.getLegal_person_id_number()) && merchantBusinessLicense.getType() <= 1) {
                    tagIngestService.ingest(Collections.singletonList(superAdmin.getUcUserInfo().getUc_user_id()), tagId, "强制实名");
                }
                // 小程序自助入网打强制实名标签
                if (SmsBiz.MINI_PLATFORM.equals(platform)) {
                    tagIngestService.ingest(Collections.singletonList(superAdmin.getUcUserInfo().getUc_user_id()), tagId, "强制实名");
                }
                // 1: 小微个体取证件号，个体若有负责人证件号取负责人，没有则取法人证件号判断实名信息是否存在；
                // 2: 存在就去绑定，不存在就去创建并绑定
                createAndBindNaturalPerson(account, merchantBusinessLicense, superAdmin.getUcUserInfo().getUc_user_id());
                String managerPassword = EnrolmentUtil.random();
                Map<String, Object> vars = new HashMap<>(3);
                vars.put(CELLPHONE, createUcUserAccountReq.getCellphone());
                vars.put(PASSWORD, loginPassword);
                vars.put("authcode", managerPassword);
                iMerchantService.updateMerchantManagerPasswordWithNew(merchant.getId(), managerPassword);
                multiMerchantService.setManagerPassword(new ManagerPasswordReq().setUc_user_id(superAdmin.getUcUserInfo().getUc_user_id()).setPassword(managerPassword).setStatus(false));
                if (needSendSmsFlag){
                    sendSms.send(platform, createUcUserAccountReq.getCellphone(), MerchantConstant.TEMPLATE_AND_MANAGER, vars);
                }
            } else {
                List<UcMerchantUserSimpleInfo> merchantUserInfos = merchantUserServiceV2.getSimpleInfoByUcUserId(account.getUcUserId());
                // 绑定的情况 1: 创建merchant_user  2: 如果没有自然人信息就要去创建并绑定
                CreateUcMerchantUserSimpleReq simpleReq = new CreateUcMerchantUserSimpleReq();
                simpleReq.setUc_user_id(account.getUcUserId());
                simpleReq.setMerchant_id(merchant.getId());
                simpleReq.setName(name);
                simpleReq.setRole(SUPER_ADMIN);
                simpleReq.setIsLkl(isLakala);
                superAdmin = merchantUserServiceV2.createMerchantUserSimple(simpleReq);
                createAndBindNaturalPerson(account, merchantBusinessLicense, superAdmin.getUcUserInfo().getUc_user_id());
                // 如果该账户是空的，则去发密码
                if (WosaiCollectionUtils.isEmpty(merchantUserInfos)) {
                    log.info("绑定空用户更改密码并发送短信, merchant_id:{} uc_user_id:{}", merchant.getId(), account.getUcUserId());
                    String loginPassword = EnrolmentUtil.randomEdge(6);
                    String managerPassword = EnrolmentUtil.random();
                    Map<String, Object> vars = new HashMap<>(3);
                    vars.put(CELLPHONE, superAdmin.getUcUserInfo().getCellphone());
                    vars.put(PASSWORD, loginPassword);
                    vars.put("authcode", managerPassword);
                    // 更改密码
                    ucUserAccountService.updatePassword(new UpdateUcUserPasswordReq().setUc_user_id(account.getUcUserId()).setNew_password(loginPassword));
                    multiMerchantService.setManagerPassword(new ManagerPasswordReq().setUc_user_id(account.getUcUserId()).setPassword(managerPassword).setStatus(false));
                    iMerchantService.updateMerchantManagerPasswordWithNew(merchant.getId(), managerPassword);
                    if (needSendSmsFlag){
                        sendSms.send(platform, superAdmin.getUcUserInfo().getCellphone(), MerchantConstant.TEMPLATE_AND_MANAGER, vars);
                    }
                }
            }
        } catch (Exception e) {
            if (superAdmin != null) {
                merchantUserServiceV2.deleteMerchantUser(superAdmin.getMerchant_user_id());
            }
            throw e;
        }
        return superAdmin;
    }

    public void checkUcUserId(AccountReq account) {
        if (WosaiStringUtils.isEmpty(account.getUcUserId())) {
            List<UcMerchantUserSimpleInfo> simpleInfoByCellphone = merchantUserServiceV2.getSimpleInfoByCellphone(account.getIdentifier());
            if (WosaiCollectionUtils.isNotEmpty(simpleInfoByCellphone)) {
                throw new CommonInvalidParameterException("账号已存在");
            }
        } else {
            UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(account.getUcUserId());
            if (ucUserInfo == null) {
                UcUserInfo info = ucUserAccountService.getUcUserByCellphone(account.getIdentifier());
                if (info != null) {
                    throw new CommonInvalidParameterException("账号对应用户已存在");
                } else {
                    account.setUcUserId(null);
                }
            }
        }
    }

    public void createOrUpdateAccount(UpdateMerchantComplete complete) {
        String merchantId = complete.getMerchant().getId();
        String platform = complete.getPlatform();
        MerchantBusinessLicenseInfo licenseInfo = JSON.parseObject(JSON.toJSONString(complete.getLicense()), MerchantBusinessLicenseInfo.class);
        UcMerchantUserInfo ucMerchantUserInfo = merchantUserServiceV2.getSuperAdminByMerchantId(merchantId);
        // 旧版app走到这个逻辑
        if (complete.getAccount() == null) {
            AccountReq accountReq = new AccountReq().setIdentifier(ucMerchantUserInfo.getUcUserInfo().getCellphone());
            this.createAndBindNaturalPerson(accountReq, licenseInfo, ucMerchantUserInfo.getUcUserInfo().getUc_user_id());
            return;
        }
        // 以下是多商户的app逻辑
        // TODO 手机号没变，可能要改实名信息了
        if (ucMerchantUserInfo.getUcUserInfo().getCellphone().equals(complete.getAccount().getIdentifier())) {
            this.createAndBindNaturalPerson(complete.getAccount(), licenseInfo, ucMerchantUserInfo.getUcUserInfo().getUc_user_id());
        } else {
            // 换了新的手机号
            this.checkUcUserId(complete.getAccount());
            String ucUserId = complete.getAccount().getUcUserId();
            if (WosaiStringUtils.isNotEmpty(ucUserId)) {
                // (已经存在的账号)，要更换merchant_user中的用户id了
                multiMerchantService.changeUcUser(new UserReq().setMerchant_user_id(ucMerchantUserInfo.getMerchant_user_id()).setUc_user_id(ucUserId));
                this.createAndBindNaturalPerson(complete.getAccount(), licenseInfo, ucUserId);
            } else {
                // 判断旧的手机号有几个merchant_user，如果只有一个而且是自己的话就去changeCellphone
                List<UcMerchantUserSimpleInfo> simpleInfoByUcUserId = merchantUserServiceV2.getSimpleInfoByUcUserId(ucMerchantUserInfo.getUcUserInfo().getUc_user_id());
                if (simpleInfoByUcUserId.size() == 1 && simpleInfoByUcUserId.get(0).getMerchant_id().equals(merchantId)) {
                    changeCellphone(platform, complete.getAccount(), licenseInfo, merchantId, ucMerchantUserInfo.getUcUserInfo().getUc_user_id());
                } else {
                    changeUcUser(platform, complete.getAccount(), licenseInfo, merchantId, ucMerchantUserInfo.getMerchant_user_id());
                }
            }

        }
    }

    /**
     * 如果账号是单商户的话，就去changeCellphone
     *
     * @param platform    提交平台
     * @param accountReq  新的手机号等参数
     * @param licenseInfo 营业执照等信息
     * @param merchantId  商户id
     * @param ucUserId    旧用户的uc_user_id
     */
    private void changeCellphone(String platform, AccountReq accountReq, MerchantBusinessLicenseInfo licenseInfo, String merchantId, String ucUserId) {
        String loginPassword = EnrolmentUtil.randomEdge(6);
        ChangeUcUserCellphoneReq cellphoneReq = new ChangeUcUserCellphoneReq();
        cellphoneReq.setUc_user_id(ucUserId);
        cellphoneReq.setNew_cellphone(accountReq.getIdentifier());
        cellphoneReq.setNew_password(loginPassword);
        ucUserAccountService.changeCellphone(cellphoneReq);
        this.createAndBindNaturalPerson(accountReq, licenseInfo, ucUserId);
        Map<String, Object> vars = new HashMap<>(3);
        String managerPassword = EnrolmentUtil.random();
        vars.put(CELLPHONE, accountReq.getIdentifier());
        vars.put(PASSWORD, loginPassword);
        vars.put("authcode", managerPassword);
        iMerchantService.updateMerchantManagerPasswordWithNew(merchantId, managerPassword);
        multiMerchantService.setManagerPassword(new ManagerPasswordReq().setUc_user_id(ucUserId).setPassword(managerPassword).setStatus(false));
        sendSms.send(platform, accountReq.getIdentifier(), MerchantConstant.TEMPLATE_AND_MANAGER, vars);
    }

    /**
     * 第二次传过来一个新的手机号，并且原来老板账号是多商户的账号，需要创建一个新的ucUser，并且绑定到原来的merchant_user上去
     *
     * @param accountReq     新的手机号等参数
     * @param licenseInfo    营业执照信息
     * @param merchantId     商户id
     * @param merchantUserId 老板的merchant_user_id
     */
    private void changeUcUser(String platform, AccountReq accountReq, MerchantBusinessLicenseInfo licenseInfo, String merchantId, String merchantUserId) {
        String newCellphone = accountReq.getIdentifier();
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(newCellphone);
        if (ucUserInfo != null) {
            ucUserAccountService.deleteUcUserByCellphone(newCellphone);
        }
        CreateUcUserAccountReq createUcUserAccountReq = new CreateUcUserAccountReq();
        createUcUserAccountReq.setCellphone(accountReq.getIdentifier());
        createUcUserAccountReq.setIdentity_type(accountReq.getIdentityType());
        String loginPassword = EnrolmentUtil.randomEdge(6);
        createUcUserAccountReq.setPassword(loginPassword);
        UcUserInfo ucUser = ucUserAccountService.createUcUser(createUcUserAccountReq);
        if (commonApolloConfigs.isForceRealNameTag() && WosaiStringUtils.isNotEmpty(licenseInfo.getLegal_person_id_number()) && licenseInfo.getType() <= 1) {
            tagIngestService.ingest(Collections.singletonList(ucUser.getUc_user_id()), tagId, "强制实名");
        }
        // 小程序自助入网打强制实名标签
        if (SmsBiz.MINI_PLATFORM.equals(platform)) {
            tagIngestService.ingest(Collections.singletonList(ucUser.getUc_user_id()), tagId, "强制实名");
        }
        // TODO 再将这个用户id绑定到原来的那个merchant_user 创建或者是绑定自然人信息
        multiMerchantService.changeUcUser(new UserReq().setMerchant_user_id(merchantUserId).setUc_user_id(ucUser.getUc_user_id()));
        this.createAndBindNaturalPerson(accountReq, licenseInfo, ucUser.getUc_user_id());
        Map<String, Object> vars = new HashMap<>(3);
        String managerPassword = EnrolmentUtil.random();
        vars.put(CELLPHONE, createUcUserAccountReq.getCellphone());
        vars.put(PASSWORD, loginPassword);
        vars.put("authcode", managerPassword);
        iMerchantService.updateMerchantManagerPasswordWithNew(merchantId, managerPassword);
        multiMerchantService.setManagerPassword(new ManagerPasswordReq().setUc_user_id(ucUser.getUc_user_id()).setPassword(managerPassword).setStatus(false));
        sendSms.send(platform, createUcUserAccountReq.getCellphone(), MerchantConstant.TEMPLATE_AND_MANAGER, vars);
    }

    /**
     * 关于自然人之间操作
     *
     * @param accountReq
     * @param licenseInfo
     */
    private void createAndBindNaturalPerson(AccountReq accountReq, MerchantBusinessLicenseInfo licenseInfo, String ucUserId) {
        if (WosaiStringUtils.isEmpty(licenseInfo.getLegal_person_id_number())) {
            return;
        }
        BaseReq baseReq = new BaseReq();
        baseReq.setUc_user_id(ucUserId);
        NaturalPersonResp ucNaturalPersonResp = multiMerchantService.getRealNameInfoByUcUserId(baseReq);
        UcNaturalPersonReq ucNaturalPersonReq = this.buildUcNaturalPersonReq(accountReq, licenseInfo, ucUserId);
        // 如果用户对应的实名信息为空 || 用户对应的实名信息和此次创建的实名信息不一致  --> 去创建并绑定
        if (ucNaturalPersonResp == null || !ucNaturalPersonReq.getIdentity_no().equals(ucNaturalPersonResp.getIdentity_no())) {
            // 创建并绑定自然人信息
            NaturalPersonResp realNameInfo = multiMerchantService.createRealNameInfo(ucNaturalPersonReq);
            BindRealNameReq req = new BindRealNameReq();
            req.setUc_user_id(ucUserId);
            req.setNatural_person_id(realNameInfo.getNatural_person_id());
            multiMerchantService.bindRealNameInfo(req);
        }
    }

    private UcNaturalPersonReq buildUcNaturalPersonReq(AccountReq accountReq, MerchantBusinessLicenseInfo licenseInfo, String ucUserId) {
        UcNaturalPersonReq ucNaturalPersonReq = new UcNaturalPersonReq();
        if (WosaiStringUtils.isNotEmpty(accountReq.getIdNumber())) {
            ucNaturalPersonReq.setName(accountReq.getName())
                    .setIdentity_type(accountReq.getIdType())
                    .setIdentity_no(accountReq.getIdNumber())
                    .setIdentity_front_photo(accountReq.getIdCardFrontPhoto())
                    .setIdentity_back_photo(WosaiStringUtils.isEmpty(accountReq.getIdCardBackPhoto()) ? accountReq.getIdCardFrontPhoto() : accountReq.getIdCardBackPhoto())
                    .setIdentity_validity(accountReq.getIdCardValidity())
                    .setBirthday(accountReq.getIdType() == 1 ? accountReq.getIdNumber().substring(6, 14) : null)
                    .setIdentity_address(accountReq.getIdCardAddress())
                    .setIdentity_issuing_authority(accountReq.getIdCardIssuingAuthority())
                    .setAuth_status(0)
                    .setCellphone(accountReq.getIdentifier())
                    .setUc_user_id(ucUserId);
        } else {
            ucNaturalPersonReq.setName(licenseInfo.getLegal_person_name())
                    .setIdentity_type(licenseInfo.getLegal_person_id_type())
                    .setIdentity_no(licenseInfo.getLegal_person_id_number())
                    .setIdentity_front_photo(licenseInfo.getLegal_person_id_card_front_photo())
                    .setIdentity_back_photo(WosaiStringUtils.isEmpty(licenseInfo.getLegal_person_id_card_back_photo()) ? licenseInfo.getLegal_person_id_card_front_photo() : licenseInfo.getLegal_person_id_card_back_photo())
                    .setIdentity_validity(licenseInfo.getId_validity())
                    .setBirthday(licenseInfo.getLegal_person_id_type() == 1 ? licenseInfo.getLegal_person_id_number().substring(6, 14) : null)
                    .setIdentity_address(licenseInfo.getLegal_person_id_card_address())
                    .setIdentity_issuing_authority(licenseInfo.getLegal_person_id_card_issuing_authority())
                    .setAuth_status(0)
                    .setCellphone(accountReq.getIdentifier())
                    .setUc_user_id(ucUserId);
        }
        return ucNaturalPersonReq;
    }

    public Map<String, BindAccountResp> getAuthBindAccountByIdentity(String identity) {
        NaturalPersonResp realNameInfoByIdentityNo = multiMerchantService.getRealNameInfoByIdentityNo(new IdentityNoReq().setIdentity_no(identity));
        if (realNameInfoByIdentityNo == null || realNameInfoByIdentityNo.getAuth_status() == 0) {
            return new HashMap<>(1);
        }
        List<UcUserInfo> ucUserInfos = ucUserAccountService.queryUcUserByNaturalPersonId(realNameInfoByIdentityNo.getNatural_person_id());
        if (WosaiCollectionUtils.isEmpty(ucUserInfos)) {
            return new HashMap<>(1);
        }
        Map<String, BindAccountResp> result = new HashMap<>(ucUserInfos.size());
        for (UcUserInfo ucUserInfo : ucUserInfos) {
            if (ucUserInfo.getStatus() != 0) {
                BindAccountResp bindAccountResp = new BindAccountResp();
                bindAccountResp.setUc_user_id(ucUserInfo.getUc_user_id());
                bindAccountResp.setCellphone(ucUserInfo.getCellphone());
                result.put(ucUserInfo.getUc_user_id(), bindAccountResp);
            }
        }
        return result;
    }
}
