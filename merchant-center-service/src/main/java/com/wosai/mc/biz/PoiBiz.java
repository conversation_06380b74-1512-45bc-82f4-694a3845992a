package com.wosai.mc.biz;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreReq;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.sales.model.gaoDe.GetPoiDetailByAddressReq;
import com.wosai.sales.model.gaoDe.GetPoiDetailByAddressResp;
import com.wosai.sales.model.gaoDe.PoiDetail;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 处理经纬度相关逻辑
 *
 * <AUTHOR>
 * @date 2024/8/21
 */
@Component
@Slf4j
public class PoiBiz {

    @Autowired
    private GaoDeService gaoDeService;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;


    /**
     * 检查并补充经纬度
     *
     * @param req
     */
    public void checkAndFillPoi(CreateMerchantReq req) {
        if (StringUtils.isNotEmpty(req.getDistrictCode())) {
            District dis = districtsServiceV2.getDistrict(MapUtil.hashMap("code", req.getDistrictCode()));
            if (Objects.isNull(dis) || Objects.equals(0, dis.getStatus())) {
                throw new com.wosai.common.exception.CommonPubBizException("商户省市区无效");
            }
            req.setProvince(dis.getProvince_name())
                    .setCity(dis.getCity_name())
                    .setDistrict(dis.getName());
        }

        if (isPointInChina(req.getLongitude(), req.getLatitude())) {
            return;
        }

        Optional<PoiDetail> poiDetailOptional = getPoiDetailByCompleteAddress(req.getProvince(), req.getCity(), req.getDistrict(), req.getStreetAddress());
        if (poiDetailOptional.isPresent()) {
            PoiDetail poiDetail = poiDetailOptional.get();
            req.setLongitude(poiDetail.getLongitude());
            req.setLatitude(poiDetail.getLatitude());
        }
    }

    /**
     * 检查并补充经纬度
     *
     * @param req
     */
    public void checkAndFillPoi(CreateStoreReq req) {
        if (StringUtils.isNotEmpty(req.getDistrictCode())) {
            District dis = districtsServiceV2.getDistrict(MapUtil.hashMap("code", req.getDistrictCode()));
            if (Objects.isNull(dis) || Objects.equals(0, dis.getStatus())) {
                throw new com.wosai.common.exception.CommonPubBizException("商户省市区无效");
            }
            req.setProvince(dis.getProvince_name())
                    .setCity(dis.getCity_name())
                    .setDistrict(dis.getName());
        }

        if (isPointInChina(req.getLongitude(), req.getLatitude())) {
            return;
        }

        Optional<PoiDetail> poiDetailOptional = getPoiDetailByCompleteAddress(req.getProvince(), req.getCity(), req.getDistrict(), req.getStreetAddress());
        if (poiDetailOptional.isPresent()) {
            PoiDetail poiDetail = poiDetailOptional.get();
            req.setLongitude(poiDetail.getLongitude());
            req.setLatitude(poiDetail.getLatitude());
        }
    }


    private final double MIN_LONGITUDE = 73.66;
    private final double MAX_LONGITUDE = 135.05;
    private final double MIN_LATITUDE = 18.10;
    private final double MAX_LATITUDE = 53.57;

    /**
     * 简单判断经纬度是否在中国，不包含南沙群岛
     * 不精确，只能用来排除比较离谱的经纬度
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return
     */
    public boolean isPointInChina(String longitude, String latitude) {
        try {
            if (WosaiStringUtils.isEmptyAny(longitude, latitude)) {
                return false;
            }
            Double lo = Double.valueOf(longitude);
            Double la = Double.valueOf(latitude);
            return lo >= MIN_LONGITUDE && lo <= MAX_LONGITUDE
                    && la >= MIN_LATITUDE && la <= MAX_LATITUDE;
        } catch (Exception e) {
            log.error("isPointInChina error {} {}", longitude, latitude, e);
            return false;
        }
    }

    /**
     * 通过地址换取poi点位
     *
     * @param province      省
     * @param city          市
     * @param district      区
     * @param streetAddress 街道地址
     * @return poi点位，高德返回多个时取第一个
     */
    public Optional<PoiDetail> getPoiDetailByCompleteAddress(String province, String city, String district, String streetAddress) {
        if (WosaiStringUtils.isEmptyAny(province, city, streetAddress)) {
            return Optional.empty();
        }
        GetPoiDetailByAddressReq req = new GetPoiDetailByAddressReq();
        req.setBusiness("支付业务");
        req.setScene("进件");
        req.setComplete_address(String.format("%s%s%s%s", province, city, district, streetAddress));
        GetPoiDetailByAddressResp resp = gaoDeService.getPoiDetailByCompleteAddress(req);
        return Optional.ofNullable(resp.getPoiDetails())
                .orElse(Collections.emptyList())
                .stream()
                .findFirst();
    }


}
