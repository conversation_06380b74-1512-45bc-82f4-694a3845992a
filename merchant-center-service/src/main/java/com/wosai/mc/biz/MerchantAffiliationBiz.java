package com.wosai.mc.biz;

import com.wosai.mc.mapper.MerchantAffiliationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/2/29
 */
@Component
public class MerchantAffiliationBiz {

    @Autowired
    private MerchantAffiliationMapper mapper;

    public boolean hasAffiliation(String merchantSn) {
        return Objects.nonNull(mapper.selectByMerchantSn(merchantSn));
    }
}
