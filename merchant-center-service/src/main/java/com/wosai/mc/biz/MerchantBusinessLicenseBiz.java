package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.constants.BankAccountEnum;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.GetMerchantAndLicenseReq;
import com.wosai.mc.model.req.UpdateLicenseReq;
import com.wosai.mc.model.req.UpdateMerchantBusinessLicenseReq;
import com.wosai.mc.service.LicenseService;
import com.wosai.mc.utils.*;
import com.wosai.model.wechat.MchAuthApply;
import com.wosai.service.WechatAuthService;
import com.wosai.upay.bank.info.api.model.BusinessLicenseId;
import com.wosai.upay.bank.info.api.service.PayBusinessConfigurationService;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.merchant.audit.api.pojo.resp.MerchantAuditInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/18
 */
@Component
@Slf4j
public class MerchantBusinessLicenseBiz {

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantBiz merchantBiz;

    @Autowired
    private McPreBiz mcPreBiz;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private PayBusinessConfigurationService payBusinessConfigurationService;

    @Autowired
    private CommonApolloConfigs apolloConfigs;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private WechatAuthService wechatAuthService;

    private static final String LONG_VALIDITY = "********";
    private static final int YEAR_5 = 5;
    private static final int YEAR_10 = 10;
    private static final int YEAR_20 = 20;
    private static final int DAY_0 = 0;
    private static final int DAY_1 = 1;

    public void updateMerchantBusinessLicense(UpdateMerchantBusinessLicenseReq req, String devCode) {
        Map merchantBusinessLicense = MyBeanUtil.toMap(req);
        merchantBusinessLicense.remove("trade_license_list");
        List<UpdateLicenseReq> trade_license_list = req.getTrade_license_list();
        //原表营业执照信息

        MerchantBusinessLicenseInfo licenseInfo = getMerchantBusinessLicenseByMerchantId(req.getMerchantId(), devCode, false);
        Map<String, Object> businessLicenseByMerchantId = JSON.parseObject(JSON.toJSONString(licenseInfo), Map.class);
        businessLicenseByMerchantId.remove("trade_license_list");
        if (StringUtils.isNotEmpty(devCode)) {
            businessLicenseByMerchantId.putAll(merchantBusinessLicense);
            mcPreBiz.recordMcPre(TableNameEnum.MERCHANT_BUSINESS_LICENSE.getTableName(), req.getMerchantId(), devCode, businessLicenseByMerchantId);

            licenseService.updateLicense(trade_license_list, MapUtils.getString(businessLicenseByMerchantId, "id"), devCode);

        } else {
            //更新原表
            merchantBusinessLicenseService.updateMerchantBusinessLicense(merchantBusinessLicense);
            licenseService.updateLicense(trade_license_list, MapUtils.getString(businessLicenseByMerchantId, "id"), null);
            //检查中间表
            Map mcPre = mcPreBiz.findMcPre(TableNameEnum.MERCHANT_BUSINESS_LICENSE.getTableName(), req.getMerchantId());
            if (WosaiMapUtils.isNotEmpty(mcPre)) {
                Map data = CommonUtils.beanToTargetObj(mcPre.get("data"), Map.class);
                if (WosaiMapUtils.isNotEmpty(data)) {
                    data.putAll(merchantBusinessLicense);
                    mcPreBiz.recordMcPre(TableNameEnum.MERCHANT_BUSINESS_LICENSE.getTableName(), req.getMerchantId(), "updateOrigin", data);
                }
            }

        }
    }

    public MerchantBusinessLicenseInfo getMerchantBusinessLicenseByMerchantId(String merchantId, String devCode, boolean needHandlePhotos) {
        Map<String, Object> businessLicense = null;
        List<Map> license = null;
        if (StringUtils.isNotEmpty(devCode)) {
            businessLicense = mcPreBiz.getMcPreData(merchantId, devCode, TableNameEnum.MERCHANT_BUSINESS_LICENSE.getTableName());
            if (MapUtils.isNotEmpty(businessLicense) && needHandlePhotos) {
                CommonUtils.handPhotos(businessLicense, "photo");
                CommonUtils.handPhotos(businessLicense, "legal_person_id_card_front_photo");
                CommonUtils.handPhotos(businessLicense, "legal_person_id_card_back_photo");
                CommonUtils.handPhotos(businessLicense, "trade_license");
            }
            if (MapUtils.isNotEmpty(businessLicense)) {
                license = licenseService.getLicenseByBusinessLicenseId(MapUtils.getInteger(businessLicense, "id").toString(), devCode);
                businessLicense.put("trade_license_list", license);
            }
        }
        //以下为查询原表逻辑
        if (MapUtils.isEmpty(businessLicense)) {
            businessLicense = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
            if (MapUtils.isEmpty(businessLicense)) {
                return null;
            }
            license = licenseService.getLicenseByBusinessLicenseId(MapUtils.getInteger(businessLicense, "id").toString(), devCode);
            businessLicense.put("trade_license_list", license);
        }

        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = JSON.parseObject(JSON.toJSONString(businessLicense), MerchantBusinessLicenseInfo.class);
        oldDataHandler(merchantBusinessLicenseInfo);
        return merchantBusinessLicenseInfo;

    }

    /**
     * 拼一个许可证的照片(如果新表有许可证)到老字段
     * <p>
     * 如果新表没值,则将老字段清空
     */
    public void oldDataHandler(MerchantBusinessLicenseInfo merchantBusinessLicenseInfo) {
        if (Objects.isNull(merchantBusinessLicenseInfo)) {
            return;
        }
        if (WosaiCollectionUtils.isNotEmpty(merchantBusinessLicenseInfo.getTrade_license_list())) {
            merchantBusinessLicenseInfo.setTradeLicense(merchantBusinessLicenseInfo.getTrade_license_list().get(0).getLicense_photo());
        } else {
            merchantBusinessLicenseInfo.setTradeLicense("");
        }
    }

    public List<Map> getLicenseByNumberOrLegalPersonIdNumber(GetMerchantAndLicenseReq req) {
        if (WosaiStringUtils.isEmpty(req.getLegalPersonIdNumber()) && WosaiStringUtils.isEmpty(req.getNumber())) {
            throw new CommonInvalidParameterException("营业执照号和证件号不能同时为空");
        }
        HashMap<String, String> map = new HashMap<>(2);
        map.put("legal_person_id_number", req.getLegalPersonIdNumber());
        map.put("number", req.getNumber());
        PageInfo pageInfo = new PageInfo(1, 200, null, null, Collections.singletonList(new OrderBy("mtime", OrderBy.OrderType.DESC)));
        return merchantBusinessLicenseService.getBusinessLicenseByLegalPersonIdNumberOrNumber(pageInfo, map).getRecords();
    }

    /**
     * 前端用的营业执照、认证记录信息的字段不一样，需要同步成认证记录里的字段
     *
     * @param merchantBusinessLicense
     */
    public Map syncMerchantAudit(MerchantBusinessLicense merchantBusinessLicense, MerchantAuditInfo merchantAuditInfo, Map businessLicenseAudit) {
        if (merchantBusinessLicense == null) {
            return new HashMap();
        }
        Map licenseMap = objectMapper.convertValue(merchantBusinessLicense, Map.class);
        Map merchantAudit = objectMapper.convertValue(merchantAuditInfo, Map.class);
        Object IdType = licenseMap.remove("legal_person_id_type");
        Object licenseAddress = licenseMap.remove("address");
        Object licenseType = licenseMap.remove("type");
        licenseMap.putIfAbsent("id_type", String.valueOf(IdType));
        licenseMap.putIfAbsent("license_address", String.valueOf(licenseAddress));
        licenseMap.putIfAbsent("license_type", String.valueOf(licenseType));

        int businessLicenseStatus = MapUtils.getIntValue(merchantAudit, "business_license_status");
        if (!this.isBusinessLicenseComplete(merchantBusinessLicense)) {
            businessLicenseStatus = 3;
        }
        //没有营业执照审核记录,就为 [未提交]
        if (businessLicenseAudit == null) {
            businessLicenseStatus = 2;
        }
        licenseMap.putIfAbsent("business_license_status", businessLicenseStatus);
        return licenseMap;

    }

    /**
     * 营业执照是否完整
     *
     * @param merchantBusinessLicense
     * @return
     */
    public boolean isBusinessLicenseComplete(MerchantBusinessLicense merchantBusinessLicense) {
        if (merchantBusinessLicense == null) {
            return false;
        }
        Integer type = merchantBusinessLicense.getLegal_person_id_type();
        if (type == null) {
            return false;
        }
        return isAllNotBlank(merchantBusinessLicense) && isAuthorizationNotBlank(merchantBusinessLicense) &&
                isValidateValid(merchantBusinessLicense);
    }

    /**
     * 校验字段是否完整
     */
    private boolean isAllNotBlank(MerchantBusinessLicense merchantBusinessLicense) {
        boolean validBlank = !StringUtils.isAnyBlank(
                merchantBusinessLicense.getPhoto(),
                merchantBusinessLicense.getNumber(),
                merchantBusinessLicense.getName(),
                merchantBusinessLicense.getAddress(),
                merchantBusinessLicense.getValidity(),
                merchantBusinessLicense.getLegal_person_id_number(),
                merchantBusinessLicense.getLegal_person_id_card_front_photo(),
                merchantBusinessLicense.getId_validity(),
                merchantBusinessLicense.getLegal_person_name());
        return validBlank;
    }

    /**
     * 授权涵是否完整
     */
    private boolean isAuthorizationNotBlank(MerchantBusinessLicense merchantBusinessLicense) {
        try {
            String merchantId = merchantBusinessLicense.getMerchant_id();
            String legalPersonName = merchantBusinessLicense.getLegal_person_name();
            MerchantBankAccount merchantBankAccount = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(merchantId);
            if (merchantBankAccount == null) {
                return true;
            }
            String holder = merchantBankAccount.getHolder();
            if (merchantBankAccount.getType().equals(Integer.valueOf(1)) && (!StringUtils.equals(holder, legalPersonName))) {
                return StringUtils.isNotBlank(merchantBusinessLicense.getLetter_of_authorization());
            }
        } catch (Exception e) {
            log.error("getMerchantBankAccountByMerchantId error : ", e);
        }
        return true;
    }

    /**
     * 日期是否满足要求
     */
    private boolean isValidateValid(MerchantBusinessLicense merchantBusinessLicense) {
        return RegularUtil.isFormatDate(merchantBusinessLicense.getId_validity()) &&
                RegularUtil.isFormatDate(merchantBusinessLicense.getValidity());
    }


    /**
     * 如果营业执照类型==0,不返回营业执照信息给前端了
     *
     * @param licenseMap
     */
    public void cleanUpIfNonLicense(Map licenseMap) {
        if (MapUtils.isNotEmpty(licenseMap) && StringUtils.equals(MapUtils.getString(licenseMap, "license_type"), "0")) {
            licenseMap.remove("ctime");
            licenseMap.remove("mtime");
            licenseMap.remove("version");
            licenseMap.remove("id");

            licenseMap.put("photo", "");
            licenseMap.put("number", "");
            licenseMap.put("name", "");
            licenseMap.put("validity", "");
            licenseMap.put("letter_of_authorization", "");
            licenseMap.put("trade_license", null);
            licenseMap.put("legal_person_id_number", "");
            licenseMap.put("legal_person_id_card_front_photo", "");
            licenseMap.put("legal_person_id_card_back_photo", "");
            licenseMap.put("id_validity", "");
            licenseMap.put("legal_person_name", "");
            licenseMap.put("license_address", "");
        }
    }


    public boolean haveSuccessApply(String merchantId) {
        MerchantInfo merchant = merchantBiz.getMerchantInfoById(merchantId, null);
        List<MchAuthApply> allSuceessApplies = wechatAuthService.getAllSuceessApplies(merchant.getSn());
        //商户入网在21年7月之后 或者 有通过的个体/组织类型的微信申请单
        long time = DateTimeUtil.getTime("2021-07-01", DateTimeUtil.FORMAT_DAY);
        return merchant.getCtime() > time ||
                Optional.ofNullable(allSuceessApplies).orElseGet(ArrayList::new).stream().anyMatch(mchAuthApply -> !mchAuthApply.getForceAuth());
    }


    /**
     * 提交前待校验
     */
    public void checkLicenseAndBankInfo(Map map) {
        String merchantId = WosaiMapUtils.getString(map, "merchant_id");
        MerchantBankAccount merchantBankAccount = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(merchantId);
        // 营业执照类型校验
        if (MapUtils.getIntValue(map, "license_type") <= 0) {
            throw new CommonInvalidParameterException("营业执照类型不合法");
        }
        String licenseName = MapUtils.getString(map, "name");

        //对公、提交过来的营业执照名称必须以结算账户名称开头
        if (merchantBankAccount.getType() == BankAccountEnum.BUSINESS.getCode() && StringUtils.isNotBlank(licenseName) &&
                StringUtils.isNotBlank(merchantBankAccount.getHolder()) && !StringUtils.startsWith(merchantBankAccount.getHolder(), licenseName)) {
            throw new CommonPubBizException("营业证照名称与对公账户名称不匹配，不允许修改，请走银行卡变更审批申请变更");
        }
        //判断营业执照类型是否跟间连支付账户类型匹配
        isLicenseMatchAccount(merchantBankAccount.getType(), MapUtils.getLongValue(map, "license_type"));
        //校验身份证以及营业执照有效期
        checkIdValidity(map);
    }

    /**
     * 判断营业执照类型是否跟间连支付账户类型匹配
     *
     * @param accountType 账户类型
     * @param licenseType 营业执照类型
     */
    private void isLicenseMatchAccount(int accountType, long licenseType) {
        BusinessLicenseId businessLicenseId = new BusinessLicenseId();
        businessLicenseId.setBusinessLicenseId(licenseType);
        Map config = payBusinessConfigurationService.findPayBusinessConfigurationByBusinessLicenseId(businessLicenseId);
        if (MapUtils.isEmpty(config)) {
            throw new CommonInvalidParameterException("营业执照类型不合法");
        }
        //行业配置规则
        List<Map> payConfig = JSON.parseArray(MapUtils.getString(config, "pay_applys"), Map.class);
        if (CollectionUtils.isEmpty(payConfig)) {
            return;
        }
        //间连
        Map<String, Object> indirectConfig = payConfig.stream().filter(pay -> pay.get("pay_type").equals(0)).findFirst().get();
        if (MapUtils.isNotEmpty(indirectConfig)) {
            //间连支持的账户类型
            List<Integer> bankTypes = JSON.parseArray(MapUtils.getString(indirectConfig, "bank_types"), Integer.class);
            if (CollectionUtils.isEmpty(bankTypes)) {
                return;
            }
            if (!bankTypes.contains(accountType)) {
                throw new CommonPubBizException("不支持当前营业证照类型");
            }
        }
    }


    private void checkIdValidity(Map map) {
        String idValidity = WosaiMapUtils.getString(map, MerchantBusinessLicence.ID_VALIDITY);
        Integer idType = WosaiMapUtils.getInteger(map, "id_type");
        String validity = WosaiMapUtils.getString(map, MerchantBusinessLicence.VALIDITY);

        if (WosaiStringUtils.isNotEmpty(idValidity) && idValidity.contains("-")) {
            String[] split = idValidity.split("\\-");
            EffectiveTimeUtil.checkoutEffectiveTime(split[1]);
            Pair<Boolean, String> valid = isValid(split[0], split[1], idType);
            if (!valid.getLeft()) {
                throw new CommonPubBizException(valid.getRight());
            }
        }
        if (WosaiStringUtils.isNotEmpty(validity) && validity.contains("-")) {
            String[] split = validity.split("\\-");
            EffectiveTimeUtil.checkoutEffectiveTime(split[1]);
        }
    }

    private Pair<Boolean, String> isValid(String startDate, String endDate, int idType) {
        //结束日期长期有效时不校验,通行证、中国护照不能选长期有效
        if (LONG_VALIDITY.equals(endDate) && canLongValidity(idType)) {
            return Pair.of(true, null);
        }
        try {
            int yearDiff = EffectiveTimeUtil.periodYear(startDate, endDate);
            switch (idType) {
                //身份证,年份要求【结束年份-开始年份】等于5、10、20年,且【结束日期 == 开始日期】
                case 1: {
                    if (yearDiff != YEAR_5 && yearDiff != YEAR_10 && yearDiff != YEAR_20) {
                        return Pair.of(false, getMessage(idType));
                    }
                    if (!EffectiveTimeUtil.isDateValid(startDate, endDate)) {
                        return Pair.of(false, getMessage(idType));
                    }
                }
                //非中华人民共和国护照、港澳居民居住证、台湾居民居住证 不校验
                case 4:
                case 6:
                case 7: {
                    return Pair.of(true, null);
                }
                //台湾居民来往内地通行证,港澳居民来往内地通行证、中国护照,年份要求【结束年份-开始年份】等于5、10年,且【结束日期早与开始日期1天或结束日期等于开始日期】
                case 3:
                case 2:
                case 5: {
                    if (yearDiff != YEAR_5 && yearDiff != YEAR_10) {
                        return Pair.of(false, getMessage(idType));
                    }
                    if (!EffectiveTimeUtil.isPeriodDay(startDate, endDate) && !EffectiveTimeUtil.isDateValid(startDate, endDate)) {
                        return Pair.of(false, getMessage(idType));
                    }
                }
                default:
                    return Pair.of(true, null);
            }
        } catch (Exception e) {
            log.error("isValid error occur : {}", e);
            return Pair.of(false, getMessage(idType));
        }

    }

    private boolean canLongValidity(int idType) {
        return idType != 3 && idType != 2 && idType != 5;
    }

    private String getMessage(Integer idType) {
        Map config = MapUtils.getMap(apolloConfigs.getIdTypeMap(), String.valueOf(idType));
        return MapUtils.isNotEmpty(config) ? MapUtils.getString(config, "message") : "有效期填写有误,请核对并修正";
    }
}
