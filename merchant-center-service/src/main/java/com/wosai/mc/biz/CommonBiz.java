package com.wosai.mc.biz;

import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.service.ContractStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/19
 */
@Slf4j
@Component
public class CommonBiz {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private ContractStatusService contractStatusService;


    public Map<String, Object> baseJudge(String merchantId) {
        Map merchantInfo = merchantService.getMerchantByMerchantId(merchantId);
        int merchantStatus = BeanUtil.getPropInt(merchantInfo, Merchant.STATUS);
        // 如果商户状态不正常
        if (merchantStatus != Merchant.STATUS_ENABLED) {
            return null;
        }
        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(BeanUtil.getPropString(merchantInfo, Merchant.SN));
        // 如果进件没通过也不发
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            return null;
        }
        UcMerchantUserSimpleInfo superAdminSimpleInfo = merchantUserServiceV2.getSuperAdminSimpleInfoByMerchantId(merchantId);
        // 如果商户没有老板账号
        if (superAdminSimpleInfo == null) {
            return null;
        }
        List merchantConfigs = tradeConfigService.getMerchantConfigsByMerchantId(merchantId);
        if (!judgeAnyPayWayIsInDirect(merchantConfigs)) {
            return null;
        }
        Map<String, Object> result = new HashMap<>(3);
        result.put("merchant", merchantInfo);
        result.put("merchantUserInfo", superAdminSimpleInfo);
        result.put("acquirer", contractStatus.getAcquirer());
        return result;
    }

    private boolean judgeAnyPayWayIsInDirect(List merchantConfigs) {
        if (WosaiCollectionUtils.isEmpty(merchantConfigs)) {
            return false;
        }
        for (Object merchantConfig : merchantConfigs) {
            int payWay = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (payWay == MerchantConfig.PAYWAY_ALIPAY_V2 || payWay == MerchantConfig.PAYWAY_WEIXIN) {
                // 支付宝和微信有一个sub_pay_way不是直连
                if (checkIfAnySubPayWayIsInDirect((Map) merchantConfig)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkIfAnySubPayWayIsInDirect(Map alipayConfig) {
        if (BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.B2C_STATUS) && !BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.B2C_FORMAL)) {
            return true;
        }
        if (BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.C2B_STATUS) && !BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.C2B_FORMAL)) {
            return true;
        }
        if (BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.WAP_STATUS) && !BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.WAP_FORMAL)) {
            return true;
        }
        if (BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.MINI_STATUS) && !BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.MINI_FORMAL)) {
            return true;
        }
        if (BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.APP_STATUS) && !BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.APP_FORMAL)) {
            return true;
        }
        if (BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.H5_STATUS) && !BeanUtil.getPropBoolean(alipayConfig, MerchantConfig.H5_FORMAL)) {
            return true;
        }
        return false;
    }
}
