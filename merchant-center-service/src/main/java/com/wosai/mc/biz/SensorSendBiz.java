package com.wosai.mc.biz;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.avro.ChangeMerchantSensor;
import com.wosai.mc.avro.ChangeStoreSensor;
import com.wosai.mc.avro.CreateMerchantSensor;
import com.wosai.mc.avro.CreateStoreSensor;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.StoreExtService;
import com.wosai.mc.utils.ClassFieldChangeUtil;
import com.wosai.upay.common.dao.DaoConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@Slf4j
@Component
public class SensorSendBiz {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    private MerchantBiz merchantBiz;

    @Autowired
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;

    @Autowired
    private StoreBiz storeBiz;

    @Autowired
    private StoreExtService storeExtService;

    @Autowired
    private ObjectMapper objectMapper;

    private static final List<String> IGNORE_PROPERTIES = Arrays
            .asList(DaoConstants.CTIME, DaoConstants.MTIME, DaoConstants.VERSION, DaoConstants.ID, DaoConstants.DELETED);

    private static final ExecutorService executor = new ThreadPoolExecutor(2, 5, 0L, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(1000), new ThreadFactoryBuilder().setNameFormat("merchant-center-pool-%d").build());

    @Value("${sensor.topic.createMerchant}")
    private String createMerchantTopic;

    @Value("${sensor.topic.createStore}")
    private String createStoreTopic;

    @Value("${sensor.topic.changeMerchant}")
    private String changeMerchantTopic;

    @Value("${sensor.topic.changeStore}")
    private String changeStoreTopic;


    private void sendCreateMerchantMessage(MerchantInfo merchantInfo) {
        Object message = null;
        try {
            message = this.buildCreateMerchantMessage(merchantInfo);
            kafkaTemplate.send(createMerchantTopic, message);
        } catch (Exception e) {
            log.error("发送创建商户消息失败 merchant_id:{} e:{}", merchantInfo.getId(), e);
        }
    }

    private void sendCreateStoreMessage(StoreInfo storeInfo) {
        Object message = null;
        try {
            message = this.buildCreateStoreMessage(storeInfo);
            kafkaTemplate.send(createStoreTopic, message);
        } catch (Exception e) {
            log.error("发送创建门店消息失败 store_id:{} e:{}", storeInfo.getId(), e);
        }
    }

    private void sendChangeMerchantMessage(MerchantInfo oldMerchant, MerchantBusinessLicenseInfo oldLicense, String devCode, String operator, String platform) {
        Object message = null;
        try {
            message = this.buildChangeMerchantMessage(oldMerchant, oldLicense, devCode, operator, platform);
            kafkaTemplate.send(changeMerchantTopic, message);
        } catch (Exception e) {
            log.error("发送更新商户消息失败 merchant_id:{} e:{}", this.getMerchantId(oldMerchant, oldLicense), e);
        }
    }

    private void sendChangeStoreMessage(StoreInfo oldStore, StoreExtInfo oldStoreExtInfo, StotreExtInfoAndPictures oldStorePictures, String devCode, String operator, String platform) {
        Object message = null;
        try {
            message = this.buildChangeStoreMessage(oldStore, oldStoreExtInfo, oldStorePictures, devCode, operator, platform);
            kafkaTemplate.send(changeStoreTopic, message);
        } catch (Exception e) {
            log.error("发送更新门店消息失败 store_id:{} e:{}", this.getStoreId(oldStore, oldStoreExtInfo, oldStorePictures), e);
        }
    }

    public void sendCreateMerchant(MerchantInfo merchantInfo) {
        executor.execute(() -> sendCreateMerchantMessage(merchantInfo));
    }

    public void sendCreateStore(StoreInfo storeInfo) {
        executor.execute(() -> sendCreateStoreMessage(storeInfo));
    }

    public void sendChangeMerchant(MerchantInfo oldMerchant, MerchantBusinessLicenseInfo oldLicense, String devCode, String operator, String platform) {
        executor.execute(() -> sendChangeMerchantMessage(oldMerchant, oldLicense, devCode, operator, platform));
    }

    public void sendChangeStore(StoreInfo oldStore, StoreExtInfo oldStoreExtInfo, StotreExtInfoAndPictures oldStorePictures, String devCode, String operator, String platform) {
        executor.execute(() -> sendChangeStoreMessage(oldStore, oldStoreExtInfo, oldStorePictures, devCode, operator, platform));
    }


    /**
     * 创建商户的消息
     *
     * @param merchantInfo 商户信息
     * @return 消息体
     */
    private Object buildCreateMerchantMessage(MerchantInfo merchantInfo) {
        CreateMerchantSensor createMerchantSensor = new CreateMerchantSensor();
        createMerchantSensor.setCtime(merchantInfo.getCtime());
        createMerchantSensor.setMerchantId(merchantInfo.getId());
        createMerchantSensor.setMerchantSn(merchantInfo.getSn());
        createMerchantSensor.setMerchantName(merchantInfo.getName());
        return createMerchantSensor;
    }


    /**
     * 创建门店的消息
     *
     * @param storeInfo 门店信息
     * @return 消息体
     */
    private Object buildCreateStoreMessage(StoreInfo storeInfo) {
        CreateStoreSensor createStoreSensor = new CreateStoreSensor();
        createStoreSensor.setMerchantId(storeInfo.getMerchant_id());
        createStoreSensor.setMerchantSn(merchantBiz.getMerchantInfoById(storeInfo.getMerchant_id(), null).getSn());
        createStoreSensor.setStoreId(storeInfo.getId());
        createStoreSensor.setStoreSn(storeInfo.getSn());
        createStoreSensor.setCtime(storeInfo.getCtime());
        return createStoreSensor;
    }


    /**
     * 商户变更的信息
     *
     * @param oldMerchant 更新之前商户信息
     * @param oldLicense  更新之前营业执照信息
     * @param devCode     业务方标识
     * @param operator    操作人
     * @param platform    操作平台
     * @return 消息体
     */
    private Object buildChangeMerchantMessage(MerchantInfo oldMerchant, MerchantBusinessLicenseInfo oldLicense, String devCode, String operator, String platform) {
        Map merchantModify = new HashMap();
        Map licenseModify = new HashMap();
        if (oldMerchant != null) {
            ClassFieldChangeUtil.getModifyContent(oldMerchant, merchantBiz.getMerchantInfoById(oldMerchant.getId(), devCode), IGNORE_PROPERTIES, merchantModify);
        }
        if (oldLicense != null) {
            ClassFieldChangeUtil.getModifyContent(oldLicense, merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(oldLicense.getMerchant_id(), devCode, false), IGNORE_PROPERTIES, licenseModify);
        }
        //字段名冲突特殊处理
        String licenseName = (String) licenseModify.get("name");
        licenseModify.putAll(merchantModify);
        ChangeMerchantSensor changeMerchantSensor = objectMapper.convertValue(licenseModify, ChangeMerchantSensor.class);
        changeMerchantSensor.setLicenseName(licenseName);
        if (oldMerchant == null) {
            oldMerchant = merchantBiz.getMerchantInfoById(oldLicense.getMerchant_id(), null);
        }
        changeMerchantSensor.setMerchantId(oldMerchant.getId());
        changeMerchantSensor.setMerchantSn(oldMerchant.getSn());
        changeMerchantSensor.setMtime(System.currentTimeMillis());
        changeMerchantSensor.setOperator(operator);
        changeMerchantSensor.setPlatform(platform);
        return changeMerchantSensor;
    }

    /**
     * 门店变更的信息
     *
     * @param oldStore         更新之前门店信息
     * @param oldStoreExtInfo  更新之前门店扩展信息
     * @param oldStorePictures 更新之前门店照片信息
     * @param devCode          业务方标识
     * @param operator         操作人
     * @param platform         操作平台
     * @return 消息体
     */
    private Object buildChangeStoreMessage(StoreInfo oldStore, StoreExtInfo oldStoreExtInfo, StotreExtInfoAndPictures oldStorePictures, String devCode, String operator, String platform) {
        Map storeModify = new HashMap();
        Map storeExtModify = new HashMap();
        Map storePicturesModify = new HashMap();
        if (oldStore != null) {
            ClassFieldChangeUtil.getModifyContent(oldStore, storeBiz.getStoreById(oldStore.getId(), devCode), IGNORE_PROPERTIES, storeModify);
        }
        if (oldStoreExtInfo != null) {
            ClassFieldChangeUtil.getModifyContent(oldStoreExtInfo, storeExtService.findStoreExtByStoreId(oldStoreExtInfo.getStoreId(), devCode), IGNORE_PROPERTIES, storeExtModify);
        }
        if (oldStorePictures != null) {
            ClassFieldChangeUtil.getModifyContent(this.buildPictures(oldStorePictures), this.buildPictures(storeExtService.findStoreExtAndPicturesByStoreId(oldStorePictures.getStoreId(), devCode)), IGNORE_PROPERTIES, storePicturesModify);
        }
        storeModify.putAll(storeExtModify);
        storeModify.putAll(storePicturesModify);
        ChangeStoreSensor changeStoreSensor = objectMapper.convertValue(storeModify, ChangeStoreSensor.class);
        if (oldStore != null) {
            changeStoreSensor.setMerchantId(oldStore.getMerchant_id());
            changeStoreSensor.setMerchantSn(merchantBiz.getMerchantInfoById(oldStore.getMerchant_id(), null).getSn());
            changeStoreSensor.setStoreId(oldStore.getId());
            changeStoreSensor.setStoreSn(oldStore.getSn());
        } else {
            String storeId = this.getStoreId(null, oldStoreExtInfo, oldStorePictures);
            StoreInfo storeInfo = storeBiz.getStoreById(storeId, null);
            changeStoreSensor.setMerchantId(storeInfo.getMerchant_id());
            changeStoreSensor.setMerchantSn(merchantBiz.getMerchantInfoById(storeInfo.getMerchant_id(), null).getSn());
            changeStoreSensor.setStoreId(storeInfo.getId());
            changeStoreSensor.setStoreSn(storeInfo.getSn());
        }
        changeStoreSensor.setMtime(System.currentTimeMillis());
        changeStoreSensor.setOperator(operator);
        changeStoreSensor.setPlatform(platform);
        return changeStoreSensor;
    }

    /**
     * 将照片url封装成新的字段
     *
     * @param oldStorePictures
     * @return
     */
    private Object buildPictures(StotreExtInfoAndPictures oldStorePictures) {
        Map picturesMap = new HashMap(6);
        if (oldStorePictures.getBrandPhoto() != null && WosaiStringUtils.isNotEmpty(oldStorePictures.getBrandPhoto().getUrl())) {
            picturesMap.put("brand_photo", oldStorePictures.getBrandPhoto().getUrl());
        }
        if (oldStorePictures.getIndoorMaterialPhoto() != null && WosaiStringUtils.isNotEmpty(oldStorePictures.getIndoorMaterialPhoto().getUrl())) {
            picturesMap.put("indoor_material", oldStorePictures.getIndoorMaterialPhoto().getUrl());
        }
        if (oldStorePictures.getOutdoorMaterialPhoto() != null && WosaiStringUtils.isNotEmpty(oldStorePictures.getOutdoorMaterialPhoto().getUrl())) {
            picturesMap.put("outdoor_material", oldStorePictures.getOutdoorMaterialPhoto().getUrl());
        }
        if (oldStorePictures.getAuditPicture() != null && WosaiStringUtils.isNotEmpty(oldStorePictures.getAuditPicture().getUrl())) {
            picturesMap.put("audit_picture", oldStorePictures.getAuditPicture().getUrl());
        }
        if (oldStorePictures.getProductPrice() != null && WosaiStringUtils.isNotEmpty(oldStorePictures.getProductPrice().getUrl())) {
            picturesMap.put("product_price", oldStorePictures.getProductPrice().getUrl());
        }
        return picturesMap;
    }

    private String getMerchantId(MerchantInfo merchantInfo, MerchantBusinessLicenseInfo licenseInfo) {
        if (merchantInfo != null) {
            return merchantInfo.getId();
        }
        if (licenseInfo != null) {
            return licenseInfo.getMerchant_id();
        }
        return null;
    }

    private String getStoreId(StoreInfo store, StoreExtInfo storeExtInfo, StotreExtInfoAndPictures pictures) {
        if (store != null) {
            return store.getId();
        }
        if (storeExtInfo != null) {
            return storeExtInfo.getStoreId();
        }
        if (pictures != null) {
            return pictures.getStoreId();
        }
        return null;
    }
}
