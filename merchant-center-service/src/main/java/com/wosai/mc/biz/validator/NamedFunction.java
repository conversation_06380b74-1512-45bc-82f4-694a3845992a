package com.wosai.mc.biz.validator;

import lombok.Getter;

import java.util.function.Function;

/**
 * 带有名字的function实现
 */
public class NamedFunction<T, R> implements Function<T, R> {

    @Getter
    private final String name;
    private final Function<T, R> function;

    public NamedFunction(String name, Function<T, R> function) {
        this.name = name;
        this.function = function;
    }

    @Override
    public R apply(T t) {
        return function.apply(t);
    }

}
