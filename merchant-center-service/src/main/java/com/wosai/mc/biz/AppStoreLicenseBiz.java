package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.constants.DraftTypeEnum;
import com.wosai.mc.constants.PlatformConstant;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.SimplePhotoInfo;
import com.wosai.mc.model.app.BusinessLicenseInfo;
import com.wosai.mc.model.app.IdCardInfo;
import com.wosai.mc.model.app.OtherInfo;
import com.wosai.mc.model.app.TradeLicenseInfo;
import com.wosai.mc.model.app.req.StoreLicenseDraftSaveReq;
import com.wosai.mc.remote.bankinfo.BankInfoClient;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.sales.merchant.business.bean.app.request.GetFieldAppInfoReq;
import com.wosai.sales.merchant.business.bean.app.request.SaveLicenseFieldAppInfoReq;
import com.wosai.sales.merchant.business.bean.app.response.GetFieldAppInfoResp;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.core.model.License;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.StoreBusinessLicence;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.merchant.contract.model.Tuple2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Component
public class AppStoreLicenseBiz {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private CommonFieldService commonFieldService;
    @Autowired
    private UcUserAccountService ucUserAccountService;
    @Autowired
    private BankInfoClient bankInfoClient;
    @Autowired
    private StoreExtBiz storeExtBiz;
    @Autowired
    private FieldValidBiz fieldValidBiz;
    @Autowired
    private Validator validator;
    @Value("${businessopen.appid.takeaway}")
    private String takeawayAppId;

    public void checkDraftInfo(Map<String, Object> submitInfo, Map merchant) {
        checkBusinessLicenseInfo(submitInfo.get(DraftTypeEnum.BUSINESS_LICENSE.getType()), submitInfo, merchant);
        checkIdCardInfo(submitInfo.get(DraftTypeEnum.ID_CARD.getType()), submitInfo, merchant);
        checkTradeLicenseInfo(submitInfo.get(DraftTypeEnum.TRADE_LICENSE.getType()), submitInfo, merchant);
    }

    public void checkSaveDraftInfo(StoreLicenseDraftSaveReq req) {
        DraftTypeEnum type = req.getDraft().getType();
        Object data = req.getDraft().getData();
        Map merchant = merchantService.getMerchantByMerchantId(req.getMerchantId());
        Map<String, Object> submitInfo = getFieldAppInfo(req.getMerchantId(), req.getOpStoreId()).getSubmitInfo();
        if (type.equals(DraftTypeEnum.BUSINESS_LICENSE)) {
            checkBusinessLicenseInfo(data, submitInfo, merchant);
        }
        if (type.equals(DraftTypeEnum.ID_CARD)) {
            checkIdCardInfo(data, submitInfo, merchant);
        }
        if (type.equals(DraftTypeEnum.TRADE_LICENSE)) {
            checkTradeLicenseInfo(data, submitInfo, merchant);
        }
        if (type.equals(DraftTypeEnum.OTHER)) {
            checkSaveOtherInfo(data, submitInfo, merchant);
        }
    }

    public void saveDraftInfo(StoreLicenseDraftSaveReq req) {
        Map<String, Object> submitInfo = getFieldAppInfo(req.getMerchantId(), req.getOpStoreId()).getSubmitInfo();
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(req.getUcUserId());
        SaveLicenseFieldAppInfoReq saveLicenseFieldAppInfoReq = new SaveLicenseFieldAppInfoReq();
        saveLicenseFieldAppInfoReq.setMerchantId(req.getMerchantId());
        saveLicenseFieldAppInfoReq.setStoreId(req.getOpStoreId());
        saveLicenseFieldAppInfoReq.setAppId(takeawayAppId);
        saveLicenseFieldAppInfoReq.setUserId(req.getUcUserId());
        saveLicenseFieldAppInfoReq.setUserName(ucUserInfo.getNickname());
        saveLicenseFieldAppInfoReq.setFieldMainType(1);
        saveLicenseFieldAppInfoReq.setFieldType("business_license");
        saveLicenseFieldAppInfoReq.setPlatform(PlatformConstant.PLATFORM_APP);
        if (Objects.nonNull(submitInfo)) {
            submitInfo.put(req.getDraft().getType().getType(), req.getDraft().getData());
            saveLicenseFieldAppInfoReq.setSubmitInfo(submitInfo);
        } else {
            saveLicenseFieldAppInfoReq.setSubmitInfo(CollectionUtil.hashMap(
                    req.getDraft().getType().getType(), req.getDraft().getData()
            ));
        }
        commonFieldService.saveOrUpdateFieldAppInfo(saveLicenseFieldAppInfoReq);
    }

    /**
     * 将目标营业执照的信息写入到草稿中
     * @param merchantId 商户ID
     * @param storeId 门店ID
     * @param ucUserId 用户ID
     * @param businessLicense 营业执照信息
     * @param reUse 是否复用商户营业执照信息
     * @param isChangeApply 是否是变更申请
     */
    public void saveTargetLicenseToDraft(String merchantId, String storeId, String ucUserId, Map<String, Object> businessLicense, boolean reUse, boolean isChangeApply) {
        // 营业执照信息
        BusinessLicenseInfo businessLicenseInfo = new BusinessLicenseInfo()
                .setType(WosaiMapUtils.getInteger(businessLicense, MerchantBusinessLicence.TYPE))
                .setPhoto(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.PHOTO))
                .setNumber(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.NUMBER))
                .setName(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.NAME))
                .setLegalPersonName(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_NAME))
                .setAddress(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.ADDRESS))
                .setBusinessScope(WosaiMapUtils.getString(businessLicense, "business_scope"))
                .setValidity(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.VALIDITY))
                .setUseMerchantBusinessLicense(reUse ? 1 : 0);
        // 法人证件信息
        IdCardInfo idCardInfo = new IdCardInfo()
                .setLegalPersonIdType(WosaiMapUtils.getInteger(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE))
                .setLegalPersonName(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_NAME))
                .setLegalPersonIdNumber(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER))
                .setIdValidity(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.ID_VALIDITY))
                .setLegalPersonIdCardFrontPhoto(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO))
                .setLegalPersonIdCardBackPhoto(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO))
                .setLegalPersonIdCardAddress(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ADDRESS))
                .setLegalPersonIdCardIssuingAuthority(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ISSUING_AUTHORITY));
        // 许可证信息
        List<Map> tradeLicenseList = (List<Map>) Optional.ofNullable(WosaiMapUtils.getObject(businessLicense, MerchantBusinessLicence.TRADE_LICENSE_LIST)).orElse(new ArrayList<>());
        List<TradeLicenseInfo> tradeLicenseInfos = tradeLicenseList.stream().map(r -> new TradeLicenseInfo()
                        .setLicenseType(WosaiMapUtils.getInteger(r, License.LICENSE_TYPE))
                        .setLicensePhoto(WosaiMapUtils.getString(r, License.LICENSE_PHOTO))
                        .setLicenseNumber(WosaiMapUtils.getString(r, License.LICENSE_NUMBER))
                        .setLicenseName(WosaiMapUtils.getString(r, License.LICENSE_NAME))
                        .setLicenseValidity(WosaiMapUtils.getString(r, License.LICENSE_VALIDITY)))
                .collect(Collectors.toList());
        // 照片信息
        List<PhotoInfo> storeOtherPhotos = storeExtBiz.findStoreOtherPhotos(storeId, null);
        OtherInfo otherInfo = null;
        if (WosaiCollectionUtils.isNotEmpty(storeOtherPhotos)) {
            otherInfo = new OtherInfo()
                    .setPhotos(storeOtherPhotos.stream().map(r -> new SimplePhotoInfo().setId(r.getId()).setUrl(r.getUrl())).collect(Collectors.toList()));
        }
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(ucUserId);
        SaveLicenseFieldAppInfoReq saveLicenseFieldAppInfoReq = new SaveLicenseFieldAppInfoReq();
        saveLicenseFieldAppInfoReq.setMerchantId(merchantId);
        saveLicenseFieldAppInfoReq.setStoreId(storeId);
        saveLicenseFieldAppInfoReq.setAppId(takeawayAppId);
        saveLicenseFieldAppInfoReq.setUserId(ucUserId);
        saveLicenseFieldAppInfoReq.setUserName(ucUserInfo.getNickname());
        saveLicenseFieldAppInfoReq.setFieldMainType(1);
        saveLicenseFieldAppInfoReq.setFieldType("business_license");
        saveLicenseFieldAppInfoReq.setPlatform(PlatformConstant.PLATFORM_APP);
        Map submitInfo = CollectionUtil.hashMap(
                DraftTypeEnum.BUSINESS_LICENSE.getType(), businessLicenseInfo,
                DraftTypeEnum.ID_CARD.getType(), idCardInfo,
                DraftTypeEnum.TRADE_LICENSE.getType(), tradeLicenseInfos,
                DraftTypeEnum.OTHER.getType(), otherInfo,
                DraftTypeEnum.CHANGE_APPLY.getType(), isChangeApply
        );
        saveLicenseFieldAppInfoReq.setSubmitInfo(submitInfo);
        commonFieldService.saveOrUpdateFieldAppInfo(saveLicenseFieldAppInfoReq);

    }

    public void clearSubmitInfo(String merchantId, String storeId, String ucUserId) {
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(ucUserId);
        SaveLicenseFieldAppInfoReq saveLicenseFieldAppInfoReq = new SaveLicenseFieldAppInfoReq();
        saveLicenseFieldAppInfoReq.setMerchantId(merchantId);
        saveLicenseFieldAppInfoReq.setStoreId(storeId);
        saveLicenseFieldAppInfoReq.setAppId(takeawayAppId);
        saveLicenseFieldAppInfoReq.setUserId(ucUserId);
        saveLicenseFieldAppInfoReq.setUserName(ucUserInfo.getNickname());
        saveLicenseFieldAppInfoReq.setFieldMainType(1);
        saveLicenseFieldAppInfoReq.setFieldType("business_license");
        saveLicenseFieldAppInfoReq.setPlatform(PlatformConstant.PLATFORM_APP);
        saveLicenseFieldAppInfoReq.setSubmitInfo(new HashMap<>());
        commonFieldService.saveOrUpdateFieldAppInfo(saveLicenseFieldAppInfoReq);
    }

    public void saveSubmitInfo(String merchantId, String storeId, String ucUserId, Map<String, Object> submitInfo) {
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(ucUserId);
        SaveLicenseFieldAppInfoReq saveLicenseFieldAppInfoReq = new SaveLicenseFieldAppInfoReq();
        saveLicenseFieldAppInfoReq.setMerchantId(merchantId);
        saveLicenseFieldAppInfoReq.setStoreId(storeId);
        saveLicenseFieldAppInfoReq.setAppId(takeawayAppId);
        saveLicenseFieldAppInfoReq.setUserId(ucUserId);
        saveLicenseFieldAppInfoReq.setUserName(ucUserInfo.getNickname());
        saveLicenseFieldAppInfoReq.setFieldMainType(1);
        saveLicenseFieldAppInfoReq.setFieldType("business_license");
        saveLicenseFieldAppInfoReq.setPlatform(PlatformConstant.PLATFORM_APP);
        saveLicenseFieldAppInfoReq.setSubmitInfo(submitInfo);
        commonFieldService.saveOrUpdateFieldAppInfo(saveLicenseFieldAppInfoReq);
    }

    public void checkBusinessLicenseInfo(Object data, Map submitInfo, Map merchant) {
        if (Objects.isNull(data)) {
            throw new CommonInvalidParameterException("营业执照草稿信息为空");
        }
        BusinessLicenseInfo businessLicenseInfo = JSON.parseObject(JSON.toJSONString(data), BusinessLicenseInfo.class);
        validate(businessLicenseInfo);

        fieldValidBiz.validBusinessLicenseType(businessLicenseInfo.getType(), WosaiMapUtils.getString(merchant, Merchant.INDUSTRY));
        fieldValidBiz.validBusinessLicenseName(businessLicenseInfo.getName());
        fieldValidBiz.validBusinessLicenseNumber(businessLicenseInfo.getType(), businessLicenseInfo.getNumber());
        fieldValidBiz.validBusinessLicensePhoto(businessLicenseInfo.getPhoto());
        fieldValidBiz.validBusinessLicenseValidity(businessLicenseInfo.getValidity());
        fieldValidBiz.validBusinessLicenseAddress(businessLicenseInfo.getAddress());
    }

    public void checkIdCardInfo(Object data, Map submitInfo, Map merchant) {
        if (Objects.isNull(data)) {
            throw new CommonInvalidParameterException("法人证件草稿信息为空");
        }
        if (Objects.isNull(submitInfo) || Objects.isNull(submitInfo.get(DraftTypeEnum.BUSINESS_LICENSE.getType()))) {
            throw new CommonInvalidParameterException("营业执照草稿信息不存在");
        }
        IdCardInfo idCardInfo = JSON.parseObject(JSON.toJSONString(data), IdCardInfo.class);
        BusinessLicenseInfo businessLicenseInfo = JSON.parseObject(JSON.toJSONString(submitInfo.get(DraftTypeEnum.BUSINESS_LICENSE.getType())), BusinessLicenseInfo.class);
        validate(idCardInfo);

        if (!Objects.equals(businessLicenseInfo.getLegalPersonName(), idCardInfo.getLegalPersonName())) {
            throw new CommonInvalidParameterException("营业执照法人姓名和证件姓名不一致");
        }

        fieldValidBiz.validBusinessLicenseLegalPersonName(idCardInfo.getLegalPersonIdType(), idCardInfo.getLegalPersonName());
        fieldValidBiz.validBusinessLicenseLegalPersonIdType(businessLicenseInfo.getType(), idCardInfo.getLegalPersonIdType());
        fieldValidBiz.validBusinessLicenseLegalPersonIdNumber(idCardInfo.getLegalPersonIdType(), idCardInfo.getLegalPersonIdNumber());
        fieldValidBiz.validBusinessLicenseIdValidity(idCardInfo.getLegalPersonIdType(), idCardInfo.getIdValidity());
        fieldValidBiz.validBusinessLicenseLegalPersonFrontPhoto(idCardInfo.getLegalPersonIdType(), idCardInfo.getLegalPersonIdCardFrontPhoto());
        fieldValidBiz.validBusinessLicenseLegalPersonBackPhoto(idCardInfo.getLegalPersonIdType(), idCardInfo.getLegalPersonIdCardBackPhoto());
    }

    public void checkTradeLicenseInfo(Object data, Map submitInfo, Map merchant) {
        List<Integer> tradeLicenseTypes = bankInfoClient.queryIndustrySupportTradeLicenses(WosaiMapUtils.getString(merchant, Merchant.INDUSTRY));
        if (WosaiCollectionUtils.isEmpty(tradeLicenseTypes)) {
            return;
        }
        if (Objects.isNull(data) || !(data instanceof List)) {
            throw new CommonInvalidParameterException("许可证信息格式错误");
        }
        List<TradeLicenseInfo> tradeLicenseInfos = JSON.parseArray(JSON.toJSONString(data), TradeLicenseInfo.class);
        if (WosaiCollectionUtils.isEmpty((Collection) data)) {
            throw new CommonInvalidParameterException("许可证信息为空");
        }
        for (TradeLicenseInfo tradeLicenseInfo : tradeLicenseInfos) {
            validate(tradeLicenseInfo);
            if (!tradeLicenseTypes.contains(tradeLicenseInfo.getLicenseType())) {
                throw new CommonInvalidParameterException("行业不支持该许可证类型");
            }
            fieldValidBiz.validTradeLicensePhoto(tradeLicenseInfo.getLicensePhoto());
            fieldValidBiz.validTradeLicenseName(tradeLicenseInfo.getLicenseName());
            fieldValidBiz.validTradeLicenseNumber(tradeLicenseInfo.getLicenseNumber());
            fieldValidBiz.validTradeLicenseValidity(tradeLicenseInfo.getLicenseValidity());
        }
    }

    /**
     * 判断是否可以保存其他信息
     * @param data 其他信息
     * @param submitInfo 草稿信息
     * @param merchant 商户信息
     */
    public void checkSaveOtherInfo(Object data, Map<String, Object> submitInfo, Map merchant) {
        if (Objects.isNull(data)) {
            return;
        }
        OtherInfo otherInfo = JSON.parseObject(JSON.toJSONString(data), OtherInfo.class);
        List<SimplePhotoInfo> photos = otherInfo.getPhotos();
        if (WosaiCollectionUtils.isNotEmpty(photos) && photos.size() > 7) {
            throw new CommonInvalidParameterException("照片数量不可超过7张");
        }
    }

    /**
     * 判断其他信息的填写状态
     * @param data 其他信息
     * @param submitInfo 草稿信息
     * @param merchant 商户信息
     */
    public void checkOtherInfo(Object data, Map<String, Object> submitInfo, Map merchant) {
        if (Objects.isNull(data)) {
            throw new CommonInvalidParameterException("其他信息不能为空");
        }
        OtherInfo otherInfo = JSON.parseObject(JSON.toJSONString(data), OtherInfo.class);
        if (WosaiStringUtils.isEmpty(otherInfo.getRemark()) && WosaiCollectionUtils.isEmpty(otherInfo.getPhotos())) {
            throw new CommonInvalidParameterException("其他信息不可全部为空");
        }
    }

    public GetFieldAppInfoResp getFieldAppInfo(String merchantId, String storeId) {
        GetFieldAppInfoReq getFieldAppInfoReq = new GetFieldAppInfoReq();
        getFieldAppInfoReq.setMerchantId(merchantId);
        getFieldAppInfoReq.setStoreId(storeId);
        getFieldAppInfoReq.setFieldMainType(1);
        getFieldAppInfoReq.setFieldType("business_license");
        return commonFieldService.getFieldAppInfo(getFieldAppInfoReq);
    }

    private void validate(Object object) {
        Set<ConstraintViolation<Object>> validate = validator.validate(object);
        if (validate.size() > 0) {
            StringJoiner message = new StringJoiner(";");
            for (ConstraintViolation<Object> constraintViolation : validate) {
                message.add(constraintViolation.getMessage());
            }
            throw new CommonInvalidParameterException(message.toString());
        }
    }

    public void checkDraftInfoChange(Map<String, Object> submitInfo, Tuple2<Boolean, Map<String, Object>> storeBusinessLicenseTuple) {
        Map<String, Object> storeBusinessLicense = storeBusinessLicenseTuple.get_2();
        BusinessLicenseInfo businessLicenseInfo = JSON.parseObject(JSON.toJSONString(submitInfo.get(DraftTypeEnum.BUSINESS_LICENSE.getType())), BusinessLicenseInfo.class);
        IdCardInfo idCardInfo = JSON.parseObject(JSON.toJSONString(submitInfo.get(DraftTypeEnum.ID_CARD.getType())), IdCardInfo.class);
        List<TradeLicenseInfo> tradeLicenseInfos = JSON.parseArray(JSON.toJSONString(submitInfo.get(DraftTypeEnum.TRADE_LICENSE.getType())), TradeLicenseInfo.class);
        boolean businessLicenseChanged = checkBusinessLicenseChanged(businessLicenseInfo, storeBusinessLicense);
        boolean idCardChanged = checkIdCardChanged(idCardInfo, storeBusinessLicense);
        boolean tradeLicensesChanged = checkTradeLicensesChanged(tradeLicenseInfos, storeBusinessLicense);
        if (businessLicenseChanged || idCardChanged || tradeLicensesChanged) {
            return;
        }
        throw new CommonInvalidParameterException("信息均未变更，不允许提交");
    }

    /**
     * 校验草稿营业执照信息和认证通过的相比是否发生了变化
     * @param businessLicenseInfo 营业执照信息
     * @param storeBusinessLicense 认证通过的营业证照信息
     * @return true发生了变化 false没有发生变化
     */
    private boolean checkBusinessLicenseChanged(BusinessLicenseInfo businessLicenseInfo, Map<String, Object> storeBusinessLicense) {
        return !(Objects.equals(businessLicenseInfo.getType(), WosaiMapUtils.getInteger(storeBusinessLicense, StoreBusinessLicence.TYPE))
                && Objects.equals(CommonUtils.baseUrl(businessLicenseInfo.getPhoto()), CommonUtils.baseUrl(WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.PHOTO)))
                && Objects.equals(businessLicenseInfo.getNumber(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.NUMBER))
                && Objects.equals(businessLicenseInfo.getName(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.NAME))
                && Objects.equals(businessLicenseInfo.getLegalPersonName(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.LEGAL_PERSON_NAME))
                && Objects.equals(businessLicenseInfo.getAddress(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.ADDRESS))
                && Objects.equals(businessLicenseInfo.getValidity(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.VALIDITY)));
    }

    /**
     * 校验草稿法人证件信息和认证通过的相比是否发生了变化
     * @param idCardInfo 法人证件信息
     * @param storeBusinessLicense 认证通过的营业证照信息
     * @return true发生了变化 false没有发生变化
     */
    private boolean checkIdCardChanged(IdCardInfo idCardInfo, Map<String, Object> storeBusinessLicense) {
        return !(Objects.equals(idCardInfo.getLegalPersonIdType(), WosaiMapUtils.getInteger(storeBusinessLicense, StoreBusinessLicence.LEGAL_PERSON_ID_TYPE))
                && Objects.equals(idCardInfo.getLegalPersonName(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.LEGAL_PERSON_NAME))
                && Objects.equals(idCardInfo.getLegalPersonIdNumber(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.LEGAL_PERSON_ID_NUMBER))
                && Objects.equals(idCardInfo.getIdValidity(), WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.ID_VALIDITY))
                && Objects.equals(CommonUtils.baseUrl(idCardInfo.getLegalPersonIdCardFrontPhoto()), CommonUtils.baseUrl(WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO)))
                && Objects.equals(CommonUtils.baseUrl(idCardInfo.getLegalPersonIdCardBackPhoto()), CommonUtils.baseUrl(WosaiMapUtils.getString(storeBusinessLicense, StoreBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO))));
    }

    private boolean checkTradeLicensesChanged(List<TradeLicenseInfo> tradeLicenses, Map<String, Object> storeBusinessLicense) {
        List<Map> verifiedStoreLicenses = (List<Map>) Optional.ofNullable(storeBusinessLicense.get("trade_license_list")).orElse(new ArrayList<>());
        tradeLicenses = Optional.ofNullable(tradeLicenses).orElse(new ArrayList<>());
        if (tradeLicenses.size() != verifiedStoreLicenses.size()) {
            return true;
        }
        tradeLicenses.sort(Comparator.comparing(TradeLicenseInfo::getLicenseNumber));
        verifiedStoreLicenses.sort(Comparator.comparing(o -> WosaiMapUtils.getString(o, License.LICENSE_NUMBER)));
        for (int i = 0; i< tradeLicenses.size(); i++) {
            TradeLicenseInfo tradeLicenseInfo = tradeLicenses.get(i);
            Map verifiedInfo = verifiedStoreLicenses.get(i);
            boolean changed =
                    !(Objects.equals(tradeLicenseInfo.getLicenseType(), WosaiMapUtils.getInteger(verifiedInfo, License.LICENSE_TYPE))
                            && Objects.equals(CommonUtils.baseUrl(tradeLicenseInfo.getLicensePhoto()), CommonUtils.baseUrl(WosaiMapUtils.getString(verifiedInfo, License.LICENSE_PHOTO)))
                            && Objects.equals(tradeLicenseInfo.getLicenseNumber(), WosaiMapUtils.getString(verifiedInfo, License.LICENSE_NUMBER))
                            && Objects.equals(tradeLicenseInfo.getLicenseName(), WosaiMapUtils.getString(verifiedInfo, License.LICENSE_NAME))
                            && Objects.equals(tradeLicenseInfo.getLicenseValidity(), WosaiMapUtils.getString(verifiedInfo, License.LICENSE_VALIDITY)));
            if (changed) {
                return true;
            }
        }
        return false;
    }
}
