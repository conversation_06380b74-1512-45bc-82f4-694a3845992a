package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.constants.MergeStoreConstant;
import com.wosai.mc.entity.StoreListHandleSuccess;
import com.wosai.mc.entity.StoreListHandleSuccessExample;
import com.wosai.mc.entity.StoreListWaitHandle;
import com.wosai.mc.mapper.StoreListHandleSuccessMapper;
import com.wosai.mc.mapper.StoreListWaitHandleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class SideNoticeBiz {
    private final List<String> APP_SIDES = Arrays.asList("TERMINALAPP");
    @Autowired
    private ClientSideNoticeService clientSideNoticeService;
    @Autowired
    private StoreListWaitHandleMapper waitHandleMapper;
    @Autowired
    private StoreListHandleSuccessMapper successHandleMapper;
    @Value("${merge_store.app_code}")
    private String appCode;
    @Value("${merge_store.need_merge_template}")
    private String needMergeTemplate;
    @Value("${merge_store.not_over_template}")
    private String notOverTemplate;
    @Value("${merge_store.over_template}")
    private String overTemplate;

    public boolean send(String devCode, String templateCode, List<String> appSides, String accountId, Map dataMap) {
        ClientSideNoticeSendModel noticeApp = new ClientSideNoticeSendModel();
        noticeApp.setDevCode(devCode);
        noticeApp.setTemplateCode(templateCode);
        noticeApp.setClientSides(appSides);
        noticeApp.setTimestamp(System.currentTimeMillis());
        noticeApp.setAccountId(accountId);
        if (WosaiMapUtils.isNotEmpty(dataMap)) {
            noticeApp.setData(dataMap);
        }
        log.info("发送通知内容:{}", JSON.toJSONString(noticeApp));
        Boolean send = clientSideNoticeService.send(noticeApp);
        log.info("发通知结果:{}", send);
        return send;
    }


    /**
     * @param day 天数 30  20  10  天
     */
    public boolean noticeMerchantMergeStore(String merchantId, int day) {
        return send(appCode, needMergeTemplate, APP_SIDES, merchantId, CollectionUtil.hashMap("day", day));
    }

    /**
     * 已完成{{number1}}家门店的信息确认，仍有{{number2}}家门店的信息待确认
     */
    public boolean noticeStoreMergedNum(String merchantId) {
        List<StoreListWaitHandle> storeListWaitHandles = waitHandleMapper.selectByMerchantIdNotMergeAddress(merchantId);
        int notOverSize = storeListWaitHandles.size();
        //已处理x条 的统计,由于多线程问题需要更精细的判断.不能拿数据库状态字段判断
        StoreListHandleSuccessExample example = new StoreListHandleSuccessExample();
        example.or().andMerchant_idEqualTo(merchantId)/*.andIs_merged_name_addressEqualTo(MergeStoreConstant.MERGED)*/;
        List<StoreListHandleSuccess> storeListHandleSuccesses = successHandleMapper.selectByExampleWithBLOBs(example);
        int overSize1 = (int) storeListHandleSuccesses.stream().filter(s -> WosaiStringUtils.isNotBlank(s.getUpdate_params_name_address())).count();
        List<StoreListWaitHandle> handlesAddressOver = waitHandleMapper.selectByMerchantId(merchantId);
        int overSize2 = (int) handlesAddressOver.stream().filter(s -> WosaiStringUtils.isNotBlank(s.getUpdate_params_name_address())).count();
        //还有未结束的合并,再触发发通知
        if (notOverSize > 0) {
            return send(appCode, notOverTemplate, APP_SIDES, merchantId, CollectionUtil.hashMap("number1", overSize1 + overSize2, "number2", notOverSize));
        }
        log.info("商户 {} 没有待确定门店,不发通知", merchantId);
        return true;
    }

    public boolean noticeMerchantAfterAllMerged(String merchantId) {
        return send(appCode, overTemplate, APP_SIDES, merchantId, null);
    }
}
