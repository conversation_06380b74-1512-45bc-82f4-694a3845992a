package com.wosai.mc.biz;

import avro.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson.JSONObject;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.constants.CrmApplyConstant;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.CreateStoreReq;
import com.wosai.mc.model.req.LogReq;
import com.wosai.mc.model.req.UpdateStorePicturesReq;
import com.wosai.mc.model.resp.FieldStatusResp;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.PhotoInfoService;
import com.wosai.upay.core.service.StoreExtService;
import com.wosai.upay.core.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/18
 */
@Slf4j
@Component
public class StoreBiz {

    @Autowired
    private StoreExtService storeExtService;

    @Autowired
    private PhotoInfoService photoInfoService;

    @Autowired
    private com.wosai.mc.service.PhotoInfoService mcPhotoInfoService;

    @Autowired
    private StoreExtBiz storeExtBiz;

    @Autowired
    private StoreService storeService;

    @Autowired
    private McPreBiz mcPreBiz;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private PoiBiz poiBiz;

    @Autowired
    private CommonFieldService commonFieldService;

    public StoreInfo createStore(CreateStoreReq req) {
        poiBiz.checkAndFillPoi(req);
        Map createStoreReq = MyBeanUtil.toMap(req);
        Map store = GetRealObject.filterParams(createStoreReq, GetRealObject.STORE);
        store = storeService.createStoreForMerchantCenter(store);
        //商户信息处理
        checkBindWhenCreateStore(store);
        handleBindStore(store, false, null);

        Map storeExt = Maps.newHashMap();
        PhotoInfo brandPhoto = req.getBrandPhoto();
        if (brandPhoto != null && WosaiStringUtils.isNotEmpty(brandPhoto.getUrl())) {
            String brandPhotoId = UUID.randomUUID().toString();
            brandPhoto.setId(brandPhotoId);
            brandPhoto.setUrl(PhotoUtils.baseUrl(brandPhoto.getUrl()));
            Map brandPhotoMap = MyBeanUtil.toMap(brandPhoto);
            storeExt.put("brand_photo_id", brandPhotoId);
            photoInfoService.createPhotoinfo(brandPhotoMap);
        }
        PhotoInfo brandOnlyScenePhoto = req.getBrandOnlyScenePhoto();
        if (brandOnlyScenePhoto != null && WosaiStringUtils.isNotEmpty(brandOnlyScenePhoto.getUrl())) {
            String brandOnlyScenePhotoId = UUID.randomUUID().toString();
            brandOnlyScenePhoto.setId(brandOnlyScenePhotoId);
            brandOnlyScenePhoto.setUrl(PhotoUtils.baseUrl(brandOnlyScenePhoto.getUrl()));

            Map brandOnlyScenePhotoMap = MyBeanUtil.toMap(brandOnlyScenePhoto);
            storeExt.put("brand_only_scene_photo_id", brandOnlyScenePhotoId);
            photoInfoService.createPhotoinfo(brandOnlyScenePhotoMap);
        }
        PhotoInfo indoorMaterialPhoto = req.getIndoorMaterialPhoto();
        if (indoorMaterialPhoto != null && WosaiStringUtils.isNotEmpty(indoorMaterialPhoto.getUrl())) {
            String indoorMaterialPhotoId = UUID.randomUUID().toString();
            indoorMaterialPhoto.setId(indoorMaterialPhotoId);
            indoorMaterialPhoto.setUrl(PhotoUtils.baseUrl(indoorMaterialPhoto.getUrl()));

            Map indoorMaterialPhotoMap = MyBeanUtil.toMap(indoorMaterialPhoto);
            storeExt.put("indoor_material_photo_id", indoorMaterialPhotoId);
            photoInfoService.createPhotoinfo(indoorMaterialPhotoMap);
        }
        PhotoInfo indoorOnlyScenePhoto = req.getIndoorOnlyScenePhoto();
        if (indoorOnlyScenePhoto != null && WosaiStringUtils.isNotEmpty(indoorOnlyScenePhoto.getUrl())) {
            String indoorOnlyScenePhotoId = UUID.randomUUID().toString();
            indoorOnlyScenePhoto.setId(indoorOnlyScenePhotoId);
            indoorOnlyScenePhoto.setUrl(PhotoUtils.baseUrl(indoorOnlyScenePhoto.getUrl()));

            Map indoorOnlyScenePhotoMap = MyBeanUtil.toMap(indoorOnlyScenePhoto);
            storeExt.put("indoor_only_scene_photo_id", indoorOnlyScenePhotoId);
            photoInfoService.createPhotoinfo(indoorOnlyScenePhotoMap);
        }
        PhotoInfo outdoorMaterialPhoto = req.getOutdoorMaterialPhoto();
        if (outdoorMaterialPhoto != null && WosaiStringUtils.isNotEmpty(outdoorMaterialPhoto.getUrl())) {
            String outdoorMaterialPhotoId = UUID.randomUUID().toString();
            outdoorMaterialPhoto.setId(outdoorMaterialPhotoId);
            outdoorMaterialPhoto.setUrl(PhotoUtils.baseUrl(outdoorMaterialPhoto.getUrl()));

            Map outdoorMaterialPhotoMap = MyBeanUtil.toMap(outdoorMaterialPhoto);
            storeExt.put("outdoor_material_photo_id", outdoorMaterialPhotoId);
            photoInfoService.createPhotoinfo(outdoorMaterialPhotoMap);
        }
        PhotoInfo outdoorOnlyScenePhoto = req.getOutdoorOnlyScenePhoto();
        if (outdoorOnlyScenePhoto != null && WosaiStringUtils.isNotEmpty(outdoorOnlyScenePhoto.getUrl())) {
            String outdoorOnlyScenePhotoId = UUID.randomUUID().toString();
            outdoorOnlyScenePhoto.setId(outdoorOnlyScenePhotoId);
            outdoorOnlyScenePhoto.setUrl(PhotoUtils.baseUrl(outdoorOnlyScenePhoto.getUrl()));

            Map outdoorOnlyScenePhotoMap = MyBeanUtil.toMap(outdoorOnlyScenePhoto);
            storeExt.put("outdoor_only_scene_photo_id", outdoorOnlyScenePhotoId);
            photoInfoService.createPhotoinfo(outdoorOnlyScenePhotoMap);
        }
        PhotoInfo productPrice = req.getProductPrice();
        if (productPrice != null && WosaiStringUtils.isNotEmpty(productPrice.getUrl())) {
            String productPriceId = UUID.randomUUID().toString();
            productPrice.setId(productPriceId);
            productPrice.setUrl(PhotoUtils.baseUrl(productPrice.getUrl()));

            Map productPriceMap = MyBeanUtil.toMap(productPrice);
            storeExt.put("product_price_id", productPriceId);
            photoInfoService.createPhotoinfo(productPriceMap);
        }
        PhotoInfo auditPicture = req.getAuditPicture();
        if (auditPicture != null && WosaiStringUtils.isNotEmpty(auditPicture.getUrl())) {
            String auditPictureId = UUID.randomUUID().toString();
            auditPicture.setId(auditPictureId);
            auditPicture.setUrl(PhotoUtils.baseUrl(auditPicture.getUrl()));

            Map auditPictureMap = MyBeanUtil.toMap(auditPicture);
            storeExt.put("audit_picture_id", auditPictureId);
            photoInfoService.createPhotoinfo(auditPictureMap);
        }
        StringJoiner otherPhotos = new StringJoiner(",");
        List<PhotoInfo> otherPhoto = req.getOtherPhoto();
        if (CollectionUtils.isNotEmpty(otherPhoto)) {
            for (PhotoInfo otherPhotoInfo : otherPhoto) {
                if (otherPhotoInfo != null && WosaiStringUtils.isNotEmpty(otherPhotoInfo.getUrl())) {
                    String otherPhotoInfoId = UUID.randomUUID().toString();
                    otherPhotoInfo.setId(otherPhotoInfoId);
                    otherPhotoInfo.setUrl(PhotoUtils.baseUrl(otherPhotoInfo.getUrl()));

                    Map otherPhotoInfoMap = MyBeanUtil.toMap(otherPhotoInfo);
                    photoInfoService.createPhotoinfo(otherPhotoInfoMap);
                    otherPhotos.add(otherPhotoInfoId);
                }
            }
        }
        storeExt.put("other_photo_id", WosaiStringUtils.isEmpty(otherPhotos.toString()) ? null : otherPhotos.toString());

        StringJoiner orderPricePhotos = new StringJoiner(",");
        List<PhotoInfo> orderPricePhoto = req.getOrderPricePhoto();
        if (CollectionUtils.isNotEmpty(orderPricePhoto)) {
            for (PhotoInfo priceInfo : orderPricePhoto) {
                if (priceInfo != null && WosaiStringUtils.isNotEmpty(priceInfo.getUrl())) {
                    String priceInfoId = UUID.randomUUID().toString();
                    priceInfo.setId(priceInfoId);
                    priceInfo.setUrl(PhotoUtils.baseUrl(priceInfo.getUrl()));

                    Map otherPhotoInfoMap = MyBeanUtil.toMap(priceInfo);
                    photoInfoService.createPhotoinfo(otherPhotoInfoMap);
                    orderPricePhotos.add(priceInfoId);
                }
            }
        }
        storeExt.put("order_price_photo_id", WosaiStringUtils.isEmpty(orderPricePhotos.toString()) ? null : orderPricePhotos.toString());

        storeExt.put("store_id", store.get("id"));
        Map storeExtMsg = GetRealObject.filterParams(createStoreReq, GetRealObject.STORE_EXT);
        storeExt.putAll(storeExtMsg);
        storeExtService.createStoreExt(storeExt);
        StoreInfo storeInfo = JSONObject.parseObject(JSONObject.toJSONString(store), StoreInfo.class);
        return storeInfo;
    }

    public void updateStorePicturesByStoreId(UpdateStorePicturesReq req, String devCode) {
        Map storeExtByStoreId = storeExtService.findStoreExtByStoreId(req.getStoreId());
        String id = "-1";
        if (MapUtils.isNotEmpty(storeExtByStoreId)) {
            id = String.valueOf(storeExtByStoreId.get("id"));
        }
        Map storeExt = storeExtBiz.findStoreExtMap(id, devCode);
        Map storeExtNew = new HashMap(8);
        storeExtNew.put("store_id", req.getStoreId());
        if (req.getAuditPicture() != null && WosaiStringUtils.isNotEmpty(req.getAuditPicture().getUrl())) {
            String auditPictureId = this.createOrUpdatePhoto(req.getAuditPicture(), storeExt, "audit_picture_id", devCode);
            storeExtNew.put("audit_picture_id", auditPictureId);
        }
        if (req.getBrandPhoto() != null && !isDelPhoto(req.getBrandPhoto())) {
            String brandPhotoId = this.createOrUpdatePhoto(req.getBrandPhoto(), storeExt, "brand_photo_id", devCode);
            storeExtNew.put("brand_photo_id", brandPhotoId);
        } else if (isDelPhoto(req.getBrandPhoto())) {
            //传null会导致序列化为字符串时丢失key.导致后续查询时,key被原表的覆盖,查询最新数据时,又查到原表数据.
            storeExtNew.put("brand_photo_id", "");

        }
        if (req.getBrandOnlyScenePhoto() != null && !isDelPhoto(req.getBrandOnlyScenePhoto())) {
            String brandOnlyScenePhotoId = this.createOrUpdatePhoto(req.getBrandOnlyScenePhoto(), storeExt, "brand_only_scene_photo_id", devCode);
            storeExtNew.put("brand_only_scene_photo_id", brandOnlyScenePhotoId);
        } else if (isDelPhoto(req.getBrandOnlyScenePhoto())) {
            storeExtNew.put("brand_only_scene_photo_id", "");
        }
        if (req.getOutdoorMaterialPhoto() != null && !isDelPhoto(req.getOutdoorMaterialPhoto())) {
            String outdoorMaterialPhotoId = this.createOrUpdatePhoto(req.getOutdoorMaterialPhoto(), storeExt, "outdoor_material_photo_id", devCode);
            storeExtNew.put("outdoor_material_photo_id", outdoorMaterialPhotoId);
        } else if (isDelPhoto(req.getOutdoorMaterialPhoto())) {
            storeExtNew.put("outdoor_material_photo_id", "");
        }
        if (req.getOutdoorOnlyScenePhoto() != null && WosaiStringUtils.isNotEmpty(req.getOutdoorOnlyScenePhoto().getUrl())) {
            String outdoorOnlyScenePhotoId = this.createOrUpdatePhoto(req.getOutdoorOnlyScenePhoto(), storeExt, "outdoor_only_scene_photo_id", devCode);
            storeExtNew.put("outdoor_only_scene_photo_id", outdoorOnlyScenePhotoId);
        }
        if (req.getIndoorMaterialPhoto() != null && !isDelPhoto(req.getIndoorMaterialPhoto())) {
            String indoorMaterialPhotoId = this.createOrUpdatePhoto(req.getIndoorMaterialPhoto(), storeExt, "indoor_material_photo_id", devCode);
            storeExtNew.put("indoor_material_photo_id", indoorMaterialPhotoId);
        } else if (isDelPhoto(req.getIndoorMaterialPhoto())) {
            storeExtNew.put("indoor_material_photo_id", "");
        }
        if (req.getIndoorOnlyScenePhoto() != null && WosaiStringUtils.isNotEmpty(req.getIndoorOnlyScenePhoto().getUrl())) {
            String indoorOnlyScenePhotoId = this.createOrUpdatePhoto(req.getIndoorOnlyScenePhoto(), storeExt, "indoor_only_scene_photo_id", devCode);
            storeExtNew.put("indoor_only_scene_photo_id", indoorOnlyScenePhotoId);
        }
        if (req.getProductPrice() != null && WosaiStringUtils.isNotEmpty(req.getProductPrice().getUrl())) {
            String productPriceId = this.createOrUpdatePhoto(req.getProductPrice(), storeExt, "product_price_id", devCode);
            storeExtNew.put("product_price_id", productPriceId);
        }
        List<String> newOrderPricePhotoIds = new ArrayList<>(3);
        if (req.getOrderPricePhoto() != null) {
            this.createOrUpdateOtherPhoto(newOrderPricePhotoIds, req.getOrderPricePhoto(), devCode);
        }
        if (WosaiCollectionUtils.isNotEmpty(newOrderPricePhotoIds)) {
            storeExtNew.put("order_price_photo_id", String.join(",", newOrderPricePhotoIds));
        } else if (isDelPhoto(req.getOrderPricePhoto())) {
            storeExtNew.put("order_price_photo_id", null);
        }

        List<String> newOtherPhotoIds = new ArrayList<>();
        if (req.getOtherPhoto() != null) {
            this.createOrUpdateOtherPhoto(newOtherPhotoIds, req.getOtherPhoto(), devCode);
        }
        if (WosaiCollectionUtils.isNotEmpty(newOtherPhotoIds)) {
            storeExtNew.put("other_photo_id", String.join(",", newOtherPhotoIds));
        } else if (isDelPhoto(req.getOtherPhoto())) {
            storeExtNew.put("other_photo_id", null);
        }

        if (storeExt != null) {
            storeExtNew.put("id", storeExt.get("id"));
            storeExtBiz.updateStoreExtPictureIds(storeExtNew, devCode);
            this.deleteOtherPhoto(storeExt, newOtherPhotoIds, "other_photo_id");
            this.deleteOtherPhoto(storeExt, newOrderPricePhotoIds, "order_price_photo_id");
        } else {
            storeExtService.createStoreExt(storeExtNew);
        }
    }

    /**
     * 是否是进行删除操作,和crm约定,图片为NULL不做操作,"" 时为删除操作
     *
     * @param photoInfos
     * @return
     */
    private boolean isDelPhoto(List<PhotoInfo> photoInfos) {
        if (photoInfos == null) return false;

        if (photoInfos.size() == 0) return true;


        //url都为 "" ,则认为是删除图片,NULL不做操作,有非空图片url时,之前已更新过图片
        boolean isDelPhoto = photoInfos.stream().allMatch(ph -> WosaiStringUtils.equals("", ph.getUrl()));

        if (isDelPhoto) {
            try {
                photoInfos.stream().filter(ph -> WosaiStringUtils.isNotEmpty(ph.getId())).forEach(ph -> photoInfoService.deletePhotoinfo(ph.getId()));
            } catch (Exception e) {
                log.error("删除图片异常, {}", photoInfos, e);
            }
        }
        return isDelPhoto;

    }

    private boolean isDelPhoto(PhotoInfo photoInfo) {
        if (photoInfo == null) return false;

        boolean isDelPhoto = "".equals(photoInfo.getUrl());
        if (isDelPhoto) {
            try {
                photoInfoService.deletePhotoinfo(photoInfo.getId());
            } catch (Exception e) {
                log.error("删除图片异常, {}", photoInfo, e);
            }
        }

        return isDelPhoto;
    }

    /**
     * 删除其他照片 和 点单价目表
     * 逻辑：将原来表中的其他照片id和这次创建或更新的其他照片id做比较
     * 将这次没有涉及到的其他照片id进行删除
     * 前提：查询照片必须获取应该获取正确的数据(最新？)，这样没有传过来的其他照片id肯定是要删除的
     * 排除：若此storeExtInfo中图片id对应的数据dev_code为null则不删除，因为这是第一次创建时的其他照片，等copy时再删除
     *
     * @param storeExtInfo     store_ext表信息
     * @param newOtherPhotoIds 这次更新的其他照片id
     */
    private void deleteOtherPhoto(Map storeExtInfo, List<String> newOtherPhotoIds, String key) {
        String otherPhotoIds = MapUtils.getString(storeExtInfo, key);
        if (WosaiStringUtils.isEmpty(otherPhotoIds)) {
            return;
        }
        List<String> oldOtherPhotoIds = Arrays.stream(otherPhotoIds.split(",")).collect(Collectors.toList());
        oldOtherPhotoIds.removeAll(newOtherPhotoIds);
        if (WosaiCollectionUtils.isNotEmpty(oldOtherPhotoIds)) {
            Map photoinfo = photoInfoService.findPhotoinfo(oldOtherPhotoIds.get(0), null);
            if (MapUtils.isNotEmpty(photoinfo) && WosaiStringUtils.isEmpty((String) photoinfo.get("dev_code"))) {
                return;
            }
        }
        if (WosaiCollectionUtils.isNotEmpty(oldOtherPhotoIds)) {
            photoInfoService.deletePhotoInfos(oldOtherPhotoIds);
        }
    }

    /**
     * 创建或更新其他照片
     * 逻辑：如果有id -> 根据id和dev_code查询到数据则更新
     * -> 根据id和dev_code查询不到数据则新增
     * 如果没有id -> 直接新增
     *
     * @param newOtherPhotoIds 更新后其他照片的id集合
     * @param photoInfos       其他照片信息
     * @param devCode          业务方标识
     */
    private void createOrUpdateOtherPhoto(List<String> newOtherPhotoIds, List<PhotoInfo> photoInfos, String devCode) {
        if (CollectionUtils.isNotEmpty(photoInfos)) {
            for (PhotoInfo otherPhotoInfo : photoInfos) {
                if (otherPhotoInfo != null && WosaiStringUtils.isNotEmpty(otherPhotoInfo.getUrl())) {
                    String otherPhotoId = "";
                    if (WosaiStringUtils.isNotEmpty(otherPhotoInfo.getId())) {
                        PhotoInfo photoInfo = otherPhotoInfo.setDev_code(devCode);
                        otherPhotoId = mcPhotoInfoService.updatePhotoInfo(photoInfo);
                    } else {
                        otherPhotoId = UUID.randomUUID().toString();
                        otherPhotoInfo.setId(otherPhotoId).setDev_code(devCode);
                        otherPhotoInfo.setUrl(PhotoUtils.baseUrl(otherPhotoInfo.getUrl()));

                        Map otherPhotoInfoMap = MyBeanUtil.toMap(otherPhotoInfo);
                        photoInfoService.createPhotoinfo(otherPhotoInfoMap);
                    }
                    newOtherPhotoIds.add(otherPhotoId);
                }
            }
        }
    }

    /**
     * 创建或更新照片
     * 逻辑：如果有id -> 根据id和dev_code查询到数据则更新
     * -> 根据id和dev_code查询不到数据则新增
     * 如果没有id -> 判断是否存在url，若存在直接新增  不存在则不创建
     *
     * @param photoInfoReq 照片信息
     * @param storeExt     store_ext表信息
     * @param fieldName    字段名
     * @param devCode      业务方标识
     * @return 此次更新照片得到的id
     */
    private String createOrUpdatePhoto(PhotoInfo photoInfoReq, Map storeExt, String fieldName, String devCode) {
        String photoInfoId = "";
        if (storeExt != null && StringUtils.isNotEmpty((CharSequence) storeExt.get(fieldName))) {
            PhotoInfo photoInfo = photoInfoReq.setId((String) storeExt.get(fieldName)).setDev_code(devCode);
            photoInfoId = mcPhotoInfoService.updatePhotoInfo(photoInfo);
        } else if (WosaiStringUtils.isNotEmpty(photoInfoReq.getId())) {
            PhotoInfo photoInfo = photoInfoReq.setDev_code(devCode);
            photoInfoId = mcPhotoInfoService.updatePhotoInfo(photoInfo);
        } else {
            if (WosaiStringUtils.isEmpty(photoInfoReq.getUrl())) {
                return null;
            } else {
                photoInfoReq.setUrl(PhotoUtils.baseUrl(photoInfoReq.getUrl()));
                Map map = MyBeanUtil.toMap(photoInfoReq);
                Map auditPicture = new HashMap();
                photoInfoId = UUID.randomUUID().toString();
                auditPicture = getPictureValues(map, auditPicture, photoInfoId, devCode);
                photoInfoService.createPhotoinfo(auditPicture);
            }
        }
        return photoInfoId;
    }

    private Map getPictureValues(Map map, Map productPrice, String pictureId, String devCode) {
        productPrice.put("id", pictureId);
        productPrice.put("dev_code", devCode);
        productPrice.putAll(map);
        return productPrice;
    }


    public StoreInfo getStoreById(String id, String devCode) {
        Map store = null;
        if (StringUtils.isNotEmpty(devCode)) {
            store = mcPreBiz.getMcPreData(id, devCode, TableNameEnum.STORE.getTableName());
        }
        if (MapUtils.isEmpty(store)) {
            store = storeService.getStoreByStoreId(id);
        }
        if (MapUtils.isEmpty(store)) {
            return null;
        }
        replaceStorePoi(id, store);
        return JSONObject.parseObject(JSONObject.toJSONString(store), StoreInfo.class);
    }

    public StoreInfo getLatestStoreById(String id) {
        Map store = storeService.getStoreByStoreId(id);
        store = mcPreBiz.mergeMcPre(id, store, TableNameEnum.STORE.getTableName());
        replaceStorePoi(id, store);
        return JSONObject.parseObject(JSONObject.toJSONString(store), StoreInfo.class);
    }

    public StoreInfo getStoreBySn(String storeSn, String devCode) {
        Map store = storeService.getStoreByStoreSn(storeSn);
        if (StringUtils.isNotEmpty(devCode) && MapUtils.isNotEmpty(store)) {
            Map mcStore = mcPreBiz.getMcPreData((String) store.get("id"), devCode, TableNameEnum.STORE.getTableName());
            if (MapUtils.isNotEmpty(mcStore)) {
                store = mcStore;
            }
        }
        if (MapUtils.isEmpty(store)) {
            return null;
        }
        replaceStorePoi((String) store.get("id"), store);
        return JSONObject.parseObject(JSONObject.toJSONString(store), StoreInfo.class);
    }

    // 不再使用poi中心数据  https://jira.wosai-inc.com/browse/CUA-4298
    private void replaceStorePoi(String id, Map store) {
//        StorePoi storePoi = null;
//        try {
//            storePoi = storePoiService.findStorePoi(id);
//        } catch (Exception e) {
//            log.error("查询poi服务异常,id为{}", id, e);
//        }
//        if (storePoi != null) {
//            store.put("longitude", storePoi.getLongitude());
//            store.put("latitude", storePoi.getLatitude());
//        }
    }

    public void convertField(FieldStatusResp resp) {
        resp.setVerifying(fieldMapping(resp.getVerifying()));
        resp.setVerifyFailed(fieldMapping(resp.getVerifyFailed()));
        resp.setVerifySuccess(fieldMapping(resp.getVerifySuccess()));
    }

    private List fieldMapping(List<String> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return null;
        }
        List<String> list = new ArrayList();
        for (String s : fields) {
            if (WosaiStringUtils.isEmpty(s)) {
                continue;
            }
            if (s.endsWith("id")) {
                list.add(s.substring(0, s.lastIndexOf("_")));
            } else {
                list.add(s);
            }
        }
        return list;
    }

    public Map getFirstStoreByMerchantId(String merchantId) {
        if (WosaiStringUtils.isEmpty(merchantId)) return null;

        PageInfo pageInfo = new PageInfo(1, 1);
        pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));

        ListResult stores = storeService.getStoreListByMerchantId(merchantId, pageInfo, null);
        if (Objects.nonNull(stores) && stores.getTotal() > 0) {
            return stores.getRecords().get(0);
        }
        return null;
    }

    private static final List<String> NEED_SYNC_KEY = Arrays.asList(Store.PROVINCE, Store.CITY, Store.DISTRICT, Store.DISTRICT_CODE, Store.STREET_ADDRESS, Store.LATITUDE, Store.LONGITUDE, Store.EXTRA, Store.STREET_ADDRESS_DESC);

    public void handleBindStore(Map updateStore, boolean needLog, LogReq logReq) {
        //无地址更新不检查同步
        if (NEED_SYNC_KEY.stream().noneMatch(key -> updateStore.containsKey(key) && updateStore.get(key) != null)) {
            return;
        }
        String storeId = MapUtils.getString(updateStore, DaoConstants.ID);
        String storeSn = MapUtils.getString(updateStore, Store.SN);
        Map store = null;
        if (WosaiStringUtils.isNotEmpty(storeId)) {
            store = storeService.getStore(storeId);
        } else if (WosaiStringUtils.isNotEmpty(storeSn)) {
            store = storeService.getStoreByStoreSn(storeSn);
        } else {
            throw new CommonPubBizException("门店id和门店号不可同时为空");
        }

        String merchantId = MapUtils.getString(store, Store.MERCHANT_ID);

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        //是绑定关系,同步地址
        if (WosaiStringUtils.equals(MapUtils.getString(store, DaoConstants.ID), MapUtils.getString(merchant, MerchantConstant.BINDED_STORE_ID))) {
            Map storeExtra = (Map) store.get(Store.EXTRA);
            Map updateStoreExtra = (Map) updateStore.get(Store.EXTRA);
            Map merchantExtra = (Map) merchant.get(Merchant.EXTRA) == null ? new HashMap<>() : (Map) merchant.get(Merchant.EXTRA);
            //有poi_address则也同步一下
            if (MapUtils.isNotEmpty(updateStoreExtra) && updateStoreExtra.containsKey("poi_address")) {
                //优先取 updateStoreExtra
                merchantExtra.put("poi_address", MapUtils.getString(updateStoreExtra, "poi_address"));
            } else if (MapUtils.isNotEmpty(storeExtra) && storeExtra.containsKey("poi_address")) {
                merchantExtra.put("poi_address", MapUtils.getString(storeExtra, "poi_address"));
            }
            Map updateMerchantReq = CollectionUtil.hashMap(Merchant.PROVINCE, MapUtils.getString(updateStore, Store.PROVINCE),
                    Merchant.CITY, MapUtils.getString(updateStore, Store.CITY),
                    Merchant.DISTRICT, MapUtils.getString(updateStore, Store.DISTRICT),
                    Merchant.STREET_ADDRESS, MapUtils.getString(updateStore, Store.STREET_ADDRESS),
                    Merchant.STREET_ADDRESS_DESC, MapUtils.getString(updateStore, Store.STREET_ADDRESS_DESC),
                    Merchant.LATITUDE, MapUtils.getString(updateStore, Store.LATITUDE),
                    Merchant.LONGITUDE, MapUtils.getString(updateStore, Store.LONGITUDE),
                    Merchant.DISTRICT_CODE, MapUtils.getString(updateStore, Store.DISTRICT_CODE),
                    Merchant.EXTRA, merchantExtra,

                    DaoConstants.ID, MapUtils.getString(merchant, DaoConstants.ID));

            merchantService.updateMerchant(updateMerchantReq);
        }
    }

    public void checkBindWhenCreateStore(Map store) {
        String merchantId = MapUtils.getString(store, Store.MERCHANT_ID);
        ListResult simpleStores = storeService.getSimpleStoreListByMerchantId(merchantId, null);

        //创建门店后,门店记录已大于1,说明目前已为多门店情况,不进行默认绑定
        if (simpleStores.getTotal() > 1) return;

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        //商户未存在绑定关系,且详细地址一致,才进行绑定
        if (merchant.get(MerchantConstant.BINDED_STORE_ID) == null &&
                WosaiStringUtils.equals(MapUtils.getString(merchant, Merchant.STREET_ADDRESS), MapUtils.getString(store, Store.STREET_ADDRESS))) {
            merchantService.updateMerchant(CollectionUtil.hashMap(MerchantConstant.BINDED_STORE_ID, MapUtils.getString(simpleStores.getRecords().get(0), DaoConstants.ID),
                    DaoConstants.ID, merchantId));
        }
    }

    /**
     * 拦截校验,某个key有进行中的申请单,则无法更新
     *
     * @param updateStore 本次提交的新数据
     */
    public void checkProcessStoreCrmApply(String storeId, Map<String, Object> updateStore) {
        Map store = storeService.getStoreByStoreId(storeId);
        ListResult infos = commonFieldService.findFieldAppInfos(
                new PageInfo(1, 15),
                CollectionUtil.hashMap("store_id", storeId, "merchant_id", MapUtils.getString(store, Store.MERCHANT_ID),
                        "statuses", Arrays.asList(CrmApplyConstant.STATUS_INIT, CrmApplyConstant.STATUS_PENDING, CrmApplyConstant.STATUS_FAIL),
                        "un_contain_field_types", Arrays.asList("industry", "address", "business_license"),
                        //商户场景传0 门店场景传1
                        "field_main_type", 1
                )
        );
        if (WosaiCollectionUtils.isNotEmpty(infos.getRecords())) {
            List<Map> records = infos.getRecords();
            for (Map record : records) {

                Map info = (Map) record.get(CrmApplyConstant.SUBMIT_INFO);
                List<Map<String, Object>> infoList = (List) info.get(CrmApplyConstant.FIELD_LIST);
                for (Map<String, Object> processingMap : infoList) {
                    for (Map.Entry<String, Object> entry : updateStore.entrySet()) {
                        if (processingMap.containsKey(entry.getKey()) && !Objects.equals(entry.getValue(), processingMap.get(entry.getKey()))) {
                            throw new com.wosai.upay.common.exception.CommonPubBizException("有正在进行的变更申请单,无法编辑");
                        }
                    }
                }

            }

        }
    }
}
