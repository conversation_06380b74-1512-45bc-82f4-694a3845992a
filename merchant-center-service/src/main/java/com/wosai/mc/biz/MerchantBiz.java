package com.wosai.mc.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.constant.LogParamsConstant;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.constants.CrmApplyConstant;
import com.wosai.mc.constants.LogConstant;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.LogReq;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.WeixinParamsUpdateApply;
import com.wosai.upay.job.service.ContractWeixinService;
import com.wosai.upay.merchant.contract.constant.UnionBusinessFileds;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/18
 */
@Component
@Slf4j
public class MerchantBiz {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private McPreBiz mcPreBiz;

    @Autowired
    private MerchantBusinessLicenseService coreBlicenseService;

    @Autowired
    private StoreService coreBstoreService;

    @Autowired
    private CommonApolloConfigs apolloConfigs;
    @Autowired
    private LogBiz logBiz;
    @Autowired
    private StoreService storeService;
    @Autowired
    private CommonFieldService commonFieldService;
    @Autowired
    private ContractWeixinService contractWeixinService;
    @Autowired
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;

    @Autowired
    private PoiBiz poiBiz;

    public MerchantInfo createMerchant(CreateMerchantReq req) {
        poiBiz.checkAndFillPoi(req);
        Map merchant = MyBeanUtil.toMap(req);
        merchant = merchantService.createMerchantForMerchantCenter(merchant);
        return JSONObject.parseObject(JSONObject.toJSONString(merchant), MerchantInfo.class);
    }

    public void updateMerchant(UpdateMerchantReq req, String devCode, LogReq logReq) {
        Map newMerchant = MyBeanUtil.toMap(req);
        Map existMerchant = getMerchantById(req.getId(), devCode);
        if (WosaiMapUtils.isEmpty(existMerchant)) {
            throw new CommonPubBizException("商户不存在");
        }
        Map newExtra = WosaiMapUtils.getMap(newMerchant, "extra", new HashMap());
        Map oldExtra = WosaiMapUtils.getMap(existMerchant, "extra", new HashMap());
        oldExtra.putAll(newExtra);
        newMerchant.put("extra", oldExtra);
        boolean needLog = logReq != null && logReq.isValid();

        if (StringUtils.isNotEmpty(devCode)) {
            existMerchant.putAll(newMerchant);
            if (needLog) {
                existMerchant.putAll(MyBeanUtil.toMap(logReq));
                existMerchant.put(LogConstant.NEED_LOG, true);
            }
            mcPreBiz.recordMcPre(TableNameEnum.MERCHANT.getTableName(), req.getId(), devCode, existMerchant);
        } else {
            handleBindedStore(newMerchant, existMerchant);
            Map originalMerchant = merchantService.getMerchantByMerchantId(req.getId());
            newMerchant = GetRealObject.filterParams(newMerchant, GetRealObject.MERCHANT_DATA);
            if (!req.is_crm()) {
                checkProcessMerchantCrmApply(req.getId(), newMerchant);
            }
            if (WosaiStringUtils.isNotEmpty(req.getCustomerPhone())) {

                try {
                    //客服电话为特殊字段,通过该接口单独更新字段,才能让前端调用的校验接口正常校验
                    Map resp = contractWeixinService.updateWeixinParams(
                            CollectionUtil.hashMap(WeixinParamsUpdateApply.MERCHANT_ID, req.getId(),
                                    UnionBusinessFileds.SERVICE_PHONE, req.getCustomerPhone())
                    );
                    if (!CommonModel.RESULT_CODE_SUCCESS.equals(MapUtils.getString(resp, CommonModel.RESULT_CODE))) {
                        throw new CommonPubBizException(MapUtils.getString(resp, CommonModel.RESULT_MESSAGE));
                    }
                } catch (Exception e) {
                    //之前只有1个单独入口需要同步客服电话, 商户中心这边先忽略掉同步失败的情况.
                    log.info("同步客服电话失败, 商户id{} ", req.getId(), e);
                }

            }
            merchantService.updateMerchant(newMerchant);
            if (needLog) {
                //记日志
                logBiz.saveLog(originalMerchant, newMerchant, TableNameEnum.MERCHANT.getTableName(), req.getId(), null, logReq);
            }
            //检查中间表
            Map mcPre = mcPreBiz.findMcPre(TableNameEnum.MERCHANT.getTableName(), req.getId());
            if (WosaiMapUtils.isNotEmpty(mcPre)) {
                Map data = CommonUtils.beanToTargetObj(mcPre.get("data"), Map.class);
                if (WosaiMapUtils.isNotEmpty(data)) {
                    data.putAll(newMerchant);
                    mcPreBiz.recordMcPre(TableNameEnum.MERCHANT.getTableName(), req.getId(), "updateOrigin", data);
                }
            }

        }
    }

    public MerchantInfo getMerchantInfoById(String merchantId, String devCode) {
        Map merchant = getMerchantById(merchantId, devCode);
        if (MapUtils.isEmpty(merchant)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(merchant), MerchantInfo.class);
    }

    public Map getMerchantById(String merchantId, String devCode) {
        Map merchant = null;
        if (StringUtils.isNotEmpty(devCode)) {
            merchant = mcPreBiz.getMcPreData(merchantId, devCode, TableNameEnum.MERCHANT.getTableName());
        }
        if (MapUtils.isEmpty(merchant)) {
            merchant = merchantService.getMerchant(merchantId);
        }
        return merchant;
    }

    public List<MerchantInfo> getMerchantListByMerchantIds(List<String> merchantIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("merchant_ids", merchantIds);
        PageInfo pageInfo = new PageInfo(1, merchantIds.size());
        ListResult merchantResult = merchantService.findMerchants(pageInfo, params);
        List<Map> merchants = merchantResult.getRecords();
        return JSON.parseArray(JSON.toJSONString(merchants), MerchantInfo.class);
    }


    public MerchantInfo getMerchantBySn(String merchantSn, String devCode) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (StringUtils.isNotEmpty(devCode) && MapUtils.isNotEmpty(merchant)) {
            String merchantId = (String) merchant.get("id");
            Map mcMerchant = mcPreBiz.getMcPreData(merchantId, devCode, TableNameEnum.MERCHANT.getTableName());
            if (MapUtils.isNotEmpty(mcMerchant)) {
                merchant = mcMerchant;
            }
        }
        if (MapUtils.isEmpty(merchant)) {
            return null;
        }
        return JSONObject.parseObject(JSONObject.toJSONString(merchant), MerchantInfo.class);

    }

    public int resolveBlackKey(String merchantId, Boolean blacklist) {
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        merchant = GetRealObject.filterParams(merchant, apolloConfigs.getMerchantFieldFilter());
        Map<String, Object> license = coreBlicenseService.getBusinessLicenseByMerchantId(merchantId);
        license = GetRealObject.filterParams(license, GetRealObject.MERCHANT_BUSINESS_LICENSE_DATA);
        List<Map> records = coreBstoreService.getStoreListByMerchantId(merchantId, new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy("ctime", OrderBy.OrderType.ASC))), null).getRecords();
        Map store = new HashMap();
        if (CollectionUtils.isNotEmpty(records) && MapUtils.isNotEmpty(records.get(0))) {
            store = GetRealObject.filterParams(records.get(0), GetRealObject.STORE_DATA);
        }
        if (blacklist) {
            if (addBlackKey(merchant)) {
                merchantService.updateMerchant(merchant);
            }
            if (addBlackKey(license)) {
                coreBlicenseService.updateMerchantBusinessLicense(license);
            }
            if (MapUtils.isNotEmpty(store)) {
                if (addBlackKey(store)) {
                    coreBstoreService.updateStore(store);
                }
            }
        } else {
            if (delBlackKey(merchant)) {
                merchantService.updateMerchant(merchant);
            }
            if (delBlackKey(license)) {
                coreBlicenseService.updateMerchantBusinessLicense(license);
            }
            if (MapUtils.isNotEmpty(store)) {
                if (delBlackKey(store)) {
                    coreBstoreService.updateStore(store);
                }
            }
        }

        return 1;
    }


    /**
     * @param map
     * @return true 有数据修改  false 没有数据修改
     */
    private boolean delBlackKey(Map map) {
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        Map extra = (Map) map.get(com.wosai.upay.core.model.Merchant.EXTRA);
        if (MapUtils.isEmpty(extra)) {
            return false;
        }
        boolean black = BeanUtil.getPropBoolean(extra, MerchantConstant.BLACK, false);
        if (!black) {
            return false;
        }
        if (extra.containsKey(MerchantConstant.BLACK)) {
            extra.put(MerchantConstant.BLACK, false);
        }
        return true;
    }

    /**
     * @param map
     * @return true 有数据修改  false 没有数据修改
     */
    private boolean addBlackKey(Map map) {
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        Map extra = (Map) map.get(com.wosai.upay.core.model.Merchant.EXTRA);
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap();
        }
        boolean black = BeanUtil.getPropBoolean(extra, MerchantConstant.BLACK, false);
        if (black) {
            return false;
        }

        extra.put(MerchantConstant.BLACK, true);
        map.put(com.wosai.upay.core.model.Merchant.EXTRA, extra);
        return true;
    }

    private static final List<String> NEED_SYNC_KEY = Arrays.asList(Merchant.PROVINCE, Merchant.CITY, Merchant.DISTRICT, Merchant.DISTRICT_CODE, Merchant.STREET_ADDRESS, Merchant.LATITUDE, Merchant.LONGITUDE, Merchant.STREET_ADDRESS_DESC);

    /**
     * 有绑定关系,门店-->商户
     */
    public void handleBindedStore(Map updateMerchant, Map originalMerchant) {

        //本次更新是否指定新的门店
        String bindStoreId = MapUtils.getString(updateMerchant, MerchantConstant.BINDED_STORE_ID);
        String oldBindStoreId = MapUtils.getString(originalMerchant, MerchantConstant.BINDED_STORE_ID);

        if (bindStoreId == null && WosaiStringUtils.isNotEmpty(oldBindStoreId)) {
            //不更新绑定门店id,且现有绑定的门店id,无法单独更新商户地址
            NEED_SYNC_KEY.forEach(key -> updateMerchant.remove(key));
        }
        //未指定(""除外,去掉绑定关系)的情况下,查看是否已有绑定关系
        if (bindStoreId == null) {
            return;
        }

        if ("".equals(bindStoreId)) {
            updateMerchant.put(Merchant.BINDED_STORE_ID, "");


        } else {
            Map store = storeService.getStoreByStoreId(bindStoreId);
            updateMerchant.put(Merchant.BINDED_STORE_ID, bindStoreId);
            updateMerchant.put(Merchant.PROVINCE, MapUtils.getString(store, Store.PROVINCE));
            updateMerchant.put(Merchant.CITY, MapUtils.getString(store, Store.CITY));
            updateMerchant.put(Merchant.DISTRICT, MapUtils.getString(store, Store.DISTRICT));
            updateMerchant.put(Merchant.DISTRICT_CODE, MapUtils.getString(store, Store.DISTRICT_CODE));
            updateMerchant.put(Merchant.STREET_ADDRESS, MapUtils.getString(store, Store.STREET_ADDRESS));
            if (WosaiStringUtils.isEmpty(MapUtils.getString(store, Store.LATITUDE)) || WosaiStringUtils.isEmpty(MapUtils.getString(store, Store.LONGITUDE))) {
                //绑定的该门店没有经纬度,则不进行经纬度同步.且如果此时有经纬度信息,补全到门店
                if (WosaiStringUtils.isNotEmpty(MapUtils.getString(updateMerchant, Merchant.LATITUDE)) || WosaiStringUtils.isNotEmpty(MapUtils.getString(updateMerchant, Merchant.LONGITUDE))) {
                    storeService.updateStore(CollectionUtil.hashMap(
                            DaoConstants.ID, bindStoreId,
                            Store.LATITUDE, MapUtils.getString(updateMerchant, Merchant.LATITUDE),
                            Store.LONGITUDE, MapUtils.getString(updateMerchant, Merchant.LONGITUDE)
                    ));
                }
            } else {
                //门店经纬度正常同步到商户
                updateMerchant.put(Merchant.LATITUDE, MapUtils.getString(store, Store.LATITUDE));
                updateMerchant.put(Merchant.LONGITUDE, MapUtils.getString(store, Store.LONGITUDE));
            }

            updateMerchant.put(Merchant.STREET_ADDRESS_DESC, MapUtils.getString(store, Store.STREET_ADDRESS_DESC));
            Map storeExtra = (Map) store.get(Store.EXTRA);
            Map merchantExtra = (Map) originalMerchant.get(Merchant.EXTRA) == null ? new HashMap<>() : (Map) originalMerchant.get(Merchant.EXTRA);
            Map updateMerchantExtra = (Map) updateMerchant.get(Merchant.EXTRA);
            //有poi_address则也同步一下
            if (MapUtils.isNotEmpty(storeExtra) && storeExtra.containsKey("poi_address")) {
                //此次更新extra,直接使用update内数据
                if (updateMerchantExtra != null) {
                    updateMerchantExtra.put("poi_address", MapUtils.getString(storeExtra, "poi_address"));

                } else {
                    //不更新extra,则取原来的extra进行覆盖
                    merchantExtra.put("poi_address", MapUtils.getString(storeExtra, "poi_address"));
                    updateMerchant.put(Merchant.EXTRA, merchantExtra);

                }
            }
        }

        Map mcMerchant = getMerchantById(MapUtils.getString(originalMerchant, DaoConstants.ID), "handleBindedStore");
        if (WosaiMapUtils.isNotEmpty(mcMerchant)) {
            mcMerchant.putAll(updateMerchant);
            mcPreBiz.recordMcPre(TableNameEnum.MERCHANT.getTableName(), MapUtils.getString(originalMerchant, DaoConstants.ID), "handleBindedStore", mcMerchant);
        }
//        Map mcStore = mcPreBiz.getMcPreData(bindStoreId, "mcMerchant_To_Store", TableNameEnum.STORE.getTableName());
//        if (WosaiMapUtils.isNotEmpty(mcStore)) {
//            mcStore.put(Store.PROVINCE, MapUtils.getString(updateMerchant, Merchant.PROVINCE));
//            mcStore.put(Store.CITY, MapUtils.getString(updateMerchant, Merchant.CITY));
//            mcStore.put(Store.DISTRICT, MapUtils.getString(updateMerchant, Merchant.DISTRICT));
//            mcStore.put(Store.DISTRICT_CODE, MapUtils.getString(updateMerchant, Merchant.DISTRICT_CODE));
//            mcStore.put(Store.STREET_ADDRESS, MapUtils.getString(updateMerchant, Merchant.STREET_ADDRESS));
//            mcStore.put(Store.LATITUDE, MapUtils.getString(updateMerchant, Merchant.LATITUDE));
//            mcStore.put(Store.LONGITUDE, MapUtils.getString(updateMerchant, Merchant.LONGITUDE));
//            mcStore.put(Store.STREET_ADDRESS_DESC, MapUtils.getString(updateMerchant, Merchant.STREET_ADDRESS_DESC));
//
//            mcPreBiz.recordMcPre(TableNameEnum.STORE.getTableName(), bindStoreId, "mcMerchant_To_Store", mcStore);
//        } else {
//            //商户地址同步至指定门店
//            Map store = storeService.getStoreByStoreId(bindStoreId);
//            Map updateStore = new HashMap();
//            updateStore.put(DaoConstants.ID, bindStoreId);
//            updateStore.put(Store.PROVINCE, MapUtils.getString(updateMerchant, Merchant.PROVINCE));
//            updateStore.put(Store.CITY, MapUtils.getString(updateMerchant, Merchant.CITY));
//            updateStore.put(Store.DISTRICT, MapUtils.getString(updateMerchant, Merchant.DISTRICT));
//            updateStore.put(Store.DISTRICT_CODE, MapUtils.getString(updateMerchant, Merchant.DISTRICT_CODE));
//            updateStore.put(Store.STREET_ADDRESS, MapUtils.getString(updateMerchant, Merchant.STREET_ADDRESS));
//            updateStore.put(Store.LATITUDE, MapUtils.getString(updateMerchant, Merchant.LATITUDE));
//            updateStore.put(Store.LONGITUDE, MapUtils.getString(updateMerchant, Merchant.LONGITUDE));
//            updateStore.put(Store.STREET_ADDRESS_DESC, MapUtils.getString(updateMerchant, Merchant.STREET_ADDRESS_DESC));
//            storeService.updateStore(updateStore);
//        }


    }

    /**
     * 拦截校验,某个key有进行中的申请单,则无法更新
     *
     * @param updateMerchant 本次提交的新数据
     */
    private void checkProcessMerchantCrmApply(String merchantId, Map<String, Object> updateMerchant) {
        ListResult infos = commonFieldService.findFieldAppInfos(
                new PageInfo(1, 15),
                CollectionUtil.hashMap("merchant_id", merchantId,
                        "statuses", Arrays.asList(CrmApplyConstant.STATUS_INIT, CrmApplyConstant.STATUS_PENDING, CrmApplyConstant.STATUS_FAIL),
                        "un_contain_field_types", Arrays.asList("industry", "address", "business_license"),
                        //商户场景传0 门店场景传1
                        "field_main_type", 0)
        );
        if (WosaiCollectionUtils.isNotEmpty(infos.getRecords())) {
            List<Map> records = infos.getRecords();
            for (Map record : records) {

                Map info = (Map) record.get(CrmApplyConstant.SUBMIT_INFO);
                List<Map<String, Object>> infoList = (List) info.get(CrmApplyConstant.FIELD_LIST);
                for (Map<String, Object> processingMap : infoList) {
                    for (Map.Entry<String, Object> entry : updateMerchant.entrySet()) {
                        if (processingMap.containsKey(entry.getKey()) && !Objects.equals(entry.getValue(), processingMap.get(entry.getKey()))) {
                            throw new CommonPubBizException("有正在进行的变更申请单,无法编辑");
                        }
                    }
                }

            }

        }
    }

    public void closeMerchantWithLog(String merchantId, LogParamsDto dto) {
        merchantService.closeMerchant(merchantId);
        if (!Objects.equals(dto.getSceneTemplateCode(), LogParamsConstant.LOG_PARAMS_TEMPLATE_CODE_NO_LOG)) {
            logBiz.handleMerchantStatusBusinessLog(merchantId, dto, "未关闭", "关闭");
        }
    }

    public void disableMerchantWithLog(String merchantId, LogParamsDto dto) {
        merchantService.disableMerchant(merchantId);
        if (!Objects.equals(dto.getSceneTemplateCode(), LogParamsConstant.LOG_PARAMS_TEMPLATE_CODE_NO_LOG)) {
            logBiz.handleMerchantStatusBusinessLog(merchantId, dto, "启用", "禁用");
        }
    }

    public void enableMerchantWithLog(String merchantId, LogParamsDto dto) {
        merchantService.enableMerchant(merchantId);
        if (!Objects.equals(dto.getSceneTemplateCode(), LogParamsConstant.LOG_PARAMS_TEMPLATE_CODE_NO_LOG)) {
            logBiz.handleMerchantStatusBusinessLog(merchantId, dto, "禁用", "启用");
        }
    }

    /**
     * 判断是否是小微商户
     * @param merchantId  商户id
     */
    public boolean isMicro(String merchantId) {
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(merchantId, null, false);
        if (Objects.isNull(merchantBusinessLicenseInfo) || Objects.isNull(merchantBusinessLicenseInfo.getType()) || BusinessLicenseTypeEnum.isMicro(merchantBusinessLicenseInfo.getType())) {
            return true;
        }
        return false;
    }
}
