package com.wosai.mc.biz;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.mc.entity.FieldConfigExample;
import com.wosai.mc.mapper.FieldConfigMapper;
import com.wosai.mc.model.FieldConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class FieldConfigBiz {

    @Autowired
    private FieldConfigMapper fieldConfigMapper;

    public List<FieldConfig> queryByTables(List<String> tables) {
        if (WosaiCollectionUtils.isEmpty(tables)) {
            return new ArrayList<>();
        }

        FieldConfigExample configExample = new FieldConfigExample();
        configExample.or().andTable_nameIn(tables);
        return fieldConfigMapper.selectByExample(configExample);
    }


}
