package com.wosai.mc.biz;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.req.LogReq;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.mc.utils.TimeUtil;
import com.wosai.sp.business.logstash.dto.ValidList;
import com.wosai.sp.business.logstash.dto.req.BsOpLogCreateReqDto;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.upay.core.model.Store;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
@Slf4j
public class LogBiz {
    private static final List<String> IGNORE_FIELD = Arrays.asList("id", "ctime", "mtime", "extra", "version", "vendor_id", "solicitor_id", "client_sn");
    @Autowired
    private BusinessOpLogService businessOpLogService;

    private static ExecutorService pool = Executors.newSingleThreadExecutor();

    public ValidList<BsOpLogCreateReqDto.Diff> checkDifferent(Map<String, Object> origin, Map<String, Object> update, String tableName) {
        for (String key : IGNORE_FIELD) {
            origin.remove(key);
            update.remove(key);
        }
        ValidList<BsOpLogCreateReqDto.Diff> differList = new ValidList<>();


        for (Map.Entry<String, Object> entry1 : update.entrySet()) {
            Object m1value = entry1.getValue() == null ? "" : entry1.getValue();

            Object m2value = origin.get(entry1.getKey()) == null ? "" : origin.get(entry1.getKey());

            //若两个map中相同key对应的value不相等
            if (!m1value.equals(m2value)) {
                if (m1value instanceof Date && m2value instanceof Date) {
                    m1value = TimeUtil.formatDate((Date) m1value);
                    m2value = TimeUtil.formatDate((Date) m2value);

                    //属性值为Long类型,且属性key没有 id 字样 的,会被转为日期
                } else if (m1value instanceof Long && m2value instanceof Long && !entry1.getKey().contains("id")) {
                    m1value = TimeUtil.formatDate(new Date((long) m1value));
                    m2value = TimeUtil.formatDate(new Date((long) m2value));

                }

                BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff(tableName + "#" + entry1.getKey(), PhotoUtils.baseUrl(m2value.toString()), PhotoUtils.baseUrl(m1value.toString()), null);
                differList.add(diff);
            }
        }

        return differList;
    }

    public void saveLog(Map<String, Object> origin, Map<String, Object> update, String tableName, String opObjectId, String rootObjectId, LogReq logReq) {
        BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                .logTemplateCode(logReq.getLogTemplateCode()).opObjectId(opObjectId)
                .opUserId(logReq.getOpUserId()).opUserName(logReq.getOpUserName()).remark(logReq.getRemark())
                .platformCode(logReq.getPlatformCode())
                .build();
        saveBusinessLog(origin, update, tableName, rootObjectId, reqDto);
    }

    public void saveLogWithLogParamsDto(Map<String, Object> origin, Map<String, Object> update, String tableName, String opObjectId, String rootObjectId, LogParamsDto dto) {
        BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                .logTemplateCode(dto.getSceneTemplateCode())
                .remark(dto.getRemark())
                .opObjectId(opObjectId)
                .opUserId(dto.getUserId())
                .opUserName(dto.getUserName())
                .platformCode(dto.getLogPlatformEnum().getCode())
                .build();
        saveBusinessLog(origin, update, tableName, rootObjectId, reqDto);
    }

    private void saveBusinessLog(Map<String, Object> origin, Map<String, Object> update, String tableName, String rootObjectId, BsOpLogCreateReqDto reqDto) {
        try {
            ValidList<BsOpLogCreateReqDto.Diff> diffs = checkDifferent(origin, update, tableName);
            //没有改变不记日志
            if (WosaiCollectionUtils.isEmpty(diffs)) return;

            //tableName为门店时,需要特殊处理下,额外记一下门店号
            if (TableNameEnum.STORE.getTableName().equals(tableName)) {
                addStoreSn(origin, diffs, reqDto);
            }


            reqDto.setDiffList(diffs);
            if (WosaiStringUtils.isNotEmpty(rootObjectId)) reqDto.setRootObjectId(rootObjectId);
            //异步调接口
            pool.submit(() -> {
                try {
                    log.info("调用businessOpLogService.createBusinessLogForAsync接口入参：{}", JSONObject.toJSONString(reqDto));
                    businessOpLogService.createBusinessLogForAsync(reqDto);
                } catch (Exception e) {
                    log.error("调用businessOpLogService.createBusinessLogForAsync接口异常", e);
                }
            });
        } catch (Exception e) {
            log.error("记录日志异常,table {}, update {} ", tableName, update, e);
        }
    }


    public void handleMerchantStatusBusinessLog(String merchantId, LogParamsDto dto, String beforeStatus, String afterStatus) {
        Map<String, Object> origin = CollectionUtil.hashMap("status", beforeStatus);
        Map<String, Object> update = CollectionUtil.hashMap("status", afterStatus);
        String tableName = TableNameEnum.MERCHANT.getTableName();
        LogReq logReq = new LogReq();
        logReq.setLogTemplateCode(dto.getSceneTemplateCode());
        logReq.setOpUserId(dto.getUserId());
        logReq.setOpUserName(dto.getUserName());
        logReq.setRemark(dto.getRemark());
        logReq.setPlatformCode(dto.getLogPlatformEnum().getCode());
        saveLog(origin, update, tableName, merchantId, null, logReq);
    }

    private void addStoreSn(Map<String, Object> origin, ValidList<BsOpLogCreateReqDto.Diff> diffs, BsOpLogCreateReqDto reqDto) {
        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff("store#sn", MapUtils.getString(origin, Store.SN), MapUtils.getString(origin, Store.SN), null);
        diffs.add(diff);
        reqDto.setSameSaveColumnCode("store#sn");
    }
}
