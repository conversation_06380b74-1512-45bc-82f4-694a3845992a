package com.wosai.mc.biz;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.utils.LogUtil;
import com.wosai.sales.bean.SubmitPoiRecord;
import com.wosai.sales.model.gaoDe.GetPoiDetailByAddressReq;
import com.wosai.sales.model.gaoDe.GetPoiDetailByAddressResp;
import com.wosai.sales.model.gaoDe.GetPoiDetailByPoiReq;
import com.wosai.sales.model.gaoDe.PoiDetail;
import com.wosai.sales.service.SubmitPoiRecordService;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
@Component
@Slf4j
public class FillDistrictBiz {

    @Autowired
    private GaoDeService gaoDeService;

    @Autowired
    private StoreService cbStoreService;

    @Autowired
    private MerchantService cbMerchantService;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private StoreBiz storeBiz;

    @Autowired
    private SubmitPoiRecordService submitPoiRecordService;

    private static final List<String> specialCode = Arrays.asList("441900", "442000", "460300", "460400", "620200");

    public void fillStoreDistrict(String storeSn) {
        Map store = cbStoreService.getStoreByStoreSn(storeSn);
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);

        String districtCode = BeanUtil.getPropString(store, Store.DISTRICT_CODE);
        if (isValid(districtCode)) {
            return;
        }

        // 检查经纬度
        String longitude = BeanUtil.getPropString(store, Store.LONGITUDE);
        String latitude = BeanUtil.getPropString(store, Store.LATITUDE);
        if (!WosaiStringUtils.isEmptyAny(longitude, latitude) && !Objects.equals("0.000000", longitude) && !Objects.equals("0.000000", latitude)) {
            GetPoiDetailByPoiReq poiReq = new GetPoiDetailByPoiReq(longitude, latitude);
            poiReq.setBusiness("支付业务");
            poiReq.setScene("进件");
            PoiDetail poiDetail = gaoDeService.getPoiDetailByPoi(poiReq);
            doFillStoreDistrict(storeId, storeSn, poiDetail.getDistrict_code(), null);
            return;
        }

        // 检查详细地址
        String address = BeanUtil.getPropString(store, Store.STREET_ADDRESS);
        if (WosaiStringUtils.isEmpty(address) || address.contains("中江路879弄")) {
            LogUtil.info(log, "门店详细地址无效", "fillStoreDistrict", storeSn, "address: " + address);
            return;
        }
        String province = BeanUtil.getPropString(store, Store.PROVINCE);
        String city = BeanUtil.getPropString(store, Store.CITY);
        String district = BeanUtil.getPropString(store, Store.DISTRICT);
        String completeAddress = String.format("%s%s%s%s", province, city, district, address);

        GetPoiDetailByAddressReq addressReq = new GetPoiDetailByAddressReq(completeAddress);
        addressReq.setBusiness("支付业务");
        addressReq.setScene("进件");

        GetPoiDetailByAddressResp resp = gaoDeService.getPoiDetailByCompleteAddress(addressReq);
        if (Objects.isNull(resp) || WosaiCollectionUtils.isEmpty(resp.getPoiDetails())) {
            LogUtil.info(log, "根据详细地址查不到poiDetail", "fillStoreDistrict", storeSn, "address: " + completeAddress);
            return;
        }
        List<String> districtCodes = resp.getPoiDetails().stream()
                .map(PoiDetail::getDistrict_code)
                .filter(code -> WosaiStringUtils.isNotEmpty(code))
                .distinct()
                .collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(districtCodes)) {
            LogUtil.info(log, "根据详细地址查不到poiDetail", "fillStoreDistrict", storeSn, "address: " + completeAddress);
            return;
        }
        if (districtCodes.size() > 1) {
            LogUtil.info(log, "根据详细地址查到多个poiDetail", "fillStoreDistrict", storeSn, "address: " + completeAddress, districtCodes);
            return;
        }

        Optional<PoiDetail> first = resp.getPoiDetails().stream()
                .filter(poiDetail -> Objects.equals(districtCodes.get(0), poiDetail.getDistrict_code()))
                .findFirst();
        doFillStoreDistrict(storeId, storeSn, districtCodes.get(0), first.orElse(null));
    }

    private void doFillStoreDistrict(String storeId, String storeSn, String districtCode, PoiDetail poiDetail) {
        District district = districtsServiceV2.getDistrict(CollectionUtil.hashMap(
                "code", districtCode
        ));
        if (Objects.isNull(district) || Objects.equals(0, district.getStatus())) {
            LogUtil.info(log, "无效的districtCode", "doFillStoreDistrict", storeSn, districtCode);
            return;
        }
        Map updateValue = CollectionUtil.hashMap(
                DaoConstants.ID, storeId,
                Store.PROVINCE, district.getProvince_name(),
                Store.CITY, district.getCity_name(),
                Store.DISTRICT, district.getName(),
                Store.DISTRICT_CODE, district.getCode()
        );
        if (Objects.nonNull(poiDetail)) {
            updateValue.put(Store.LONGITUDE, poiDetail.getLongitude());
            updateValue.put(Store.LATITUDE, poiDetail.getLatitude());
        }

        cbStoreService.updateStore(updateValue);

        if (Objects.nonNull(poiDetail)) {
            // 同步poi中心
            SubmitPoiRecord poiRecord = new SubmitPoiRecord();
            poiRecord.setDevCode("OEBIBCAY2K1H");
            poiRecord.setDevScenario("no_examine_poi");
            poiRecord.setStoreId(storeId);
            poiRecord.setLongitude(poiDetail.getLongitude());
            poiRecord.setLatitude(poiDetail.getLatitude());
            submitPoiRecordService.insertSubmitPoiRecord(poiRecord);
        }
    }

    public void fillMerchantDistrict(String merchantSn) {
        Map merchant = cbMerchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (isValid(BeanUtil.getPropString(merchant, Merchant.DISTRICT_CODE))) {
            return;
        }
        Map firstStore = storeBiz.getFirstStoreByMerchantId(merchantId);
        if (!isValid(BeanUtil.getPropString(firstStore, Store.DISTRICT_CODE))) {
            LogUtil.info(log, "第一家门店省市区错误", "fillMerchantDistrict", merchantSn);
            return;
        }
        cbMerchantService.updateMerchant(CollectionUtil.hashMap(
                DaoConstants.ID, merchantId,
                Store.PROVINCE, BeanUtil.getPropString(firstStore, Store.PROVINCE),
                Store.CITY, BeanUtil.getPropString(firstStore, Store.CITY),
                Store.DISTRICT, BeanUtil.getPropString(firstStore, Store.DISTRICT),
                Store.DISTRICT_CODE, BeanUtil.getPropString(firstStore, Store.DISTRICT_CODE)
        ));

    }


    /**
     * 检查是否是区县code
     *
     * @param districtCode
     * @return
     */
    private boolean isValid(String districtCode) {
        if (WosaiStringUtils.isBlank(districtCode)) {
            return false;
        }
        if (specialCode.contains(districtCode)) {
            return true;
        }

        return !Objects.equals("00", districtCode.substring(4));
    }


}
