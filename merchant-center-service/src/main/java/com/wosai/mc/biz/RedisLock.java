package com.wosai.mc.biz;

import com.yammer.metrics.stats.EWMA;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RedisLock {

    final StringRedisTemplate redisTemplate;

    private final static DefaultRedisScript<Long> LOCK_LUA_SCRIPT = new DefaultRedisScript<>(
            "if redis.call('setnx',KEYS[1],ARGV[1]) == 1 then return redis.call('expire',KEYS[1],ARGV[2])  else return 0 end"
            , Long.class
    );

    private final static DefaultRedisScript<Long> UNLOCK_LUA_SCRIPT = new DefaultRedisScript<>(
            "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return 0 end"
            , Long.class
    );

    /**
     * 加锁
     *
     * @param key
     * @param value
     * @param timeInSeconds
     * @return
     */
    public boolean lock(final String key, final String value, long timeInSeconds) {
        Long result = redisTemplate.execute(LOCK_LUA_SCRIPT, Collections.singletonList(key), value, String.valueOf(timeInSeconds));
        return result == 1;
    }

    /**
     * 是否已经有key
     */
    public boolean hasKey(final String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }


    /**
     * 解锁
     *
     * @param key
     * @param value
     */
    public boolean unlock(String key, String value) {
        Long result = redisTemplate.execute(UNLOCK_LUA_SCRIPT, Collections.singletonList(key), value);
        return result != null && result == 1;
    }
}