package com.wosai.mc.biz;

import com.shouqianba.cua.enums.core.LicenseSettlementAccountTypeEnum;
import com.wosai.mc.biz.processor.PhotoProcessor;
import com.wosai.mc.model.CommonResultResp;
import com.wosai.mc.model.LicenseSettlementAccountExtraParams;
import com.wosai.mc.model.dto.BankAccountDTO;
import com.wosai.mc.model.dto.BusinessLicenseDTO;
import com.wosai.mc.model.dto.req.BusinessLicenseAuditApplyReqDTO;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * 银行账户比对
 *
 * <AUTHOR>
 * @date 2025/3/17 15:45
 */
@Component
@Slf4j
public class BankAccountCompareBiz {

    @Autowired
    private BankBusinessLicenseService bankBusinessLicenseService;

    /**
     * 比对银行账户信息是否变更
     *
     * @param applyReqDTO  业务提交的信息
     * @return true-变更了  false-未变更
     */
    public boolean checkBankAccountIsChange(BusinessLicenseAuditApplyReqDTO applyReqDTO) {
        if (Objects.isNull(applyReqDTO.getBankAccount())) {
            return false;
        }
        BankAccountDTO bankAccount = applyReqDTO.getBankAccount();
        BusinessLicenseDTO businessLicense = applyReqDTO.getBusinessLicense();
        String merchantId = applyReqDTO.getMerchantId();
        // 获取当前的结算信息，和提交的结算信息做比对
        MerchantBankAccount existedMerchantBank = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(merchantId);
        if (Objects.isNull(existedMerchantBank)) {
            log.info("checkBankAccountIsChange existedMerchantBank is null, merchantId:{}", merchantId);
            return true;
        }
        populateBankAccount(bankAccount, businessLicense);
        PhotoProcessor.removeSign(bankAccount);
        // 比较共同字段
        CommonResultResp commonFieldResultResp = commonFieldExistDifferent(bankAccount, existedMerchantBank);
        if (commonFieldResultResp.getResult()) {
            log.info("checkBankAccountIsChange return true, commonFieldExistDifferent, merchantId:{}, different:{}", merchantId, commonFieldResultResp.getMessage());
            return true;
        }
        // 法人对私:证件类型，证件名字 证件号 证件正反面照片 证件有效期 证件地址 证件签发机关  对公账户证明材料 其他照片
        if (Objects.equals(bankAccount.getSettlementAccountType(), LicenseSettlementAccountTypeEnum.LEGAL_PERSONAL.getValue())) {
            CommonResultResp legalPersonDiffResult = legalPersonFieldExistDifferent(bankAccount, existedMerchantBank);
            if (legalPersonDiffResult.getResult()) {
                log.info("checkBankAccountIsChange return true, legalPersonFieldExistDifferent, merchantId:{}, different:{}", merchantId, legalPersonDiffResult.getMessage());
                return true;
            }
        }
        // 其他个人:证件类型，证件名字 证件号 证件正反面照片 证件有效期 证件地址 证件签发机关  授权函 对公账户证明材料 法人手持授权函 其他照片
        if (Objects.equals(bankAccount.getSettlementAccountType(), LicenseSettlementAccountTypeEnum.OTHER_PERSONAL.getValue())) {
            CommonResultResp otherDiffResult = otherPersonFieldExistDifferent(bankAccount, existedMerchantBank);
            if (otherDiffResult.getResult()) {
                log.info("checkBankAccountIsChange return true, otherPersonFieldExistDifferent, merchantId:{}, different:{}", merchantId, otherDiffResult.getMessage());
                return true;
            }
        }
        // 对公企业:其他照片
        if (Objects.equals(bankAccount.getSettlementAccountType(), LicenseSettlementAccountTypeEnum.CORPORATE.getValue())) {
            CommonResultResp corporateDiffResult = corporateFieldExistDifferent(bankAccount, existedMerchantBank);
            if (corporateDiffResult.getResult()) {
                log.info("checkBankAccountIsChange return true, corporateFieldExistDifferent, merchantId:{}, different:{}", merchantId, corporateDiffResult.getMessage());
                return true;
            }
        }
        // 授权对公:授权函，结算账户营业证照法人证件正面照片，结算账户营业证照法人证件反面照片，账户营业证照法人证件号，其他照片，授权对公账户证明，授权对公关系证明
        if (Objects.equals(bankAccount.getSettlementAccountType(), LicenseSettlementAccountTypeEnum.AUTHORIZED_CORPORATE.getValue())) {
            CommonResultResp commonResultResp = authorizedCorporateFieldExistDifferent(bankAccount, existedMerchantBank);
            if (commonResultResp.getResult()) {
                log.info("checkBankAccountIsChange return true, authorizedCorporateFieldExistDifferent, merchantId:{}, different:{}", merchantId, commonResultResp.getMessage());
                return true;
            }
        }
        return false;
    }

    private CommonResultResp authorizedCorporateFieldExistDifferent(BankAccountDTO bankAccount, MerchantBankAccount existedMerchantBank) {
        // 授权对公:授权函，结算账户营业证照法人证件正面照片，结算账户营业证照法人证件反面照片，账户营业证照法人证件号，其他照片，授权对公账户证明，授权对公关系证明
        String existedLetter = PhotoUtils.baseUrl(existedMerchantBank.getLetter_of_authorization());
        if (!compareCommaSeparatedStrings(bankAccount.getLetterOfAuthorization(), existedLetter)) {
            return CommonResultResp.SUCCESS("授权函变更 new:" + bankAccount.getLetterOfAuthorization() + " existed:" + existedLetter);
        }
        String exitedAccLicLpCertificateFrontPhoto = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.SETT_ACC_LIC_LP_CERTIFICATE_FRONT_PHOTO, existedMerchantBank));
        if (!StringUtils.equals(bankAccount.getSettAccLicLpCertificateFrontPhoto(),exitedAccLicLpCertificateFrontPhoto)) {
            return CommonResultResp.SUCCESS("结算账户营业证照法人证件正面照片变更 new:" + bankAccount.getSettAccLicLpCertificateFrontPhoto() + " existed:" + exitedAccLicLpCertificateFrontPhoto);
        }
        String exitedAccLicLpCertificateBackPhoto = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.SETT_ACC_LIC_LP_CERTIFICATE_BACK_PHOTO, existedMerchantBank));
        if (!StringUtils.equals(bankAccount.getSettAccLicLpCertificateBackPhoto(),exitedAccLicLpCertificateBackPhoto)) {
            return CommonResultResp.SUCCESS("结算账户营业证照法人证件反面照片变更 new:" + bankAccount.getSettAccLicLpCertificateBackPhoto() + " existed:" + exitedAccLicLpCertificateBackPhoto);
        }
        String existedAccLicLpCertificateNum = getValueFromAccountExtra(LicenseSettlementAccountExtraParams.SETT_ACC_LIC_LP_CERTIFICATE_NUM, existedMerchantBank);
        if (!compareCommaSeparatedStrings(bankAccount.getSettAccLicLpCertificateNum(),existedAccLicLpCertificateNum)) {
            return CommonResultResp.SUCCESS("结算账户营业证照法人证件号变更 new:" + bankAccount.getSettAccLicLpCertificateNum() + " existed:" + existedAccLicLpCertificateNum);
        }
        String existedOtherPhotos = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.OTHER_PHOTOS, existedMerchantBank));
        if (!compareCommaSeparatedStrings(bankAccount.getOtherPhotos(),existedOtherPhotos)) {
            return CommonResultResp.SUCCESS("其他照片变更 new:" + bankAccount.getOtherPhotos() + " existed:" + existedOtherPhotos);
        }
        String existedAuthorizeCorpRelProof = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.AUTHORIZE_CORP_REL_PROOF, existedMerchantBank));
        if (!compareCommaSeparatedStrings(bankAccount.getAuthorizeCorpRelProof(),
                PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.AUTHORIZE_CORP_REL_PROOF, existedMerchantBank)))) {
            return CommonResultResp.SUCCESS("授权对公关系证明变更 new:" + bankAccount.getAuthorizeCorpRelProof() + " existed:" + existedAuthorizeCorpRelProof);
        }
        return CommonResultResp.FAIL("授权对公银行卡信息没有变更");
    }

    private CommonResultResp corporateFieldExistDifferent(BankAccountDTO bankAccount, MerchantBankAccount existedMerchantBank) {
        String existedOtherPhotos = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.OTHER_PHOTOS, existedMerchantBank));
        if (!compareCommaSeparatedStrings(bankAccount.getOtherPhotos(), existedOtherPhotos)) {
            return CommonResultResp.SUCCESS("其他照片变更 new:" + bankAccount.getOtherPhotos() + " existed:" + existedOtherPhotos);
        }
        return CommonResultResp.FAIL("对公企业银行卡信息没有变更");
    }

    private CommonResultResp otherPersonFieldExistDifferent(BankAccountDTO bankAccount, MerchantBankAccount existedMerchantBank) {
        // 其他个人:证件类型，证件名字 证件号 证件正反面照片 证件有效期 证件地址 证件签发机关  授权函 对公账户证明材料 法人手持授权函 其他照片
        CommonResultResp commonResultResp = checkCertificateChange(bankAccount, existedMerchantBank);
        if (commonResultResp.getResult()) {
            return commonResultResp;
        }
        if (!StringUtils.equals(bankAccount.getCardValidity(), existedMerchantBank.getCard_validity())) {
            return CommonResultResp.SUCCESS("银行卡有效期变更 new:" + bankAccount.getCardValidity() + " existed:" + existedMerchantBank.getCard_validity());
        }
        String existedLetters = PhotoUtils.baseUrl(existedMerchantBank.getLetter_of_authorization());
        if (!compareCommaSeparatedStrings(bankAccount.getLetterOfAuthorization(), existedLetters)) {
            return CommonResultResp.SUCCESS("授权函变更 new:" + bankAccount.getLetterOfAuthorization() + " existed:" + existedLetters);
        }
        String existedHandLetters = PhotoUtils.baseUrl(existedMerchantBank.getHand_letter_of_authorization());
        if (!compareCommaSeparatedStrings(bankAccount.getHandLetterOfAuthorization(), existedHandLetters)) {
            return CommonResultResp.SUCCESS("法人手持授权函变更 new:" + bankAccount.getHandLetterOfAuthorization() + " existed:" + existedHandLetters);
        }
        String existedOtherPhotos = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.OTHER_PHOTOS, existedMerchantBank));
        if (!compareCommaSeparatedStrings(bankAccount.getOtherPhotos(), existedOtherPhotos)) {
            return CommonResultResp.SUCCESS("其他照片变更 new:" + bankAccount.getOtherPhotos() + " existed:" + existedOtherPhotos);
        }
        return CommonResultResp.FAIL("其他个人银行卡信息没有变更");
    }

    // 法人对私:证件类型，证件名字 证件号 证件正反面照片 证件有效期 证件地址 证件签发机关  其他照片
    private CommonResultResp legalPersonFieldExistDifferent(BankAccountDTO bankAccount, MerchantBankAccount existedMerchantBank) {
        CommonResultResp certificateChange = checkCertificateChange(bankAccount, existedMerchantBank);
        if (certificateChange.getResult()) {
            return CommonResultResp.SUCCESS(certificateChange.getMessage());
        }
        if (!StringUtils.equals(bankAccount.getCardValidity(), existedMerchantBank.getCard_validity())) {
            return CommonResultResp.SUCCESS("银行卡有效期变更 new:" + bankAccount.getCardValidity() + " existed:" + existedMerchantBank.getCard_validity());
        }
        String otherPhotos = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.OTHER_PHOTOS, existedMerchantBank));
        if (!compareCommaSeparatedStrings(bankAccount.getOtherPhotos(), otherPhotos)) {
            return CommonResultResp.SUCCESS("其他照片变更 new:" + bankAccount.getOtherPhotos() + " existed:" + otherPhotos);
        }
        return CommonResultResp.FAIL("法人对私银行卡信息没有变更");
    }

    private CommonResultResp checkCertificateChange(BankAccountDTO bankAccount, MerchantBankAccount existedMerchantBank) {
        if (!Objects.equals(bankAccount.getCertificateType(), existedMerchantBank.getId_type())) {
            return CommonResultResp.SUCCESS("证件类型变更 new:" + bankAccount.getCertificateType() + " existed:" + existedMerchantBank.getId_type());
        }
        String existedCertificateName = getCertificateNameFromExistedAccount(existedMerchantBank);
        if (!StringUtils.equals(bankAccount.getCertificateName(), existedCertificateName)) {
            return CommonResultResp.SUCCESS("证件名称变更 new:" + bankAccount.getCertificateName() + " existed:" + existedCertificateName);
        }
        if (!StringUtils.equals(bankAccount.getCertificateNumber(), existedMerchantBank.getIdentity())) {
            return CommonResultResp.SUCCESS("证件号码变更 new:" + bankAccount.getCertificateNumber() + " existed:" + existedMerchantBank.getIdentity());
        }
        String existedCertificateFrontPhoto = PhotoUtils.baseUrl(existedMerchantBank.getHolder_id_front_photo());
        if (!compareCommaSeparatedStrings(bankAccount.getCertificateFrontPhoto(), existedCertificateFrontPhoto)) {
            return CommonResultResp.SUCCESS("证件正面照片变更 new:" + bankAccount.getCertificateFrontPhoto() + " existed:" + existedCertificateFrontPhoto);
        }
        String existedCertificateBackPhoto = PhotoUtils.baseUrl(existedMerchantBank.getHolder_id_back_photo());
        if (!compareCommaSeparatedStrings(bankAccount.getCertificateBackPhoto(), existedCertificateBackPhoto)) {
            return CommonResultResp.SUCCESS("证件反面照片变更 new:" + bankAccount.getCertificateBackPhoto() + " existed:" + existedCertificateBackPhoto);
        }
        if (!compareCommaSeparatedStrings(bankAccount.getCertificateValidity(), existedMerchantBank.getId_validity())) {
            return CommonResultResp.SUCCESS("证件有效期变更 new:" + bankAccount.getCertificateValidity() + " existed:" + existedMerchantBank.getId_validity());
        }
        return CommonResultResp.FAIL("证件信息没有变更");
    }

    private String getCertificateNameFromExistedAccount(MerchantBankAccount existedMerchantBank) {
        if (Objects.isNull(existedMerchantBank)) {
            return null;
        }
        Object extra = existedMerchantBank.getExtra();
        if (Objects.isNull(extra)) {
            return existedMerchantBank.getHolder();
        }
        if (extra instanceof Map) {
            return MapUtils.getString((Map) extra, LicenseSettlementAccountExtraParams.CERTIFICATE_NAME, existedMerchantBank.getHolder());
        }
        return existedMerchantBank.getHolder();
    }

    // 银行卡照片 账户名称 银行卡号 开户银行 开户城市 开户支行 银行卡有效期 辅助证明材料
    private CommonResultResp commonFieldExistDifferent(BankAccountDTO bankAccount, MerchantBankAccount existedMerchantBank) {
        if (!StringUtils.equals(bankAccount.getBankCardPhoto(), PhotoUtils.baseUrl(existedMerchantBank.getBank_card_image()))) {
            return CommonResultResp.SUCCESS("银行卡照片变更 new:" + bankAccount.getBankCardPhoto() + " existed:" + existedMerchantBank.getBank_card_image());
        }
        if (!StringUtils.equals(bankAccount.getHolder(), existedMerchantBank.getHolder())) {
            return CommonResultResp.SUCCESS("账户名称变更 new:" + bankAccount.getHolder() + " existed:" + existedMerchantBank.getHolder());
        }
        if ( !StringUtils.equals(bankAccount.getAccountNumber(), existedMerchantBank.getNumber())) {
            return CommonResultResp.SUCCESS("银行卡号变更 new:" + bankAccount.getAccountNumber() + " existed:" + existedMerchantBank.getNumber());
        }
        if (!StringUtils.equals(bankAccount.getOpeningBank(), existedMerchantBank.getBank_name())) {
            return CommonResultResp.SUCCESS("开户银行变更 new:" + bankAccount.getOpeningBank() + " existed:" + existedMerchantBank.getBank_name());
        }
        if (!StringUtils.equals(bankAccount.getOpeningCity(), existedMerchantBank.getCity())) {
            return CommonResultResp.SUCCESS("开户城市变更 new:" + bankAccount.getOpeningCity() + " existed:" + existedMerchantBank.getCity());
        }
        if (!StringUtils.equals(bankAccount.getOpeningBranch(), existedMerchantBank.getBranch_name())) {
            return CommonResultResp.SUCCESS("开户支行变更 new:" + bankAccount.getOpeningBranch() + " existed:" + existedMerchantBank.getBranch_name());
        }
        String existedAuxiliary = PhotoUtils.baseUrl(getValueFromAccountExtra(LicenseSettlementAccountExtraParams.AUXILIARY_PROOF_MATERIALS, existedMerchantBank));
        if (!compareCommaSeparatedStrings(bankAccount.getAuxiliaryProofMaterials(), existedAuxiliary)) {
            return CommonResultResp.SUCCESS("辅助证明材料变更 new:" + bankAccount.getAuxiliaryProofMaterials() + " existed:" + existedAuxiliary);
        }
        return CommonResultResp.FAIL("银行卡信息公共字段没有变更");
    }


    private String getValueFromAccountExtra(String key, MerchantBankAccount existedMerchantBank) {
        if (Objects.isNull(existedMerchantBank)) {
            return null;
        }
        Object extra = existedMerchantBank.getExtra();
        if (Objects.isNull(extra)) {
            return null;
        }
        if (extra instanceof Map) {
            return MapUtils.getString((Map) extra, key, null);
        }
        return null;
    }


    private boolean compareCommaSeparatedStrings(String str1, String str2) {
        if (str1 == null && str2 == null) {
            return true;
        }
        if (str1 == null || str2 == null) {
            return false;
        }
        String[] arr1 = str1.split(",");
        String[] arr2 = str2.split(",");
        if (arr1.length == 0 && arr2.length == 0) {
            return true;
        }
        if (arr1.length == 0 || arr2.length == 0) {
            return false;
        }
        Arrays.sort(arr1);
        Arrays.sort(arr2);
        return Arrays.equals(arr1, arr2);
    }

    private void populateBankAccount(BankAccountDTO bankAccount, BusinessLicenseDTO businessLicense) {
        if (Objects.equals(bankAccount.getSettlementAccountType(), LicenseSettlementAccountTypeEnum.LEGAL_PERSONAL.getValue())) {
            bankAccount.setCertificateType(businessLicense.getLegalPersonCertificateType());
            bankAccount.setCertificateNumber(businessLicense.getLegalPersonCertificateNumber());
            bankAccount.setCertificateName(businessLicense.getLegalPersonCertificateName());
            bankAccount.setCertificateFrontPhoto(businessLicense.getLegalPersonCertificateFrontPhoto());
            bankAccount.setCertificateBackPhoto(businessLicense.getLegalPersonCertificateBackPhoto());
            bankAccount.setCertificateValidity(businessLicense.getLegalPersonCertificateValidity());
            bankAccount.setCertificateAddress(businessLicense.getLegalPersonCertificateAddress());
            bankAccount.setCertificateIssuingAuthority(businessLicense.getLegalPersonCertificateIssuingAuthority());
        }
    }
}
