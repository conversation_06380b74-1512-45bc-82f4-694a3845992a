package com.wosai.mc.dao;

import com.wosai.mc.entity.BusinessLicenseEditDraft;
import com.wosai.mc.entity.BusinessLicenseEditDraftExample;
import com.wosai.mc.mapper.BusinessLicenseEditDraftMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 营业执照编辑草稿dao
 *
 * <AUTHOR>
 * @date 2025/1/21 14:19
 */
@Repository
public class BusinessLicenseEditDraftDAO {

    @Resource
    private BusinessLicenseEditDraftMapper businessLicenseEditDraftMapper;

    public BusinessLicenseEditDraft getByPrimaryKey(Long id) {
        return businessLicenseEditDraftMapper.selectByPrimaryKey(id);
    }

    public BusinessLicenseEditDraft getByMerchantSn(String merchantSn) {
        BusinessLicenseEditDraftExample example = new BusinessLicenseEditDraftExample();
        example.createCriteria().andMerchantSnEqualTo(merchantSn);
        List<BusinessLicenseEditDraft> businessLicenseEditDrafts = businessLicenseEditDraftMapper.selectByExampleWithBLOBs(example);
        return CollectionUtils.isEmpty(businessLicenseEditDrafts) ? null : businessLicenseEditDrafts.get(0);
    }

    public int insertSelective(BusinessLicenseEditDraft record) {
        return businessLicenseEditDraftMapper.insertSelective(record);
    }

    public int updateByPrimaryKey(BusinessLicenseEditDraft record) {
        return businessLicenseEditDraftMapper.updateByPrimaryKey(record);
    }

    public int updateByPrimaryKeySelective(BusinessLicenseEditDraft record) {
        return businessLicenseEditDraftMapper.updateByPrimaryKeySelective(record);
    }


    public void deleteByMerchantSn(String merchantSn) {
        BusinessLicenseEditDraftExample example = new BusinessLicenseEditDraftExample();
        example.createCriteria().andMerchantSnEqualTo(merchantSn);
        businessLicenseEditDraftMapper.deleteByExample(example);
    }
}
