package com.wosai.mc.service.app;

import com.github.pagehelper.PageHelper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.aop.annotation.RequireMask;
import com.wosai.mc.aop.annotation.RequireRoles;
import com.wosai.mc.biz.MergeStoreBiz;
import com.wosai.mc.biz.StoreExtBiz;
import com.wosai.mc.constants.CrmApplyConstant;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.constants.MergeStoreConstant;
import com.wosai.mc.entity.StoreListWaitHandle;
import com.wosai.mc.mapper.StoreListWaitHandleMapper;
import com.wosai.mc.model.McListResult;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.app.ManagePassTokenReq;
import com.wosai.mc.model.app.req.ChangeDataReq;
import com.wosai.mc.model.app.req.StoreBasicInfoQueryReq;
import com.wosai.mc.model.app.req.StoreIdQueryReq;
import com.wosai.mc.model.app.req.StoreListQueryReq;
import com.wosai.mc.model.app.resp.MergeStoreDataResp;
import com.wosai.mc.model.app.resp.StoreBasicInfoQueryResp;
import com.wosai.mc.model.app.resp.StoreListQueryResp;
import com.wosai.mc.model.app.resp.StorePhotosQueryResp;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.sales.merchant.business.bean.MerchantAppOpenResult;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.StoreService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wosai.mc.model.crmPlatform.CrmApply;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Service
@AutoJsonRpcServiceImpl
public class AppStoreServiceImpl implements AppStoreService {

    @Autowired
    private StoreService storeService;
    @Autowired
    private CommonAppInfoService commonAppInfoService;
    @Autowired
    protected CommonFieldService commonFieldService;
    @Autowired
    private StoreExtBiz storeExtBiz;
    @Value("${businessopen.appid.takeaway}")
    private String takeawayAppId;
    @Value("${businessopen.appid.order}")
    private String orderAppId;
    @Autowired
    private IndustryV2Service industryV2Service;
    @Value("${dbb_app_id}")
    private String dbbAppId;
    @Autowired
    private StoreListWaitHandleMapper waitHandleMapper;
    @Autowired
    private MergeStoreBiz mergeStoreBiz;

    @Override
    @RequireRoles(MerchantConstant.SUPER_ADMIN)
    public McListResult<StoreListQueryResp> queryStoreList(StoreListQueryReq storeListQueryReq) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(storeListQueryReq.getPaging().getCurrentPage());
        pageInfo.setPageSize(storeListQueryReq.getPaging().getPageSize());
        pageInfo.setOrderBy(Collections.singletonList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));
        Map queryReq = CollectionUtil.hashMap(Store.MERCHANT_ID, storeListQueryReq.getMerchantId(), "store_name", storeListQueryReq.getStoreName(), Store.STATUS, Store.STATUS_ENABLED, DaoConstants.DELETED, false);
        // 如果不是超管，就要去查管理的门店的信息
        ListResult listResult = storeService.findStores(pageInfo, queryReq);
        if (Objects.isNull(listResult) || WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            return new McListResult<>();
        }
        return new McListResult<>(
                listResult.getTotal(),
                listResult.getRecords().stream().map(r -> new StoreListQueryResp()
                                .setId(WosaiMapUtils.getString(r, DaoConstants.ID))
                                .setSn(WosaiMapUtils.getString(r, Store.SN))
                                .setName(WosaiMapUtils.getString(r, Store.NAME))
                                .setProvince(WosaiMapUtils.getString(r, Store.PROVINCE))
                                .setCity(WosaiMapUtils.getString(r, Store.CITY))
                                .setDistrict(WosaiMapUtils.getString(r, Store.DISTRICT))
                                .setLogo(WosaiMapUtils.getString(r, Store.LOGO))
                                .setCrm_status(queryCrmApply(WosaiMapUtils.getString(r, DaoConstants.ID))))
                        .collect(Collectors.toList())
        );
    }

    /**
     * @return 获取每个门店的申请单列表, 申请单单号，状态，字段信息
     */
    private List<CrmApply> queryCrmApply(String storeId) {
        Map store = storeService.getStoreByStoreId(storeId);
        //该接口商户id必传,查门店时多传门店id
        ListResult infos = commonFieldService.findFieldAppInfos(
                new PageInfo(1, 20),
                CollectionUtil.hashMap("store_id", storeId, "merchant_id", MapUtils.getString(store, Store.MERCHANT_ID),
                        "statuses", Arrays.asList(CrmApplyConstant.STATUS_INIT, CrmApplyConstant.STATUS_PENDING, CrmApplyConstant.STATUS_FAIL),
                        //含义:只查门店申请单
                        "field_main_type", 1,
                        "un_contain_field_types", Arrays.asList("industry", "address", "business_license")
                )
        );
        if (WosaiCollectionUtils.isNotEmpty(infos.getRecords())) {
            List<Map> records = infos.getRecords();
            List<CrmApply> applyList = records.stream().map(r -> new CrmApply()
                    .setField_app_info_id(MapUtils.getInteger(r, DaoConstants.ID))
                    .setField_app_status(MapUtils.getInteger(r, CrmApplyConstant.STATUS))
                    .setSubmit_info((List) (((Map) r.get(CrmApplyConstant.SUBMIT_INFO)).get(CrmApplyConstant.FIELD_LIST)))).collect(Collectors.toList());
            return applyList;
        }
        return new ArrayList<>();
    }

    @Override
    @RequireRoles(MerchantConstant.SUPER_ADMIN)
    @RequireMask
    public StoreBasicInfoQueryResp queryStoreBasicInfo(StoreBasicInfoQueryReq storeBasicInfoQueryReq) {
        StoreBasicInfoQueryResp resp = queryStoreBasicInfoInner(storeBasicInfoQueryReq.getQueryStoreId());
        return resp;

    }

    private StoreBasicInfoQueryResp queryStoreBasicInfoInner(String storeId) {
        Map storeInfo = storeService.getStoreByStoreId(storeId);
        StotreExtInfoAndPictures pictures = storeExtBiz.findStoreExtAndPicturesByStoreId(storeId, null);
        Map<String, Object> originalMap = CommonUtils.beanToTargetObj(pictures, Map.class);
        Map<String, Object> snakeCaseMap = new HashMap<>();

        if (WosaiMapUtils.isNotEmpty(originalMap)) {
            for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
                String originalKey = entry.getKey();
                //将首字母小写的驼峰字符串转换为蛇形字符串
                String snakeCaseKey = WordUtils.uncapitalize(originalKey).replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
                snakeCaseMap.put(snakeCaseKey, entry.getValue());
            }
        }

        Map<String, Object> industryData = new HashMap<>();
        if (WosaiStringUtils.isNotEmpty(WosaiMapUtils.getString(storeInfo, Store.INDUSTRY))) {
            Map<String, Object> industry = industryV2Service.getIndustry(WosaiMapUtils.getString(storeInfo, Store.INDUSTRY));
            if (WosaiMapUtils.isNotEmpty(industry)) {
                industryData.putAll(industry);
            }

        }
        //是否只开通电饱饱
        ListResult appInfos = commonAppInfoService.findAppInfos(new PageInfo(1, 10),
                CollectionUtil.hashMap(
//                        "merchant_id", WosaiMapUtils.getString(storeInfo, Store.MERCHANT_ID),
                        "status", "3",
                        "store_id", storeId
                )
        );
        //默认false
        boolean onlyDbb = false;
        //只有1个应用
        if (appInfos != null && appInfos.getTotal() == 1) {
            Map map = appInfos.getRecords().get(0);
            onlyDbb = MapUtils.getString(map, "app_id", "").equals(dbbAppId);
        }

        return new StoreBasicInfoQueryResp()
                .setId(WosaiMapUtils.getString(storeInfo, DaoConstants.ID))
                .setSn(WosaiMapUtils.getString(storeInfo, Store.SN))
                .setName(WosaiMapUtils.getString(storeInfo, Store.NAME))
                .setProvince(WosaiMapUtils.getString(storeInfo, Store.PROVINCE))
                .setCity(WosaiMapUtils.getString(storeInfo, Store.CITY))
                .setDistrict(WosaiMapUtils.getString(storeInfo, Store.DISTRICT))
                .setDistrictCode(WosaiMapUtils.getString(storeInfo, Store.DISTRICT_CODE))
                .setStreetAddress(WosaiMapUtils.getString(storeInfo, Store.STREET_ADDRESS))
                .setStreetAddressDesc(WosaiMapUtils.getString(storeInfo, Store.STREET_ADDRESS_DESC))
                .setContactName(WosaiMapUtils.getString(storeInfo, Store.CONTACT_NAME))
                .setContact_name_value(WosaiMapUtils.getString(storeInfo, Store.CONTACT_NAME))
                .setContactCellphone(WosaiMapUtils.getString(storeInfo, Store.CONTACT_CELLPHONE))
                .setContact_cellphone_value(WosaiMapUtils.getString(storeInfo, Store.CONTACT_CELLPHONE))
                .setLongitude(WosaiMapUtils.getString(storeInfo, Store.LONGITUDE))
                .setLatitude(WosaiMapUtils.getString(storeInfo, Store.LATITUDE))
                .setContactEmail(WosaiMapUtils.getString(storeInfo, Store.CONTACT_EMAIL))
                .setContact_email_value(WosaiMapUtils.getString(storeInfo, Store.CONTACT_EMAIL))
                .setIndustry(WosaiMapUtils.getString(storeInfo, Store.INDUSTRY))
                .setStorePhotosQueryResp(snakeCaseMap)
                .setCrm_status(queryCrmApply(storeId))
                .setIndustry_data(industryData)
                .setExtra(CommonUtils.beanToTargetObj(storeInfo.get(Store.EXTRA), Map.class))
                .setPoiName(WosaiMapUtils.getString(storeInfo, Store.POI_NAME))
                .setPoiSimpleAddress(WosaiMapUtils.getString(storeInfo, Store.POI_SIMPLE_ADDRESS))
                .setExtra(CommonUtils.beanToTargetObj(storeInfo.get(Store.EXTRA), Map.class))
                .setOnlyOpenDbb(onlyDbb);
    }

    @Override
    public StoreBasicInfoQueryResp queryOldestStoreBasicInfo(ManagePassTokenReq managePassTokenReq) {
        String merchantId = managePassTokenReq.getMerchantId();
        return doGetStoreBasicInfoQueryResp(merchantId);
    }

    public StoreBasicInfoQueryResp doGetStoreBasicInfoQueryResp(String merchantId) {
        ListResult oldestStore = storeService.findStores(new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy("ctime", OrderBy.OrderType.ASC))),
                CollectionUtil.hashMap("merchant_id", merchantId));
        Map oldest = oldestStore.getRecords().get(0);

        return queryStoreBasicInfoInner(MapUtils.getString(oldest, DaoConstants.ID));
    }

    @Override
    public StorePhotosQueryResp queryStorePhotos(StoreIdQueryReq storeIdQueryReq) {
        return queryStorePhotosInner(storeIdQueryReq.getQueryStoreId());
    }

    private StorePhotosQueryResp queryStorePhotosInner(String storeId) {
        StotreExtInfoAndPictures storeExtAndPictures = storeExtBiz.findStoreExtAndPicturesByStoreId(storeId, null);
        StorePhotosQueryResp resp = new StorePhotosQueryResp();
        if (Objects.isNull(storeExtAndPictures)) {
            return resp;
        }
        if (Objects.nonNull(storeExtAndPictures.getBrandPhoto())) {
            resp.setBrandPhotoUrl(storeExtAndPictures.getBrandPhoto().getUrl());
        }
        if (Objects.nonNull(storeExtAndPictures.getIndoorMaterialPhoto())) {
            resp.setIndoorMaterialUrl(storeExtAndPictures.getIndoorMaterialPhoto().getUrl());
        }
        if (Objects.nonNull(storeExtAndPictures.getOutdoorMaterialPhoto())) {
            resp.setOutdoorMaterialUrl(storeExtAndPictures.getOutdoorMaterialPhoto().getUrl());
        }
        if (WosaiCollectionUtils.isNotEmpty(storeExtAndPictures.getOtherPhoto())) {
            resp.setOtherPhotos(storeExtAndPictures.getOtherPhoto().stream().map(PhotoInfo::getUrl).collect(Collectors.toList()));
            resp.setOtherPhoto(storeExtAndPictures.getOtherPhoto().stream().map(PhotoInfo::getUrl).collect(Collectors.toList()));
        }
        return resp;
    }

    @Override
    @RequireRoles(MerchantConstant.SUPER_ADMIN)
    public boolean checkStoreOpenedTakeawayOrOrder(StoreIdQueryReq storeIdQueryReq) {
        Map<String, List<MerchantAppOpenResult>> storeAppOpen = commonAppInfoService.getStoreAppOpen(Collections.singletonList(storeIdQueryReq.getQueryStoreId()));
        List<MerchantAppOpenResult> merchantAppOpenResults = storeAppOpen.get(storeIdQueryReq.getQueryStoreId());

        return merchantAppOpenResults.stream().anyMatch(r -> r.getStatus() == 3 && (takeawayAppId.equals(r.getAppId()) || orderAppId.equals(r.getAppId())));
    }

    @Override
    @RequireRoles(MerchantConstant.SUPER_ADMIN)
    public MergeStoreDataResp getNeedMergeStoreList(StoreListQueryReq req) {
        String merchantId = req.getMerchantId();
        PageHelper.startPage(req.getPaging().getCurrentPage(), req.getPaging().getPageSize());
        List<StoreListWaitHandle> storeListWaitHandles = waitHandleMapper.selectByMerchantIdNotMergeAddress(merchantId);
        if (WosaiCollectionUtils.isEmpty(storeListWaitHandles)) {
            return null;
        }
        com.github.pagehelper.PageInfo<StoreListWaitHandle> pageInfo = new com.github.pagehelper.PageInfo<>(storeListWaitHandles);
        long total = pageInfo.getTotal();
        int originSize = storeListWaitHandles.size();
        //再判断下里面有没有已经有申请单的
        storeListWaitHandles = storeListWaitHandles.stream().filter(s -> {
            //门店是否有申请单
            ListResult infos = commonFieldService.findFieldAppInfos(
                    new PageInfo(1, 1),
                    CollectionUtil.hashMap("store_id", s.getStore_id(), "merchant_id", s.getMerchant_id(),
                            //含义:只查门店申请单
                            "field_main_type", 1
                    )
            );
            if (infos.getTotal() > 0) {
                waitHandleMapper.updateByPrimaryKeySelective(new StoreListWaitHandle().setId(s.getId()).setIs_merged_name_address(MergeStoreConstant.NO_NEED_MERGE_HAD_APPLY));
            }
            return infos.getTotal() == 0;
        }).collect(Collectors.toList());

        int afterSize = storeListWaitHandles.size();
        MergeStoreDataResp mergeStoreDataResp = mergeStoreBiz.addData(storeListWaitHandles);
        //这里的count是总数
        mergeStoreDataResp.setCount(total - (originSize - afterSize));
        return mergeStoreDataResp;
    }

    @Override
    @RequireRoles(MerchantConstant.SUPER_ADMIN)
    public void changeData(ChangeDataReq changeDataReq) {
        List<ChangeDataReq.Req> reqs = changeDataReq.getReqs();
        mergeStoreBiz.changeData(reqs, changeDataReq.getMerchantId());
    }
}
