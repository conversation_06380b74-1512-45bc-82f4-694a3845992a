package com.wosai.mc.service.rpc;

import java.util.List;
import java.util.Set;

/**
 * bank-info-service 营业执照配置服务
 */
public interface BankPayBusinessConfigurationService {


    /**
     * 获取支付应用支持的营业执照类型
     *
     * @param paymentApps 支付应用列表
     * @return 支持的营业执照类型
     */
    Set<Long> getSupportedBusinessLicenseTypesByPaymentApps(List<Integer> paymentApps);


    /**
     * 获取营业执照类型下，支付应用支持的结算账户类型（取交集）
     *
     * @param businessLicenseType 营业执照类型
     * @param paymentApps 支付应用列表
     */
    Set<Integer> listSupportedSettlementAccountTypesByLicenseTypeAndApps(Integer businessLicenseType, List<Integer> paymentApps);
}
