package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.backend.api.service.CommonConfig;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.biz.*;
import com.wosai.mc.constants.LogConstant;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.entity.Store;
import com.wosai.mc.entity.converter.VerifyStoreConverter;
import com.wosai.mc.model.StoreBusinessLicenseInfo;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.StoreFilterConfig;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.*;
import com.wosai.mc.model.resp.*;
import com.wosai.mc.utils.ChineseUtil;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonAccessDeniedException;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.service.ProviderTerminalSerivce;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.mc.model.StoreConfigConstant.*;

/**
 * <AUTHOR>
 * @date 2020-08-11
 */
@Component
@AutoJsonRpcServiceImpl
public class StoreServiceImpl implements StoreService {

    private Logger log = LoggerFactory.getLogger(StoreServiceImpl.class);

    @Autowired
    com.wosai.upay.core.service.StoreService storeService;
    @Autowired
    com.wosai.upay.core.service.StoreExtService storeExtService;
    @Autowired
    com.wosai.upay.core.service.PhotoInfoService photoInfoService;
    @Autowired
    McPreBiz mcPreBiz;
    @Autowired
    com.wosai.upay.core.service.StoreService coreBstoreService;
    @Autowired
    com.wosai.upay.core.service.McPreService corBmcPreService;

    @Autowired
    SensorSendBiz sensorSendBiz;
    @Autowired
    StoreBiz storeBiz;
    @Autowired
    StoreExtBiz storeExtBiz;
    @Autowired
    DistrictsServiceV2 districtsServiceV2;

    @Autowired
    CommonApolloConfigs commonApolloConfigs;
    @Autowired
    @Lazy
    StoreBusinessLicenseService storeBusinessLicenseService;
    @Autowired
    LicenseService licenseService;
    @Autowired
    MerchantService merchantService;

    @Autowired
    ProviderTerminalSerivce providerTerminalSerivce;

    @Autowired
    FillDistrictBiz fillDistrictBiz;
    @Autowired
    private LogBiz logBiz;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Override
    public StoreInfo createStore(CreateStoreReq req) {
        StoreInfo storeInfo = storeBiz.createStore(req);
        sensorSendBiz.sendCreateStore(storeInfo);
        return storeInfo;
    }

    @Override
    public StoreCompleteResp createStoreComplete(StoreComplete storeComplete) {
        StoreInfo storeInfo = null;
        StoreBusinessLicenseInfo storeBusinessLicense = null;
        try {
            storeInfo = createStore(storeComplete.getCreateStoreReq());
            CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = storeComplete.getStoreBusinessLicenseReq();

            if (storeBusinessLicenseReq != null) {
                storeBusinessLicenseReq.setStoreId(storeInfo.getId());
                storeBusinessLicenseReq.setMerchantId(storeInfo.getMerchant_id());
                storeBusinessLicense = storeBusinessLicenseService.createStoreBusinessLicense(JSON.parseObject(JSON.toJSONString(storeBusinessLicenseReq), CreateStoreBusinessLicenseReq.class));
            }

            return new StoreCompleteResp().setStoreInfo(storeInfo).setStoreBusinessLicenseInfo(storeBusinessLicense);
        } catch (Exception e) {
            if (storeInfo != null) {
                storeService.deleteStoreById(storeInfo.getId());
            }
            if (storeBusinessLicense != null && storeInfo != null) {
                storeBusinessLicenseService.deleteStoreBusinessLicenseByStoreId(storeInfo.getId());
            }
            log.error("创建门店完整信息异常: ", e);
            throw e;
        }

    }

    @Override
    public UpdateStoreCompleteResp updateStoreComplete(UpdateStoreComplete updateStoreComplete) {
        if (WosaiStringUtils.isNotBlank(updateStoreComplete.getDevCode())) {
            updateStoreComplete.getUpdateStoreReq().setDevCode(updateStoreComplete.getDevCode());
        } else {
            updateStoreComplete.getUpdateStoreReq().setDevCodes(null).setDevCode(null);
        }
        StoreInfo storeInfo = updateStore(updateStoreComplete.getUpdateStoreReq());
        if (updateStoreComplete.getStoreBusinessLicenseReq() != null) {
            updateStoreComplete.getStoreBusinessLicenseReq().setStoreId(updateStoreComplete.getUpdateStoreReq().getId());
            storeBusinessLicenseService.updateStoreBusinessLicense(updateStoreComplete.getStoreBusinessLicenseReq(), updateStoreComplete.getDevCode());
        }
        //营业执照部分参数不为null 才对许可证数据操作.  否则不更新许可证部分数据
        if (updateStoreComplete.getStoreBusinessLicenseReq() != null) {
            StoreBusinessLicenseInfo storeBusinessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeInfo.getId(), updateStoreComplete.getDevCode());
            licenseService.updateLicense(updateStoreComplete.getUpdateLicenseReqs(), storeBusinessLicense.getId(), updateStoreComplete.getDevCode());

        }
        StoreBusinessLicenseInfo storeBusinessLicenseByStoreId = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(updateStoreComplete.getUpdateStoreReq().getId(), updateStoreComplete.getDevCode());

        return new UpdateStoreCompleteResp().setStoreInfo(storeInfo).setStoreBusinessLicenseInfo(storeBusinessLicenseByStoreId);

    }

    @Override
    public StoreInfo updateStore(UpdateStoreReq req) {
        return updateStoreV2(req, null);
    }

    @Override
    public StoreInfo updateStoreV2(UpdateStoreReq req, LogReq logReq) {
        if (WosaiCollectionUtils.isNotEmpty(req.getDevCodes())) {
            String firstCode = req.getDevCodes().get(0);
            if (WosaiStringUtils.isNotBlank(firstCode)) {
                //老逻辑中，有devCodes时，没有涉及更新原表,应该是在存在codes时，默认也存在code。因此不影响下面对写入方向的判断
                req.setDevCode(firstCode);
            }
        }

        StoreInfo oldStore = this.getStoreById(req.getId(), req.getDevCode());
        StoreExtInfo oldStoreExtInfo = storeExtBiz.findStoreExtByStoreId(req.getId(), req.getDevCode());
        StotreExtInfoAndPictures oldStorePictures = storeExtBiz.findStoreExtAndPicturesByStoreId(req.getId(), req.getDevCode());
        Map store = MyBeanUtil.toMap(req);
        boolean needLog = logReq != null && logReq.isValid();

        Map storeByStoreId = storeService.getStoreByStoreId(req.getId());
        Map newExtra = WosaiMapUtils.getMap(store, "extra", new HashMap());
        Map oldExtra = WosaiMapUtils.getMap(storeByStoreId, "extra", new HashMap());
        oldExtra.putAll(newExtra);
        store.put("extra", oldExtra);
        if (StringUtils.isNotEmpty(req.getDevCode())) {
            storeByStoreId.putAll(store);
            //如果有操作人信息
            if (needLog) {
                storeByStoreId.putAll(MyBeanUtil.toMap(logReq));
                storeByStoreId.put(LogConstant.NEED_LOG, true);
            }
            mcPreBiz.recordMcPre(TableNameEnum.STORE.getTableName(), req.getId(), req.getDevCode(), storeByStoreId);
        } else {
            boolean syncTermianl = false;
            if (req.getLongitude() != null && req.getLatitude() != null
                    && !req.getLongitude().equals(BeanUtil.getPropString(storeByStoreId, "longitude"))
                    && !req.getLatitude().equals(BeanUtil.getPropString(storeByStoreId, "latitude"))) {
                syncTermianl = true;
            }
            store = GetRealObject.filterParams(store, GetRealObject.STORE_DATA);
            Map originalStore = storeService.getStoreByStoreId(req.getId());
            if (!req.is_crm()) {
                storeBiz.checkProcessStoreCrmApply(req.getId(), store);
            }
            storeService.updateStore(store);
            //检查绑定关系
            storeBiz.handleBindStore(store, needLog, logReq);
            //检查中间表
            Map mcPre = mcPreBiz.findMcPre(TableNameEnum.STORE.getTableName(), req.getId());
            if (WosaiMapUtils.isNotEmpty(mcPre)) {
                Map data = CommonUtils.beanToTargetObj(mcPre.get("data"), Map.class);
                if (WosaiMapUtils.isNotEmpty(data)) {
                    data.putAll(store);
                    mcPreBiz.recordMcPre(TableNameEnum.STORE.getTableName(), req.getId(), "updateOrigin", data);
                }
            }
            //需要记日志
            if (needLog) {
                logBiz.saveLog(GetRealObject.filterParams(originalStore, GetRealObject.STORE_DATA), store, TableNameEnum.STORE.getTableName(), req.getId(), MapUtils.getString(originalStore, com.wosai.upay.core.model.Store.MERCHANT_ID), logReq);
            }
            if (syncTermianl) {
                providerTerminalSerivce.updateProviderTermianl(storeByStoreId);
            }
        }
        StoreExtReq storeExtReq = req.buildStoreExtReq();
        if (storeExtReq != null) {
            storeExtBiz.updateStoreExt(storeExtReq, req.getDevCode());
        }
        UpdateStorePicturesReq updateStorePicturesReq = req.buildUpdateStorePicturesReq();
        if (updateStorePicturesReq != null) {
            storeBiz.updateStorePicturesByStoreId(updateStorePicturesReq, req.getDevCode());
        }
        sensorSendBiz.sendChangeStore(oldStore, oldStoreExtInfo, oldStorePictures, req.getDevCode(), req.getOperator(), req.getPlatform());


        return getStoreById(req.getId(), req.getDevCode());
    }

    @Override
    public int updateStorePicturesByStoreId(@Valid UpdateStorePicturesReq req, String devCode) {
        StotreExtInfoAndPictures oldPictures = storeExtBiz.findStoreExtAndPicturesByStoreId(req.getStoreId(), devCode);
        storeBiz.updateStorePicturesByStoreId(req, devCode);
        sensorSendBiz.sendChangeStore(null, null, oldPictures, devCode, req.getOperator(), req.getPlatform());
        return 1;
    }

    @Override
    public StoreInfo getStoreById(String id, String devCode) {
        return storeBiz.getStoreById(id, devCode);
    }

    @Override
    public StoreInfo getLatestStoreById(@NotBlank(message = "门店ID不可为空") String id) {
        return storeBiz.getLatestStoreById(id);
    }

    @Override
    public StoreInfo getStoreBySn(String storeSn, String devCode) {
        return storeBiz.getStoreBySn(storeSn, devCode);
    }

    @Override
    public StoreInfo getStoreByClientSn(String merchantId, String clientSn) {
        Map store = storeService.getStoreByClientSn(merchantId, clientSn);
        StoreInfo storeInfo = JSONObject.parseObject(JSONObject.toJSONString(store), StoreInfo.class);
        return storeInfo;
    }


    @Override
    public int verifyStore(@Valid VerifyStoreReq req) {
        List<String> verifyStatuss = Lists.newArrayList("1", "2", "3");
        StoreInfo storeInfo = getStoreById(req.getStoreId(), null);
        Map<String, List<String>> extra = storeInfo.getExtra();
        extra = Optional.ofNullable(extra).orElse(new HashMap<String, List<String>>(16));
        for (String verifyStatus : verifyStatuss) {
            if (req.getVerifyStatus().equals(verifyStatus)) {
                extra.put(verifyStatus, req.getVerifyParams());
                verifyStatuss.remove(verifyStatus);
                for (String status : verifyStatuss) {
                    if (extra.get(status) != null) {
                        extra.get(status).removeAll(req.getVerifyParams());
                    }
                }
                break;
            }
        }
        Store store = VerifyStoreConverter.INSTANCE.do2dto(req);
        store.setExtra(extra);
        storeService.updateStore(JSONObject.parseObject(JSONObject.toJSONString(store), Map.class));
        return 1;
    }

    @Override
    public FieldStatusResp getStoreVerifyFieldStatus(String storeId) {
        Map store = storeService.getStore(storeId);
        if (MapUtils.isEmpty(store) || MapUtils.isEmpty((Map) store.get(com.wosai.upay.core.model.Store.EXTRA))) {
            return null;
        }
        Map extra = (Map) store.get(com.wosai.upay.core.model.Store.EXTRA);
        FieldStatusResp fieldStatusResp = new FieldStatusResp().build(extra);
        storeBiz.convertField(fieldStatusResp);
        if (!Objects.isNull(MapUtils.getObject(extra, MerchantConstant.BLACK)) && MapUtils.getBoolean(extra, MerchantConstant.BLACK)) {
            fieldStatusResp.setBlackField(commonApolloConfigs.getStoreBlackField());
        }
        return fieldStatusResp;
    }

    @Override
    public int copyMcInfoToStoreByMerchantId(String merchantId, String devCode) {
        List<Map> records = coreBstoreService.getStoreListByMerchantId(merchantId, new PageInfo(1, 1, null, null, Collections.singletonList(new OrderBy("ctime", OrderBy.OrderType.ASC))), null).getRecords();
        if (CollectionUtils.isNotEmpty(records) && MapUtils.isNotEmpty(records.get(0))) {
            String storeId = (String) records.get(0).get("id");
            copyMcInfoToStore(storeId, devCode);
        }
        return 1;
    }

    @Override
    public int copyMcInfoToStoreByStoreId(String storeId, String devCode) {
        copyMcInfoToStore(storeId, devCode);
        return 1;
    }

    private void copyMcInfoToStore(String storeId, String devCode) {
        Map storeData = mcPreBiz.getMcPreDataWithMcId(storeId, devCode, TableNameEnum.STORE.getTableName());
        boolean needLog = MapUtils.getBooleanValue(storeData, LogConstant.NEED_LOG);
        LogReq logReq = CommonUtils.beanToTargetObj(GetRealObject.filterParams(storeData, GetRealObject.LOG_REQ), LogReq.class);
        if (MapUtils.isNotEmpty(storeData)) {
            storeData = GetRealObject.filterParams(storeData, GetRealObject.STORE_DATA);
            Map storeOriginal = GetRealObject.filterParams(storeService.getStoreByStoreId(storeId), GetRealObject.STORE_DATA);
            storeService.updateStore(storeData);
            if (MapUtils.getString(storeData, "longitude") != null && MapUtils.getString(storeData, "latitude") != null
                    && !MapUtils.getString(storeData, "longitude").equals(BeanUtil.getPropString(storeOriginal, "longitude"))
                    && !MapUtils.getString(storeData, "latitude").equals(BeanUtil.getPropString(storeOriginal, "latitude"))) {
                providerTerminalSerivce.updateProviderTermianl(storeData);
            }
            corBmcPreService.deleteExcessData(TableNameEnum.STORE.getTableName(), storeId);
            if (needLog) {
                //记日志
                logBiz.saveLog(storeOriginal, storeData, TableNameEnum.STORE.getTableName(), storeId, MapUtils.getString(storeOriginal, com.wosai.upay.core.model.Store.MERCHANT_ID), logReq);
            }
            //todo 如果需要,还同步商户地址信息
            storeBiz.handleBindStore(storeData, needLog, logReq);

        }
        Map storeExtByStoreId = storeExtService.findStoreExtByStoreId(storeId);
        if (MapUtils.isEmpty(storeExtByStoreId)) {
            return;
        }
        String storeExtId = String.valueOf(storeExtByStoreId.get("id"));
        Map storeExtData = mcPreBiz.getMcPreDataWithMcId(storeExtId, devCode, TableNameEnum.STORE_EXT.getTableName());
        if (MapUtils.isNotEmpty(storeExtData)) {
            storeExtData = GetRealObject.filterParams(storeExtData, GetRealObject.STORE_EXT_DATA);
            storeExtService.updateStoreExt(storeExtData);
            corBmcPreService.deleteExcessData(TableNameEnum.STORE_EXT.getTableName(), storeExtId);
        }
        if (MapUtils.isNotEmpty(storeExtData)) {
            //需要处理一下照片
            this.resolvePhotos(storeExtByStoreId, storeExtData);
        }
    }


    /**
     * 将新的照片id对应的照片dev_code标志位改为null
     * 将旧的照片id对应的照片进行删除
     *
     * @param originStoreExt 原表存储的信息 待删除
     * @param newStoreExt    pre表存储的信息 待设置dev_code为null
     */
    private void resolvePhotos(Map originStoreExt, Map newStoreExt) {
        List<String> waitForDeletePhotos = new ArrayList<>();
        List<String> waitForSetDevCodeToNull = new ArrayList<>();
        this.getPhotoIds(waitForDeletePhotos, originStoreExt);
        this.getPhotoIds(waitForSetDevCodeToNull, newStoreExt);
        //如果有照片没有更换id，原表store_ext和pre的store_ext 中存储的id可能相同，需要将待删除的排除掉
        waitForDeletePhotos.removeAll(waitForSetDevCodeToNull);
        Map setDevCodeToNull = new HashMap();
        setDevCodeToNull.put("dev_code", null);
        //将新的照片dev_code标志改为null
        for (String photoId : waitForSetDevCodeToNull) {
            try {
                setDevCodeToNull.put("id", photoId);
                photoInfoService.updatePhotoinfo(setDevCodeToNull);
            } catch (Exception e) {
                log.error("更新照片dev_code为null失败 id:{} e:{}", photoId, e);
            }
        }
        //将旧照片删除
        try {
            if (WosaiCollectionUtils.isNotEmpty(waitForDeletePhotos)) {
                photoInfoService.deletePhotoInfos(waitForDeletePhotos);
            }
        } catch (Exception e) {
            log.error("删除照片失败 ids:{}", JSON.toJSONString(waitForDeletePhotos), e);
        }
    }

    public static final List PHOTO_FIELDS = Lists.newArrayList("brand_photo_id", "brand_only_scene_photo_id", "indoor_material_photo_id", "indoor_only_scene_photo_id",
            "outdoor_material_photo_id", "outdoor_only_scene_photo_id", "product_price_id", "audit_picture_id");

    /**
     * 从storeExt信息中获取所有照片的id集合
     *
     * @param photoLists 照片集合
     * @param storeExt   门店环境信息
     */
    private void getPhotoIds(List<String> photoLists, Map storeExt) {
        for (Object photoField : PHOTO_FIELDS) {
            if (WosaiStringUtils.isNotEmpty((String) storeExt.get(photoField))) {
                photoLists.add((String) storeExt.get(photoField));
            }
        }
        if (WosaiStringUtils.isNotEmpty((String) storeExt.get("other_photo_id"))) {
            String otherPhotoIds = (String) storeExt.get("other_photo_id");
            String[] split = otherPhotoIds.split(",");
            for (String s : split) {
                photoLists.add(s);
            }
        }

        if (WosaiStringUtils.isNotEmpty((String) storeExt.get("order_price_photo_id"))) {
            String orderPricePhotoIds = (String) storeExt.get("order_price_photo_id");
            String[] split = orderPricePhotoIds.split(",");
            for (String s : split) {
                photoLists.add(s);
            }
        }

    }


    @Override
    public StoreAllInfo getStoreAllInfoById(String id) {
        Map store = storeService.getStoreByStoreId(id);
        if (MapUtils.isEmpty(store)) {
            return null;
        }
        StotreExtInfoAndPictures storeExtAndPictures = storeExtBiz.findStoreExtAndPicturesByStoreId(id, null);
        if (storeExtAndPictures != null) {
            Map storeOtherInfo = JSONObject.parseObject(JSONObject.toJSONString(storeExtAndPictures), Map.class);
            storeOtherInfo.put("storeExtId", storeExtAndPictures.getId());
            storeOtherInfo.remove("id");
            store.putAll(storeOtherInfo);
        }
        Map underscoreFromLowerCameL = MyBeanUtil.getUnderscoreFromLowerCameL(store);
        Map extra = (Map) underscoreFromLowerCameL.remove("extra");
        StoreAllInfo storeAllInfo = MyBeanUtil.toBean(underscoreFromLowerCameL, StoreAllInfo.class);
        return storeAllInfo.setExtra(JSON.toJSONString(extra)).setExtra_map(extra);
    }

    @Override
    public Map<String, Object> getFirstStore(Map params) {
        String merchantSn = MapUtils.getString(params, "merchant_sn");
        String merchantId = MapUtils.getString(params, com.wosai.upay.core.model.Store.MERCHANT_ID);

        //需要直接查第一家门店的信息
        if (WosaiStringUtils.isNotEmpty(merchantSn)) {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            if (MapUtils.isEmpty(merchant)) {
                return null;
            }
            return storeBiz.getFirstStoreByMerchantId(MapUtils.getString(merchant, "id"));

        }

        return storeBiz.getFirstStoreByMerchantId(merchantId);
    }

    @Override
    public boolean isFirstStore(Map params) {
        String storeSn = MapUtils.getString(params, com.wosai.upay.core.model.Store.SN);
        String storeId = MapUtils.getString(params, "id");
        Map store = null;
        if (WosaiStringUtils.isNotEmpty(storeSn)) {
            store = storeService.getStoreByStoreSn(storeSn);
        } else {
            store = storeService.getStoreByStoreId(storeId);
        }

        if (MapUtils.isNotEmpty(store)) {
            Map firstStore = storeBiz.getFirstStoreByMerchantId(MapUtils.getString(store, com.wosai.upay.core.model.Store.MERCHANT_ID));
            return WosaiStringUtils.equals(MapUtils.getString(firstStore, "id"), MapUtils.getString(store, "id"));
        }
        return false;
    }

    @Override
    public void fillStoreDistrict(String storeSn) {
        fillDistrictBiz.fillStoreDistrict(storeSn);
    }

    @Override
    public StoreFilterConfig createOrUpdateStoreFilterConfig(CreateOrUpdateStoreFilterConfigReq req) {
        StoreFilterConfig configBefore = queryStoreFilterConfig(new QueryStoreFilterConfigReq().setMerchant_user_id(req.getMerchant_user_id()));
        StoreFilterConfig willUpdate = req.getConfig();

        if (WosaiCollectionUtils.isEmpty(willUpdate.getManual())) {
            willUpdate.setManual(configBefore.getManual());
        }
        Map config = commonConfig.appendMap(req.getMerchant_user_id(), STORE_FILTER, CommonUtils.beanToTargetObj(willUpdate, Map.class));
        return CommonUtils.beanToTargetObj(config, StoreFilterConfig.class);
    }

    @Override
    public StoreFilterConfig queryStoreFilterConfig(QueryStoreFilterConfigReq req) {
        String merchantUserId = req.getMerchant_user_id();
        Map<String, Object> configMap = commonConfig.getMap(merchantUserId, STORE_FILTER);
        //初始化一个默认配置
        if (MapUtils.isEmpty(configMap)) {
            Map config = commonConfig.appendMap(req.getMerchant_user_id(), STORE_FILTER,
                    CommonUtils.beanToTargetObj(new StoreFilterConfig()
                                    .setStatus(Arrays.asList(com.wosai.upay.core.model.Store.STATUS_ENABLED, com.wosai.upay.core.model.Store.STATUS_DISABLED))
                                    .setOrder(CTIME_DESC),
                            Map.class));
            return CommonUtils.beanToTargetObj(config, StoreFilterConfig.class);
        }
        return CommonUtils.beanToTargetObj(configMap, StoreFilterConfig.class);
    }

    @Override
    public ListResult getStoreListByMerchantUserId(GetStoreListByMerchantUserIdReq req) {
        String merchantUserId = req.getMerchant_user_id();
        //用户可以看到的门店
        List<String> storeIds = getStoreIds(req.getDesignated_store_ids(), merchantUserId);

        if (WosaiCollectionUtils.isEmpty(storeIds)) {
            return new ListResult();
        }

        StoreFilterConfig config = queryStoreFilterConfig(new QueryStoreFilterConfigReq().setMerchant_user_id(req.getMerchant_user_id()));

        Map filterParams = buildFilterParams(storeIds, config, req);

        ListResult listResult = getUserStores(filterParams, config);

        if (WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            return listResult;
        }

        Integer order = config.getOrder();
        if (req.getManual_order() || Objects.equals(order, RANK_ASC)) {
            // 手工排序
            listResult = handleManualByConfig(config, listResult.getRecords(), filterParams, merchantUserId);
        } else if (Objects.equals(order, NAME_ASC) || Objects.equals(order, NAME_DESC)) {
            // 名称排序
            List<Map> records = listResult.getRecords();
            ChineseUtil.sortByFieldName(records, com.wosai.upay.core.model.Store.NAME);
            //默认升序,此为降序
            if (Objects.equals(order, NAME_DESC)) {
                Collections.reverse(records);
            }
            listResult.setRecords(records);
        }

        //数据格式
        if (WosaiCollectionUtils.isNotEmpty(listResult.getRecords())) {
            List<Map> stores = listResult.getRecords().stream().map(store -> {
                Map copyMap = new HashMap<>(store);
                copyMap.put("storeId", MapUtils.getString(store, DaoConstants.ID));
                return copyMap;
            }).collect(Collectors.toList());
            listResult.setRecords(stores);
        }
        return listResult;
    }

    private List<String> getStoreIds(List<String> designatedStoreIds, String merchantUserId) {

        List<String> storeIds;

        if (CollectionUtils.isNotEmpty(designatedStoreIds)) {
            storeIds = designatedStoreIds;
        } else {
            storeIds = merchantUserServiceV2.getStoreIdsByMerchantUserId(merchantUserId);
        }

        return storeIds;
    }

    private Map buildFilterParams(List<String> storeIds, StoreFilterConfig config, GetStoreListByMerchantUserIdReq req) {
        Map filterParams = new HashMap<>();
        filterParams.put("store_ids", storeIds);

        if (WosaiCollectionUtils.isNotEmpty(req.getStatus_list())) {
            filterParams.put("status_list", req.getStatus_list());
        } else {
            List<Integer> status = config.getStatus();
            if (WosaiCollectionUtils.isNotEmpty(status)) {
                filterParams.put("status_list", status);
            }
        }
        if (StringUtils.isNotEmpty(req.getStore_sn())) {
            filterParams.put("sn", req.getStore_sn());
        }
        if (StringUtils.isNotEmpty(req.getStore_name())) {
            filterParams.put("store_name", req.getStore_name());
        }
        if (StringUtils.isNotEmpty(req.getClient_sn())) {
            filterParams.put("client_sn", req.getClient_sn());
        }

        return filterParams;
    }

    private ListResult getUserStores(Map filterParams, StoreFilterConfig config) {
        PageInfo pageInfo = new PageInfo(1, 9999);
        config.addPageInfoOrder(pageInfo);
        return storeService.getSimpleStoreListByMerchantIdOrStoreIdsFromSlaveDb(filterParams, pageInfo);
    }

    /**
     * @param config         当前配置
     * @param stores         当前所有可见的 store 集合
     * @param filterParams   过滤条件
     * @param merchantUserId 商户用户id
     * @return 手动排序得到的结果
     */
    private ListResult handleManualByConfig(StoreFilterConfig config, List<Map> stores, Map filterParams, String merchantUserId) {
        if (WosaiCollectionUtils.isEmpty(config.getManual())) {
            config = initManual(filterParams, merchantUserId, config);
        }

        sortByStoreId(stores, config.getManual());

        return new ListResult(stores.size(), stores);
    }

    private StoreFilterConfig initManual(Map filterMap, String merchantUserId, StoreFilterConfig config) {
        PageInfo pageInfo = new PageInfo(1, 200);
        pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));

        ListResult listResult = storeService.getSimpleStoreListByMerchantIdOrStoreIdsFromSlaveDb(filterMap, pageInfo);

        if (listResult.getTotal() > 0) {
            List<String> initStoreIds = listResult.getRecords().stream()
                    .map(store -> MapUtils.getString(store, DaoConstants.ID))
                    .collect(Collectors.toList());
            return createOrUpdateStoreFilterConfig(
                    new CreateOrUpdateStoreFilterConfigReq()
                            .setMerchant_user_id(merchantUserId)
                            .setConfig(config.setManual(initStoreIds))
            );

        }
        return config;
    }

    /**
     * 按 storeIds 内, id 顺序,排序list
     */
    private void sortByStoreId(List<Map> records, List<String> storeIds) {
        if (WosaiCollectionUtils.isEmpty(records) || WosaiCollectionUtils.isEmpty(storeIds)) {
            return;
        }

        // 创建一个Map，将id与其在orderList中的位置关联起来
        java.util.Map<String, Long> orderMap = new java.util.HashMap<>();
        for (int i = 0; i < storeIds.size(); i++) {
            orderMap.put(storeIds.get(i), (long) i);
        }

        long current = System.currentTimeMillis();

        // 使用Comparator进行排序
        Collections.sort(records, (o1, o2) -> {
            String id1 = (String) o1.get(DaoConstants.ID);
            String id2 = (String) o2.get(DaoConstants.ID);

            Long order1 = orderMap.get(id1);
            Long order2 = orderMap.get(id2);

            // 如果id在orderList中不存在，则保持原有顺序
            if (order1 == null) {
                order1 = current - BeanUtil.getPropLong(o1, DaoConstants.CTIME, current) + storeIds.size();
            }
            if (order2 == null) {
                order2 = current - BeanUtil.getPropLong(o2, DaoConstants.CTIME, current) + storeIds.size();
            }

            return Long.compare(order1, order2);
        });

    }

    @Override
    public int updateStoreRank(UpdateStoreRankReq req) {
        List<String> storeIds = req.getManual();
        if (storeIds.size() > 200) {
            storeIds = storeIds.subList(0, 200);
        }
        String merchantUserId = req.getMerchant_user_id();
        //理论上,调用该接口时一定有了配置
        StoreFilterConfig config = CommonUtils.beanToTargetObj(commonConfig.getMap(merchantUserId, STORE_FILTER), StoreFilterConfig.class);
        config.setManual(storeIds);
        commonConfig.appendMap(req.getMerchant_user_id(), STORE_FILTER, CommonUtils.beanToTargetObj(config, Map.class));
        return 1;
    }

    @Override
    public void closeStore(String storeId) {
        //查询门店,还在生效的应用 7.29更新,不再校验应用
//        ListResult appInfos = commonAppInfoService.findAppInfos(new PageInfo(1, 1),
//                CollectionUtil.hashMap("store_id", storeId, "status", "3"));
//
//        if (appInfos.getTotal() > 0) {
//            throw new CommonPubBizException("门店还有未关闭的应用,无法关闭");
//        }

        storeService.closeStore(storeId);
    }

    @Override
    public void enableStore(String storeId) {
        storeService.enableStore(storeId);
    }

    @Override
    public void disableStore(String storeId) {
        Map store = storeService.getStore(storeId);
        if (store != null) {
            int preStatus = BeanUtil.getPropInt(store, com.wosai.upay.core.model.Store.STATUS, 0);
            if (preStatus == com.wosai.upay.core.model.Store.STATUS_CLOSED) {
                throw new CommonAccessDeniedException("关闭的门店不能修改为禁用状态");
            }
        }
        storeService.disableStore(storeId);
    }

}