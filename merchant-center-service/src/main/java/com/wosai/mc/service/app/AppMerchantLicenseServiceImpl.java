package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.mc.aop.annotation.RequireRoles;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.app.resp.MerchantLicenseQueryResp;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Service
@AutoJsonRpcServiceImpl
public class AppMerchantLicenseServiceImpl implements AppMerchantLicenseService{

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Override
    @RequireRoles(MerchantConstant.SUPER_ADMIN)
    public MerchantLicenseQueryResp queryMerchantLicense(BaseAppReq req) {
        Map<String, Object> businessLicense = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(req.getMerchantId());
        return new MerchantLicenseQueryResp()
                .setName(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.NAME))
                .setMerchantId(req.getMerchantId())
                .setType(WosaiMapUtils.getInteger(businessLicense, MerchantBusinessLicence.TYPE))
                .setLegalPersonName(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.LEGAL_PERSON_NAME));
    }
}
