package com.wosai.mc.service.crm;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mc.model.app.ManagePassTokenReq;
import com.wosai.mc.model.app.resp.StoreBasicInfoQueryResp;
import com.wosai.mc.model.dto.req.MerchantIdReqDTO;
import com.wosai.mc.service.app.AppStoreService;
import com.wosai.mc.service.app.AppStoreServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2025/3/14 13:57
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class CrmAppStoreServiceImpl implements CrmAppStoreService{

    @Resource
    private AppStoreServiceImpl appStoreServiceImpl;

    /**
     * 获取首家门店的基本信息
     *
     * @param req
     * @return 返回首家门店的基本信息
     */
    @Override
    public StoreBasicInfoQueryResp queryOldestStoreBasicInfo(ApiRequestParam<MerchantIdReqDTO, Map<String, Object>> req) {
        return appStoreServiceImpl.doGetStoreBasicInfoQueryResp(req.getBodyParams().getMerchantId());
    }
}
