package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.mc.crypt.CryptBiz;
import com.wosai.mc.schedule.ConfigPollingTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@AutoJsonRpcServiceImpl
public class SupportServiceImpl implements SupportService {

    @Autowired
    private CryptBiz cryptBiz;
    @Autowired
    private ConfigPollingTask pollingTask;

    @Override
    public List<Map<String, Object>> decrypt(List<Map<String, Object>> dataMapList, List<String> fields) {
        if (WosaiCollectionUtils.isEmpty(dataMapList) || WosaiCollectionUtils.isEmpty(fields)) {
            return dataMapList;
        }
        return (List<Map<String, Object>>) (List<?>) cryptBiz.decryptList((List<Object>) (List<?>) dataMapList, fields);

    }

    @Override
    public List<String> decryptList(List<String> texts) {
        List<String> result = new ArrayList();
        for (String string : texts) {
            String decrypt = cryptBiz.decryptString(string);
            result.add(decrypt);
        }
        return result;

    }

    @Override
    public List<String> encryptList(List<String> texts) {
        return cryptBiz.encrypts(texts);
    }

    @Override
    public void pollConfig(Map emptyMap) {
        pollingTask.pollConfig();
    }
}
