package com.wosai.mc.service.common;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mc.model.app.LegalIdTypeModel;
import com.wosai.mc.model.app.LicenseTypeModel;
import com.wosai.mc.model.app.TradeLicenseTypeModel;
import com.wosai.mc.remote.bankinfo.BankInfoClient;
import com.wosai.mc.remote.bankinfo.model.LicenseDicInfo;
import com.wosai.mc.remote.bankinfo.model.PapersImageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class CommonEnumServiceImpl implements CommonEnumService {

    @Autowired
    private BankInfoClient bankInfoClient;

    @Override
    public Map<Integer, LicenseTypeModel> queryLicenseTypes() {
        Map<Integer, LicenseDicInfo> licenseTypes = bankInfoClient.queryAllLicenseDic();
        return licenseTypes.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, r ->
                new LicenseTypeModel()
                        .setType(r.getKey())
                        .setName(r.getValue().getName())
        ));
    }

    @Override
    public Map<Integer, LegalIdTypeModel> queryLegalIdTypes() {
        Map<Integer, PapersImageInfo> papersImageInfoMap = bankInfoClient.queryPapersImageInfo();
        return papersImageInfoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, r ->
                new LegalIdTypeModel()
                        .setType(r.getKey())
                        .setName(r.getValue().getName())
                        .setPhotoNum(r.getValue().getPhotoNum())
                        .setFront(r.getValue().getFront())
                        .setBack(r.getValue().getBack())
                        .setFrontImg(r.getValue().getFrontImg())
                        .setBackImg(r.getValue().getBackImg())
        ));
    }

    @Override
    public Map<Integer, TradeLicenseTypeModel> queryTradeLicenseTypes() {
        return bankInfoClient.queryAllTradeLicenseDic().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, r ->
                new TradeLicenseTypeModel()
                        .setType(r.getValue().getType())
                        .setName(r.getValue().getName()))
        );
    }
}
