package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mc.biz.FieldConfigBiz;
import com.wosai.mc.model.FieldConfig;
import com.wosai.mc.model.app.req.FieldConfigReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class AppFieldConfigServiceImpl implements AppFieldConfigService{

    @Autowired
    private FieldConfigBiz fieldConfigBiz;
    @Override
    public List<FieldConfig> queryFieldConfigs(FieldConfigReq fieldConfigReq) {
        return fieldConfigBiz.queryByTables(fieldConfigReq.getTableNames());
    }
}
