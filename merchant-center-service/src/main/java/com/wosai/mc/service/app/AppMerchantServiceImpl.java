package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.exception.MchUserSrvCommonException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.merchant.exception.MerchantServerException;
import com.wosai.mc.constants.CommonConstant;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.app.req.BindStoreReq;
import com.wosai.mc.model.app.req.MerchantQueryReq;
import com.wosai.mc.model.app.req.UnBindStoreReq;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.StoreService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
@AutoJsonRpcServiceImpl
public class AppMerchantServiceImpl implements AppMerchantService {
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private CommonAppInfoService commonAppInfoService;
    @Value("${crm_app_id.pay}")
    private String appId;

    @Override
    public boolean unBindStoreId(UnBindStoreReq unBindStoreReq) {
        String merchantId = unBindStoreReq.getMerchantId();

        Map req = CommonUtils.beanToTargetObj(unBindStoreReq, Map.class);
        req.put(DaoConstants.ID, merchantId);
        req.put(Merchant.BINDED_STORE_ID, "");

        Map updateMerchant = GetRealObject.filterParams(req, GetRealObject.MERCHANT_DATA);
        merchantService.updateMerchant(CommonUtils.beanToTargetObj(updateMerchant, UpdateMerchantReq.class), null);

        return true;
    }

    @Override
    public boolean bindStoreId(BindStoreReq bindStoreReq) {
        String storeId = bindStoreReq.getBind_store_id();

        Map store = storeService.getStoreByStoreId(storeId);
        Map map = GetRealObject.filterParams(store, CommonConstant.DISTRICT_KEYS);
        Map updateMerchant = CollectionUtil.hashMap(DaoConstants.ID, bindStoreReq.getMerchantId(), Merchant.BINDED_STORE_ID, storeId);
        updateMerchant.putAll(map);

        merchantService.updateMerchant(CommonUtils.beanToTargetObj(updateMerchant, UpdateMerchantReq.class), null);

        return true;
    }

    @Override
    public boolean isOpenedPay(BaseAppReq req) {
        ListResult appInfos = commonAppInfoService.findAppInfos(new PageInfo(1, 10),
                CollectionUtil.hashMap("merchant_id", req.getMerchantId(), "status", "3",
                        "app_id", appId
                )
        );
        if (WosaiCollectionUtils.isNotEmpty(appInfos.getRecords())) {
            return true;
        }
        return false;
    }

    @Override
    public String getMerchantName(MerchantQueryReq req) {
        MerchantInfo merchant = merchantService.getMerchantBySn(req.getMerchantSn(), null);
        if (Objects.isNull(merchant)){
            throw new MchUserSrvCommonException("商户不存在");
        }
        return StringUtils.isBlank(merchant.getBusiness_name()) ? merchant.getName() :merchant.getBusiness_name();
    }
}
