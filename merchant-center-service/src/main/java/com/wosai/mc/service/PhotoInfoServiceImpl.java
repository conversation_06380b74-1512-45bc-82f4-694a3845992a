package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.mc.utils.PhotoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.Map;
import java.util.UUID;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-28
 * @Description:
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class PhotoInfoServiceImpl implements PhotoInfoService {
    @Autowired
    com.wosai.upay.core.service.PhotoInfoService coreBPhotoInfoService;

    @Autowired
    com.wosai.upay.core.service.McPreService coreBMcPreService;

    @Override
    public PhotoInfo getPhotoInfoByIdAndDevcode(String id, String devCode) {
        Map photoInfo = null;
        if (StringUtils.isNotEmpty(devCode)) {
            photoInfo = coreBPhotoInfoService.findPhotoinfo(id, devCode);
        }
        if (MapUtils.isEmpty(photoInfo)) {
            photoInfo = coreBPhotoInfoService.findPhotoinfo(id, null);
        }
        if (MapUtils.isEmpty(photoInfo)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(photoInfo), PhotoInfo.class);
    }


    @Override
    public String updatePhotoInfo(@Valid PhotoInfo photoInfo) {
        Map photoinfo = coreBPhotoInfoService.findPhotoinfo(photoInfo.getId(), photoInfo.getDev_code());
        photoInfo.setUrl(PhotoUtils.baseUrl(photoInfo.getUrl()));

        String photoId = "";
        if (WosaiMapUtils.isEmpty(photoinfo)) {
            photoId = UUID.randomUUID().toString();
            photoInfo.setId(photoId);
            Map photo = MyBeanUtil.toMap(photoInfo);
            coreBPhotoInfoService.createPhotoinfo(photo);
            return photoId;
        } else {
            Map photo = MyBeanUtil.toMap(photoInfo);
            coreBPhotoInfoService.updatePhotoinfo(photo);
            photoId = photoInfo.getId();
        }
        return photoId;
    }
}
