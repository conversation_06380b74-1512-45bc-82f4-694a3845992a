package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.constant.LogParamsConstant;
import com.shouqianba.cua.enums.businesslog.LogPlatformEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.app.common.UserType;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.dto.multi.req.CellphoneReq;
import com.wosai.app.dto.multi.resp.NaturalPersonResp;
import com.wosai.app.service.MultiMerchantService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.biz.*;
import com.wosai.mc.config.exception.McException;
import com.wosai.mc.constants.LogConstant;
import com.wosai.mc.constants.MerchantConstant;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.entity.Merchant;
import com.wosai.mc.entity.bo.MicroUpgradeCheckResultBO;
import com.wosai.mc.entity.converter.MerchantConverter;
import com.wosai.mc.entity.converter.VerifyMerchantConverter;
import com.wosai.mc.model.*;
import com.wosai.mc.model.req.*;
import com.wosai.mc.model.resp.*;
import com.wosai.mc.remote.IMerchantService;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.merchant.audit.api.model.MerchantAudit;
import com.wosai.upay.merchant.audit.api.pojo.resp.MerchantAuditInfo;
import com.wosai.upay.merchant.audit.api.service.IndirectPayAuditService;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import com.wosai.upay.merchant.audit.api.service.MerchantSpeechCraftAuditService;
import facade.ICustomerRelationValidateFacade;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.mc.constants.MerchantConstant.SUPER_ADMIN;
import static com.wosai.mc.constants.MerchantConstant.SUPER_ADMIN_ROLE_ID;

/**
 * <AUTHOR>
 * @date 2020-08-04
 */
@Component
@AutoJsonRpcServiceImpl
public class MerchantServiceImpl implements MerchantService {
    private Logger log = LoggerFactory.getLogger(MerchantServiceImpl.class);
    @Autowired
    com.wosai.upay.core.service.MerchantService merchantService;
    @Autowired
    @Lazy
    MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    MerchantUserServiceV2 merchantUserServiceV2;
    @Autowired
    McPreBiz mcPreBiz;
    @Autowired
    com.wosai.upay.core.service.StoreService coreBstoreService;
    @Autowired
    MerchantAuditService merchantAuditService;
    @Autowired
    StoreService mcStoreService;

    @Autowired
    IMerchantService iMerchantService;
    @Autowired
    com.wosai.upay.core.service.McPreService corBmcPreService;

    @Autowired
    StoreExtService storeExtService;
    @Autowired
    MultiMerchantService multiMerchantService;
    @Autowired
    UcUserAccountService ucUserAccountService;
    @Autowired
    SensorSendBiz sensorSendBiz;
    @Autowired
    MerchantBiz merchantBiz;
    @Autowired
    MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;
    @Autowired
    StoreBiz storeBiz;
    @Autowired
    private CommonApolloConfigs commonApolloConfigs;
    @Autowired
    MerchantUserBiz merchantUserBiz;
    @Autowired
    IndirectPayAuditService indirectPayAuditService;
    @Autowired
    MerchantSpeechCraftAuditService merchantSpeechCraftAuditService;
    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    private ICustomerRelationValidateFacade customerRelationValidateFacade;

    @Autowired
    private FillDistrictBiz fillDistrictBiz;
    @Autowired
    private LogBiz logBiz;
    @Autowired
    private MerchantAffiliationBiz merchantAffiliationBiz;

    public static final String[] PICTUREKEYS = {"brand_photo_poi",
            "indoor_material_photo_poi", "outdoor_material_photo_poi", "product_price_poi"};

    @Override
    public CreateMerchantResp createMerchantComplete(MerchantComplete merchantComplete) {
        String platform = merchantComplete.getPlatform();
        CreateMerchantReq merchant = merchantComplete.getMerchant();
        CreateMerchantBusinessLicenseReq license = merchantComplete.getLicense();
        AccountReq account = merchantComplete.getAccount();
        merchantUserBiz.checkUcUserId(account);
        MerchantInfo merchantInfo = null;
        MerchantBusinessLicenseInfo merchantBusinessLicense = null;
        UcMerchantUserInfo superAdmin = null;
        try {
            merchantInfo = merchantBiz.createMerchant(merchant);
            license.setMerchantId(merchantInfo.getId());
            merchantBusinessLicense = merchantBusinessLicenseService.createMerchantBusinessLicense(license);
            superAdmin = merchantUserBiz.createSuperAdmin(platform, merchant, account, merchantBusinessLicense, merchantComplete.getIsLakala(), merchantComplete.getNeedSendSms());
            sensorSendBiz.sendCreateMerchant(merchantInfo);
        } catch (Exception e) {
            if (merchantBusinessLicense != null) {
                merchantBusinessLicenseService.deleteMerchantBusinessLicenseById(merchantBusinessLicense.getId());
            }
            if (merchantInfo != null) {
                deleteMerchantByMerchantId(merchantInfo.getId());
            }
            if (Objects.nonNull(superAdmin)) {
                merchantUserServiceV2.deleteMerchantUser(superAdmin.getMerchant_user_id());
            }
            log.error("创建用户失败，商户联系手机号为{}", merchant.getContactCellphone(), e);
            throw e;
        }
        return MerchantConverter.INSTANCE.do2dto(merchantInfo);
    }

    @Override
    public CreateMerchantResp updateMerchantComplete(@Valid UpdateMerchantComplete complete) {
        UpdateMerchantReq updateMerchantReq = complete.getMerchant();
        UpdateMerchantBusinessLicenseReq updateMerchantBusinessLicenseReq = complete.getLicense();
        //这个判断只影响发送神策时，携带的devCode的值，是来自原本的dev还是列表里的dev
        if (WosaiCollectionUtils.isNotEmpty(complete.getDevCodes())) {
            //老逻辑下,devcodes不为空时，发送神策时,循环列表中的devcode发送, 但实际发送的数据都是根据那个单个devcode查询出的(商户信息,营业执照信息)
            String firstCode = complete.getDevCodes().get(0);
            if (WosaiStringUtils.isNotBlank(firstCode)) {
                complete.setDevCode(firstCode);
            }
        }

        MerchantInfo oldMerchant = this.getMerchantById(updateMerchantReq.getId(), complete.getDevCode());
        MerchantBusinessLicenseInfo oldLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(updateMerchantReq.getId(), complete.getDevCode());
        if (updateMerchantReq != null) {
            if (updateMerchantBusinessLicenseReq != null) {
                updateMerchantReq.setMerchantType(updateMerchantBusinessLicenseReq.getType() == 0 ? 0 : updateMerchantBusinessLicenseReq.getType() == 1 ? 1 : 2);
            }
            merchantBiz.updateMerchant(updateMerchantReq, complete.getDevCode(), null);
        }
        if (updateMerchantBusinessLicenseReq != null) {
            merchantBusinessLicenseBiz.updateMerchantBusinessLicense(updateMerchantBusinessLicenseReq, complete.getDevCode());
        }
        merchantUserBiz.createOrUpdateAccount(complete);
        sensorSendBiz.sendChangeMerchant(oldMerchant, oldLicense, complete.getDevCode(), complete.getOperator(), complete.getPlatform());

        Map merchant = merchantService.getMerchant(updateMerchantReq.getId());
        MerchantInfo merchantInfo = JSONObject.parseObject(JSONObject.toJSONString(merchant), MerchantInfo.class);
        return MerchantConverter.INSTANCE.do2dto(merchantInfo);
    }


    @Override
    public CreateMerchantResp createMerchantAndStore(@Valid CreateMerchantAndStoreReq req) {
        String platform = req.getPlatform();
        CreateMerchantReq merchant = req.getMerchant();
        CreateMerchantBusinessLicenseReq license = req.getLicense();
        AccountReq account = req.getAccount();
        CreateStoreReq createStoreReq = req.getStore();

        merchantUserBiz.checkUcUserId(account);
        MerchantInfo merchantInfo = null;
        MerchantBusinessLicenseInfo merchantBusinessLicense = null;
        StoreInfo storeInfo = null;
        UcMerchantUserInfo superAdmin = null;
        try {
            merchantInfo = merchantBiz.createMerchant(merchant);
            license.setMerchantId(merchantInfo.getId());
            merchantBusinessLicense = merchantBusinessLicenseService.createMerchantBusinessLicense(license);
            superAdmin = merchantUserBiz.createSuperAdmin(platform, merchant, account, merchantBusinessLicense, req.getIsLakala(), req.getNeedSendSms());
            storeInfo = storeBiz.createStore(createStoreReq);
            sensorSendBiz.sendCreateMerchant(merchantInfo);
            sensorSendBiz.sendCreateStore(storeInfo);
        } catch (Exception e) {
            if (merchantBusinessLicense != null) {
                merchantBusinessLicenseService.deleteMerchantBusinessLicenseById(merchantBusinessLicense.getId());
            }
            if (merchantInfo != null) {
                deleteMerchantByMerchantId(merchantInfo.getId());
            }
            if (storeInfo != null) {
                coreBstoreService.deleteStoreById(storeInfo.getId());
            }
            if (Objects.nonNull(superAdmin)) {
                merchantUserServiceV2.deleteMerchantUser(superAdmin.getMerchant_user_id());
            }
            log.error("创建用户失败，商户联系手机号为{}", merchant.getContactCellphone());
            throw e;
        }
        return MerchantConverter.INSTANCE.do2dto(merchantInfo);
    }

    @Override
    public CreateMerchantResp updateMerchantAndStore(@Valid UpdateMerchantAndStoreReq req) {
        UpdateMerchantComplete merchantComplete = new UpdateMerchantComplete();
        merchantComplete.setMerchant(req.getMerchant());
        merchantComplete.setAccount(req.getAccountReq());
        merchantComplete.setLicense(req.getLicense());
        merchantComplete.setDevCode(req.getDevCode());
        merchantComplete.setOperator(req.getOperator());
        merchantComplete.setPlatform(req.getPlatform());
        CreateMerchantResp createMerchantResp = this.updateMerchantComplete(merchantComplete);
        mcStoreService.updateStore(req.getStore());
        return createMerchantResp;
    }

    @Override
    public MerchantInfo createMerchant(CreateMerchantReq req) {
        MerchantInfo merchantInfo = merchantBiz.createMerchant(req);
        sensorSendBiz.sendCreateMerchant(merchantInfo);
        return merchantInfo;
    }

    @Override
    public int updateMerchant(UpdateMerchantReq req, String devCode) {
//        MerchantInfo merchantInfo = this.getMerchantById(req.getId(), devCode);
//        merchantBiz.updateMerchant(req, devCode);
//        sensorSendBiz.sendChangeMerchant(merchantInfo, null, devCode, req.getOperator(), req.getPlatform());
//        return 1;
        return updateMerchantV2(req, devCode, null);
    }

    @Override
    public int updateMerchantV2(UpdateMerchantReq req, String devCode, LogReq logReq) {
        MerchantInfo merchantInfo = this.getMerchantById(req.getId(), devCode);
        merchantBiz.updateMerchant(req, devCode, logReq);
        sensorSendBiz.sendChangeMerchant(merchantInfo, null, devCode, req.getOperator(), req.getPlatform());
        return 1;
    }

    @Override
    public MerchantInfo getMerchantById(String merchantId, String devCode) {
        return merchantBiz.getMerchantInfoById(merchantId, devCode);
    }

    @Override
    public List<MerchantInfo> getMerchantListByMerchantIds(List<String> merchantIds) {
        return merchantBiz.getMerchantListByMerchantIds(merchantIds);
    }

    @Override
    public MerchantInfo getLatestMerchantById(@NotBlank(message = "商户id不能为空") String merchantId) {
        Map merchant = merchantService.getMerchant(merchantId);
        Map map = mcPreBiz.mergeMcPre(merchantId, merchant, TableNameEnum.MERCHANT.getTableName());
        return JSONObject.parseObject(JSONObject.toJSONString(map), MerchantInfo.class);
    }


    @Override
    public MerchantInfo getMerchantBySn(String merchantSn, String devCode) {
        return merchantBiz.getMerchantBySn(merchantSn, devCode);
    }


    @Override
    public int deleteMerchantByMerchantId(String merchantId) {
        return merchantService.deleteMerchantByMerchantId(merchantId);
    }

    @Override
    public int moveStoreImageByMerchantId(@NotBlank(message = "商户id不能为空") String merchantId) {
        List<Map> records = coreBstoreService.getStoreListByMerchantId(merchantId, new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy("ctime", OrderBy.OrderType.ASC))), null).getRecords();
        if (WosaiCollectionUtils.isEmpty(records) || WosaiMapUtils.isEmpty(records.get(0))) {
            return 0;
        }

        String storeId = BeanUtil.getPropString(records.get(0), "id");
        StoreExtInfo storeExt = storeExtService.findLastStoreExtByStoreId(storeId);
        if (Objects.nonNull(storeExt) &&
                (WosaiStringUtils.isNotEmpty(storeExt.getBrandPhotoId()) || WosaiStringUtils.isNotEmpty(storeExt.getBrandOnlyScenePhotoId()))
        ) {
            return 0;
        }

        Map auditExt = merchantAuditService.getAuditByMerchantId(merchantId);
        UpdateStorePicturesReq req = new UpdateStorePicturesReq();
        if (StringUtils.isNotEmpty(MapUtils.getString(auditExt, MerchantAudit.AUDIT_PHOTO))) {
            PhotoInfo auditPhoto = new PhotoInfo();
            auditPhoto.setUrl(MapUtils.getString(auditExt, MerchantAudit.AUDIT_PHOTO));
            req.setAuditPicture(auditPhoto);
        }
        Map extra = MapUtils.getMap(auditExt, MerchantAudit.EXTRA);
        if (MapUtils.isNotEmpty(extra)) {
            Object photoPoiInfo = extra.get("photo_poi_info");
            Map photoPoiInfos = JSON.parseObject(JSON.toJSONString(photoPoiInfo), Map.class);
            if (MapUtils.isNotEmpty(photoPoiInfos)) {
                for (String pictureKey : PICTUREKEYS) {
                    Object pictureValue = photoPoiInfos.get(pictureKey);
                    if (pictureValue != null) {
                        Map map = JSON.parseObject(JSON.toJSONString(pictureValue), Map.class);
                        PhotoInfo photoInfo = JSON.parseObject(JSON.toJSONString(pictureValue), PhotoInfo.class);
                        Object photo = map.get("photo");
                        if (photo != null) {
                            photoInfo.setUrl((String) photo);
                            if (pictureKey.equals(PICTUREKEYS[0])) {
                                req.setBrandPhoto(photoInfo);
                            } else if (pictureKey.equals(PICTUREKEYS[1])) {
                                req.setIndoorMaterialPhoto(photoInfo);
                            } else if (pictureKey.equals(PICTUREKEYS[2])) {
                                req.setOutdoorMaterialPhoto(photoInfo);
                            } else if (pictureKey.equals(PICTUREKEYS[3])) {
                                req.setProductPrice(photoInfo);
                            }
                        }
                    }
                }
                Object otherPhoto = photoPoiInfos.get("other_photo");
                if (otherPhoto != null) {
                    List<Map> maps = JSON.parseObject(JSON.toJSONString(otherPhoto), List.class);
                    List<PhotoInfo> photoInfoList = new ArrayList<>();
                    for (Map map : maps) {
                        PhotoInfo photoInfo = MyBeanUtil.toBean(map, PhotoInfo.class);
                        Object photo = map.get("photo");
                        if (photo != null) {
                            photoInfo.setUrl((String) photo);
                            photoInfoList.add(photoInfo);
                        }
                    }
                    req.setOtherPhoto(photoInfoList);
                }
            }
        }
        req.setStoreId(storeId);
        storeBiz.updateStorePicturesByStoreId(req, null);
        return 1;
    }

    @Override
    public int copyMcInfoToMerchantByMerchantId(String merchantId, String devCode) {
        Map data = mcPreBiz.getMcPreDataWithMcId(merchantId, devCode, TableNameEnum.MERCHANT.getTableName());
        boolean needLog = MapUtils.getBooleanValue(data, LogConstant.NEED_LOG);
        LogReq logReq = CommonUtils.beanToTargetObj(GetRealObject.filterParams(data, GetRealObject.LOG_REQ), LogReq.class);
        if (needLog && Objects.nonNull(logReq)) {
            LogParamsDto logParamsDto = new LogParamsDto();
            logParamsDto.setSceneTemplateCode(logReq.getLogTemplateCode());
            logParamsDto.setUserId(logReq.getOpUserId());
            logParamsDto.setUserName(logReq.getOpUserName());
            LogPlatformEnum logPlatformEnum = LogPlatformEnum.getEnumByAliasList(logReq.getPlatformCode());
            logParamsDto.setLogPlatformEnum(Objects.isNull(logPlatformEnum) ? LogPlatformEnum.CRM : logPlatformEnum);
            logParamsDto.setRemark(logReq.getRemark());
            return copyMcMerchantInfoToMerchantByMerchantId(merchantId, logParamsDto, data);
        }
        return copyMcMerchantInfoToMerchantByMerchantId(merchantId, new LogParamsDto().setSceneTemplateCode(LogParamsConstant.LOG_PARAMS_TEMPLATE_CODE_NO_ADD_NEW_LOG), data);
    }

    @Override
    public int copyMcInfoToMerchantByMerchantIdWithLog(String merchantId, String devCode, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new CommonPubBizException("sceneTemplateCode不能为空");
        }
        Map data = mcPreBiz.getMcPreDataWithMcId(merchantId, devCode, TableNameEnum.MERCHANT.getTableName());
        return copyMcMerchantInfoToMerchantByMerchantId(merchantId, dto, data);
    }

    public int copyMcMerchantInfoToMerchantByMerchantId(String merchantId, LogParamsDto dto, Map data) {
        if (MapUtils.isEmpty(data)) {
            return 1;
        }

        Map updateMerchant = GetRealObject.filterParams(data, commonApolloConfigs.getMerchantFieldFilter());
        Map originMerchant = merchantService.getMerchantByMerchantId(merchantId);
        if (WosaiStringUtils.isNotEmpty(MapUtils.getString(data, MerchantConstant.BINDED_STORE_ID))) {
            //有绑定的门店id
            merchantBiz.handleBindedStore(updateMerchant, originMerchant);
        }

        merchantService.updateMerchant(updateMerchant);
        if (!Objects.equals(LogParamsConstant.LOG_PARAMS_TEMPLATE_CODE_NO_ADD_NEW_LOG, dto.getSceneTemplateCode())) {
            logBiz.saveLogWithLogParamsDto(GetRealObject.filterParams(originMerchant, GetRealObject.MERCHANT_DATA), updateMerchant, TableNameEnum.MERCHANT.getTableName(), merchantId, null, dto);
        }
        corBmcPreService.deleteExcessData(TableNameEnum.MERCHANT.getTableName(), merchantId);

        return 1;

    }

    @Override
    public int copyMcInfoToOriginalByMerchantId(String merchantId, String devCode) {
        copyMcInfoToMerchantByMerchantId(merchantId, devCode);
        merchantBusinessLicenseService.copyMcInfoToMerchantBusinessLicenseByMerchantId(merchantId, devCode);
        mcStoreService.copyMcInfoToStoreByMerchantId(merchantId, devCode);
        return 1;
    }

    @Override
    public int verifyMerchant(@Valid VerifyMerchantReq req) {
        List<String> verifyStatuss = Lists.newArrayList("1", "2", "3");
        MerchantInfo merchantInfo = getLatestMerchantById(req.getMerchantId());
        Map<String, List<String>> extra = merchantInfo.getExtra();
        extra = Optional.ofNullable(extra).orElse(new HashMap<String, List<String>>(16));
        for (String verifyStatus : verifyStatuss) {
            if (req.getVerifyStatus().equals(verifyStatus)) {
                extra.put(verifyStatus, req.getVerifyParams());
                verifyStatuss.remove(verifyStatus);
                for (String status : verifyStatuss) {
                    if (extra.get(status) != null) {
                        extra.get(status).removeAll(req.getVerifyParams());
                    }
                }
                break;
            }

        }
        Merchant merchant = VerifyMerchantConverter.INSTANCE.do2dto(req);
        merchant.setExtra(extra);
        merchantService.updateMerchant(JSONObject.parseObject(JSONObject.toJSONString(merchant), Map.class));
        return 1;
    }

    @Override
    public FieldStatusResp getMerchantVerifyFieldStatus(String merchantId) {
        Map merchant = merchantService.getMerchant(merchantId);
        if (MapUtils.isEmpty(merchant) || MapUtils.isEmpty((Map) merchant.get(com.wosai.upay.core.model.Merchant.EXTRA))) {
            return null;
        }
        Map extra = (Map) merchant.get(com.wosai.upay.core.model.Merchant.EXTRA);
        FieldStatusResp resp = new FieldStatusResp().build(extra);
        if (!Objects.isNull(MapUtils.getObject(extra, MerchantConstant.BLACK)) && MapUtils.getBoolean(extra, MerchantConstant.BLACK)) {
            resp.setBlackField(commonApolloConfigs.getMerchantBlackField());
        }
        return resp;
    }


    @Override
    public int appealSuccess(@NotBlank(message = "商户id不能为空") String merchantId) {
        return merchantBiz.resolveBlackKey(merchantId, false);
    }

    @Override
    public int hitBlacklist(@NotBlank(message = "商户id不能为空") String merchantId) {
        return merchantBiz.resolveBlackKey(merchantId, true);
    }

    @Override
    public List<MerchantAndUcUserInfo> getMerchantAndUserInfo(GetMerchantAndLicenseReq req) {
        // 获取对应的营业执照商户id列表 这里是按照mtime进行排序的
        List<Map> licenses = merchantBusinessLicenseBiz.getLicenseByNumberOrLegalPersonIdNumber(req);
        if (WosaiCollectionUtils.isEmpty(licenses)) {
            return Lists.newArrayList();
        }
        List<String> merchantIds = licenses.stream().map(r -> (String) r.get(MerchantBusinessLicence.MERCHANT_ID)).collect(Collectors.toList());
        if (WosaiStringUtils.isNotEmpty(req.getCrmOrgCode())) {
            merchantIds = merchantIds.stream()
                    .filter(merchantId -> isMerchantMatchOrg(merchantId, req.getCrmOrgCode()))
                    .collect(Collectors.toList());
        }
        if (WosaiCollectionUtils.isEmpty(merchantIds)) {
            return Lists.newArrayList();
        }
        // 获取商户列表中的正常商户
        Map<String, Object> queryFilter = new HashMap<>(2);
        queryFilter.put("merchant_ids", merchantIds);
        queryFilter.put("status", com.wosai.upay.core.model.Merchant.STATUS_ENABLED);
        ListResult enableMerchants = merchantService.findMerchants(new PageInfo(1, licenses.size()), queryFilter);
        if (WosaiCollectionUtils.isEmpty(enableMerchants.getRecords())) {
            return Lists.newArrayList();
        }
        Map<String, Object> merchantMap = new HashMap<>(enableMerchants.getRecords().size());
        List<String> enabledMerchantIds = new ArrayList<>(enableMerchants.getRecords().size());
        for (Map record : enableMerchants.getRecords()) {
            merchantMap.put(MapUtils.getString(record, DaoConstants.ID), record);
            enabledMerchantIds.add(MapUtils.getString(record, DaoConstants.ID));
        }

        // 获取正常商户中的老板账号信息
        List<UcMerchantUserInfo> merchantUsers = merchantUserServiceV2.getMerchantUser(new QueryMerchantUserReq().setMerchant_ids(enabledMerchantIds).setRole(SUPER_ADMIN).setStatus(1));
        if (WosaiCollectionUtils.isEmpty(merchantUsers)) {
            return Lists.newArrayList();
        }
        Map<String, Object> merchantUserMap = new HashMap<>(merchantUsers.size());
        for (UcMerchantUserInfo merchantUser : merchantUsers) {
            merchantUserMap.put(merchantUser.getMerchant_id(), merchantUser.getUcUserInfo());
        }

        List<MerchantAndUcUserInfo> result = new ArrayList<>(merchantUsers.size());
        for (Map license : licenses) {
            String merchantId = (String) license.get(MerchantBusinessLicence.MERCHANT_ID);
            if (merchantUserMap.containsKey(merchantId)) {
                MerchantAndUcUserInfo merchantAndUcUserInfo = new MerchantAndUcUserInfo();
                merchantAndUcUserInfo.setMerchantInfo(JSON.parseObject(JSON.toJSONString(merchantMap.get(merchantId)), MerchantInfo.class));
                merchantAndUcUserInfo.setUcUserInfo(JSON.parseObject(JSON.toJSONString(merchantUserMap.get(merchantId)), Map.class));
                merchantAndUcUserInfo.setLicense(license);
                result.add(merchantAndUcUserInfo);
            }
        }
        return result;
    }

    private boolean isMerchantMatchOrg(String merchantId, String orgCode) {
        boolean isLklOrg = commonApolloConfigs.getLklOrgCode().contains(orgCode);
        boolean isLklMch = customerRelationValidateFacade.isLakalaPayMerchant(merchantId);
        return isLklOrg == isLklMch;
    }

    @Override
    public List<BindAccountResp> getBindAccountByIdentity(String identity) {
        // 查询证件号已经实名的账号
        Map<String, BindAccountResp> authResult = merchantUserBiz.getAuthBindAccountByIdentity(identity);
        // 查询证件号对应的可绑定的账号信息 正常商户小微或个体的老板账号
        Map<String, BindAccountResp> superResult = this.getSuperAdminBindAccountByIdentity(identity);
        authResult.putAll(superResult);
        return new ArrayList<>(authResult.values());
    }

    @Override
    public List<BindAccountResp> getBindAccountByIdentityV2(GetBindAccountByIdentityReq req) {
        List<BindAccountResp> bindAccountByIdentity = getBindAccountByIdentity(req.getIdentity());
        if (WosaiCollectionUtils.isEmpty(bindAccountByIdentity) || WosaiStringUtils.isEmpty(req.getCrmOrgCode())) {
            return bindAccountByIdentity;
        }
        return bindAccountByIdentity.stream()
                .filter(account -> isCellphoneMatchOrg(account.getCellphone(), req.getCrmOrgCode()))
                .collect(Collectors.toList());
    }

    private boolean isCellphoneMatchOrg(String cellphone, String orgCode) {
        UserType userType = merchantUserServiceV2.getUserTypeByCellphone(cellphone);
        if (userType == UserType.NOT_REGISTER) {
            return true;
        }
        boolean isLklUser = userType == UserType.LKL || userType == UserType.LKL_NOT_ACTIVE;
        boolean isLklOrg = commonApolloConfigs.getLklOrgCode().contains(orgCode);
        return isLklOrg == isLklUser;
    }

    @Override
    public List<MerchantInfo> getMerchantInfoByCellphone(String cellphone) {
        List<UcMerchantUserSimpleInfo> simpleInfoByCellphone = merchantUserServiceV2.getSimpleInfoByCellphone(cellphone);
        List<MerchantInfo> result = new ArrayList<>();
        for (UcMerchantUserSimpleInfo ucMerchantUserSimpleInfo : simpleInfoByCellphone) {
            if (SUPER_ADMIN.equals(ucMerchantUserSimpleInfo.getRole())) {
                result.add(merchantBiz.getMerchantInfoById(ucMerchantUserSimpleInfo.getMerchant_id(), null));
            }
        }
        return result;
    }

    @Override
    public boolean allowBindAccount(String merchantId, String cellphone, String identity) {
        AllowBindAccountReq req = new AllowBindAccountReq()
                .setMerchantId(merchantId)
                .setCellphone(cellphone)
                .setIdentity(identity);
        return allowBindAccountV2(req);
    }

    @Override
    public boolean allowBindAccountV2(AllowBindAccountReq req) {
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(req.getCellphone());
        if (ucUserInfo == null) {
            return true;
        }
        if (ucUserInfo.getStatus() == 0) {
            return false;
        }
        if (WosaiStringUtils.isNotEmpty(req.getCrmOrgCode()) && !isCellphoneMatchOrg(req.getCellphone(), req.getCrmOrgCode())) {
            throw new CommonInvalidParameterException("手机号已注册，请更换其他手机号");
        }
        if (WosaiStringUtils.isEmpty(req.getIdentity())) {
            return true;
        }
        NaturalPersonResp ucNaturalPersonResp = multiMerchantService.getRealNameInfoByCellphone(new CellphoneReq().setCellphone(req.getCellphone()));
        // 账号已经实名
        if (ucNaturalPersonResp != null) {
            // 实名信息和证件号一致则允许绑定或变更
            return ucNaturalPersonResp.getIdentity_no().equals(req.getIdentity());
        } else {
            // 账号未实名
            List<UcMerchantUserSimpleInfo> simpleInfoByCellphone = merchantUserServiceV2.getSimpleInfoByCellphone(req.getCellphone());
            if (WosaiCollectionUtils.isNotEmpty(simpleInfoByCellphone)) {
                for (UcMerchantUserSimpleInfo simpleInfo : simpleInfoByCellphone) {
                    if (SUPER_ADMIN.equals(simpleInfo.getRole()) &&
                            SUPER_ADMIN_ROLE_ID.equals(simpleInfo.getRole_id()) &&
                            !simpleInfo.getMerchant_id().equals(req.getMerchantId())) {
                        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(simpleInfo.getMerchant_id());
                        if (licenseInfo == null) {
                            continue;
                        }
                        // 小微或者是电饱饱这里可能是空的，查一下银行卡的信息来判断 如果是电饱饱则这里还是空的
                        if (WosaiStringUtils.isEmpty(licenseInfo.getLegal_person_id_number()) && licenseInfo.getType() == 0) {
                            Map bankAccount = merchantService.getMerchantBankAccountByMerchantId(simpleInfo.getMerchant_id());
                            licenseInfo.setLegal_person_id_number(MapUtils.getString(bankAccount, MerchantBankAccountPre.IDENTITY));
                        }
                        // 非实名用户关联老板账号，并且主体信息不一致 并且法人证件信息不为空
                        if (WosaiStringUtils.isNotEmpty(licenseInfo.getLegal_person_id_number()) && !req.getIdentity().equals(licenseInfo.getLegal_person_id_number())) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 查询证件号对应的可绑定的账号信息 正常商户小微或个体的老板账号
     *
     * @param identity 证件号
     * @return 为了去重 key为uc_user_id value为用户信息
     */
    private Map<String, BindAccountResp> getSuperAdminBindAccountByIdentity(String identity) {
        List<Map> licenses = merchantBusinessLicenseBiz.getLicenseByNumberOrLegalPersonIdNumber(new GetMerchantAndLicenseReq().setLegalPersonIdNumber(identity));

        if (WosaiCollectionUtils.isEmpty(licenses)) {
            return new HashMap<>(1);
        }
        List<String> merchantIds = new ArrayList<>();
        for (Map license : licenses) {
            if (MapUtils.getInteger(license, MerchantBusinessLicence.TYPE) > 1) {
                return new HashMap<>(1);
            }
            merchantIds.add((String) license.get(MerchantBusinessLicence.MERCHANT_ID));
        }
        Map<String, Object> queryFilter = new HashMap<>(2);
        queryFilter.put("merchant_ids", merchantIds);
        queryFilter.put("status", com.wosai.upay.core.model.Merchant.STATUS_ENABLED);
        // 获取商户列表中的正常商户
        ListResult enableMerchants = merchantService.findMerchants(new PageInfo(1, licenses.size()), queryFilter);
        if (WosaiCollectionUtils.isEmpty(enableMerchants.getRecords())) {
            return new HashMap<>(1);
        }
        // 获取正常商户中的老板账号信息
        List<String> enabledMerchantIds = new ArrayList<>(enableMerchants.getRecords().size());
        for (Map record : enableMerchants.getRecords()) {
            enabledMerchantIds.add(MapUtils.getString(record, DaoConstants.ID));
        }
        List<UcMerchantUserInfo> merchantUsers = merchantUserServiceV2.getMerchantUser(new QueryMerchantUserReq().setMerchant_ids(enabledMerchantIds).setRole(SUPER_ADMIN).setStatus(1));
        if (WosaiCollectionUtils.isEmpty(merchantUsers)) {
            return new HashMap<>(1);
        }
        Map<String, BindAccountResp> result = new HashMap<>(merchantUsers.size());
        for (UcMerchantUserInfo merchantUser : merchantUsers) {
            if (merchantUser.getUcUserInfo().getStatus() != 0) {
                BindAccountResp bindAccountResp = new BindAccountResp();
                bindAccountResp.setUc_user_id(merchantUser.getUcUserInfo().getUc_user_id());
                bindAccountResp.setCellphone(merchantUser.getUcUserInfo().getCellphone());
                result.put(bindAccountResp.getUc_user_id(), bindAccountResp);
            }
        }
        return result;
    }


    @Override
    public void storePhotoPreCheckStatus(Map params) {
        if (WosaiMapUtils.isNotEmpty(params)) {
            Map merchant = (Map) params.get("merchant");
            Map merchantSnData = (Map) merchant.get("merch_info.sn");
            String merchantSn = (String) merchantSnData.get("value");

            if (WosaiStringUtils.isBlank(merchantSn)) {
                throw new McException("商户号不可为空!");
            }
            MerchantInfo merchantInfo = merchantBiz.getMerchantBySn(merchantSn, null);
            if (Objects.nonNull(merchantInfo)) {
                if (MerchantConstant.STATUS_ENABLED != merchantInfo.getStatus()) {
                    throw new McException("商户状态非正常");
                }
                MerchantAuditInfo merchantAuditInfo = indirectPayAuditService.getAuditByMerchantId(merchantInfo.getId());
                if (Objects.isNull(merchantAuditInfo) || merchantAuditInfo.getStatus() != MerchantConstant.MERCHANT_AUDIT_APPROVE_STATUS) {
                    throw new McException("商户认证未通过");
                }
//                Integer payStatus = tradeConfigService.queryStatus(merchantInfo.getId(), MerchantConstant.PAY_STATUS);
//                if (Objects.nonNull(payStatus) && 1 != payStatus) {
//                    throw new McException("商户间连扫码状态非正常");
//                }
                Map storeInfos = WosaiMapUtils.getMap(params, "store");
                String storeSn = null;
                if (Objects.nonNull(WosaiMapUtils.getMap(storeInfos, "store_sn"))) {
                    storeSn = (String) WosaiMapUtils.getMap(storeInfos, "store_sn").get("value");
                    if (StringUtils.isNotEmpty(storeSn)) {
                        StoreInfo storeInfo = storeBiz.getStoreBySn(storeSn, null);
                        if (!Objects.equals(merchantInfo.getId(), storeInfo.getMerchant_id())) {
                            throw new McException("商户与门店不匹配，请检查门店 " + storeSn + " 是否在商户 " + merchantInfo.getSn() + " 下");
                        }
                    }
                }
                return;
            }
            throw new McException("该商户不存在");

        }
    }

    @Override
    public void updateMerchantTypeByApp(Map params) {
        String merchant_id = (String) params.get("merchant_id");
        String merchant_user_id = (String) params.get("merchant_user_id");
        Integer type = (Integer) params.get("type");

        UcMerchantUserSimpleInfo simpleInfo = merchantUserServiceV2.getSimpleInfoById(merchant_user_id);
        if (simpleInfo != null && WosaiStringUtils.equalsIgnoreCase("super_admin", simpleInfo.getRole())) {
            UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq().setId(merchant_id).setType(type);
            updateMerchant(updateMerchantReq, null);
            return;
        }
        log.error("更新商户类型失败 : params : {}  操作用户 : {}", params, simpleInfo);
        throw new McException("只有超级管理员才可以更改该信息");
    }

    @Override
    public List<RejectFieldAndSuggest> getRejectFieldAndSuggest(String merchantId) {
        List<RejectFieldAndSuggest> result = new ArrayList<>();
        MerchantAuditInfo auditByMerchantId = indirectPayAuditService.getAuditByMerchantId(merchantId);
        // 如果真实性审核数据不存在或者不是驳回状态 返回空列表
        if (auditByMerchantId == null || auditByMerchantId.getStatus() != MerchantAudit.STATUS_REJECTED) {
            return result;
        }
        List<Map> merchantSpeeches = merchantSpeechCraftAuditService.getLastMerchantAuditTemplateWithItemByMerchantId(merchantId);
        if (WosaiCollectionUtils.isEmpty(merchantSpeeches)) {
            return result;
        }
        for (Map merchantSpeech : merchantSpeeches) {
            // 获取驳回原因和修改建议
            String reason = BeanUtil.getPropString(merchantSpeech, "content");
            String suggest = BeanUtil.getPropString(merchantSpeech, "suggest");
            List<Map> auditItemList = (List<Map>) merchantSpeech.get("audit_item_list");
            if (WosaiCollectionUtils.isNotEmpty(auditItemList)) {
                for (Map map : auditItemList) {
                    RejectFieldAndSuggest rejectFieldAndSuggest = getRejectField(map, reason, suggest);
                    if (rejectFieldAndSuggest != null) {
                        result.add(rejectFieldAndSuggest);
                    }
                }
            } else {
                result.add(new RejectFieldAndSuggest().setReason(reason).setSuggest(suggest));
            }
        }
        return result;
    }

    @Override
    public void fillMerchantDistrict(String merchantSn) {
        fillDistrictBiz.fillMerchantDistrict(merchantSn);
    }

    @Override
    public ChangeAcquirerResp checkMerchantAffiliation(ChangeAcquirerReq request) {
        ChangeAcquirerResp response = new ChangeAcquirerResp();
        response.setAllowed(true);

        boolean hasAffiliation = merchantAffiliationBiz.hasAffiliation(request.getMerchant_sn());
        if (hasAffiliation) {
            response.setAllowed(false);
            response.setMsg("商户当前存在附属关系不可切换");
        }
        return response;
    }

    @Override
    public void delMcMerchant(String merchantId) {
        Map merchantInfo = merchantService.getMerchantByMerchantId(merchantId);
        if (WosaiMapUtils.isEmpty(merchantInfo)) {
            throw new CommonPubBizException("未查到商户信息");
        }
        corBmcPreService.deleteExcessData(TableNameEnum.MERCHANT.getTableName(), merchantId);
    }

    private RejectFieldAndSuggest getRejectField(Map map, String reason, String suggest) {
        String rejectFieldName = BeanUtil.getPropString(map, "name");
        String rejectField = BeanUtil.getPropString(map, "merchant_center_field");
        if (WosaiStringUtils.isEmpty(rejectField)) {
            return null;
        }
        String[] split = rejectField.split("\\.");
        String tableName = split[0];
        String fieldName = split[1];
        RejectFieldAndSuggest rejectFieldAndSuggest = new RejectFieldAndSuggest()
                .setReason(reason)
                .setSuggest(suggest)
                .setRejectFieldName(rejectFieldName);

        if ("merchant".equals(tableName) ||
                "merchant_business_license".equals(tableName) ||
                "merchant_bank_account".equals(tableName)) {
            rejectFieldAndSuggest
                    .setRejectField(fieldName)
                    .setType(RejectFieldAndSuggest.TYPE_MERCHANT)
                    .setParent(tableName);
        }
        if ("store".equals(tableName)) {
            rejectFieldAndSuggest.setType(RejectFieldAndSuggest.TYPE_STORE).setParent(tableName);
            // 存储的照片字段都是.._id,把id截掉返回出去
            if (rejectField.endsWith("id")) {
                rejectFieldAndSuggest.setRejectField(fieldName.substring(0, fieldName.lastIndexOf("_")));
            } else {
                rejectFieldAndSuggest.setRejectField(fieldName);
            }
        }
        return rejectFieldAndSuggest;
    }


    @Override
    public void closeMerchantWithLog(String merchantId, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new CommonPubBizException("sceneTemplateCode不能为空");
        }
        merchantBiz.closeMerchantWithLog(merchantId, dto);
    }

    @Override
    public void disableMerchantWithLog(String merchantId, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new CommonPubBizException("sceneTemplateCode不能为空");
        }
        merchantBiz.disableMerchantWithLog(merchantId, dto);
    }

    public void enableMerchantWithLog(String merchantId, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new CommonPubBizException("sceneTemplateCode不能为空");
        }
        merchantBiz.enableMerchantWithLog(merchantId, dto);
    }

    @Autowired
    private ContractStatusService contractStatusService;

    @Autowired
    private MerchantBankService merchantBankService;

    /**
     * 是否可以提交对公账户审核
     * 如果不可以，抛出异常（适配审批中心接口标准）
     *
     * @param paramsMap 参数
     */
    @Override
    public void isCanSubmitCorporateReceiptAudit(Map paramsMap) {
        String merchantSn = getMerchantSnFromAuditParams(paramsMap);
        isMerchantCanSubmitCorporateReceiptAudit(merchantSn);
    }

    /**
     * 是否可以提交营业执照公示网截图审核
     * 商户间连扫码进件通过、商户类型非小微
     * 如果不可以，抛出异常（适配审批中心接口标准）
     *
     * @param paramsMap 参数
     */
    @Override
    public void isCanSubmitLicensePubScreenshotAudit(Map paramsMap) {
        String merchantSn = getMerchantSnFromAuditParams(paramsMap);
        isContractSuccess(merchantSn);
        String merchantId = getMerchantIdByMerchantSn(merchantSn);
        MerchantBusinessLicenseInfo licenseInfo = getMerchantBusinessLicenseInfo(merchantId);
        if (BusinessLicenseTypeEnum.isMicro(licenseInfo.getType())) {
            throw new CommonPubBizException("营业执照类型不满足");
        }
    }

    public void isMerchantCanSubmitCorporateReceiptAudit(String merchantSn) {
        isContractSuccess(merchantSn);
        String merchantId = getMerchantIdByMerchantSn(merchantSn);
        MerchantBusinessLicenseInfo licenseInfo = getMerchantBusinessLicenseInfo(merchantId);
        if (Objects.isNull(licenseInfo) || BusinessLicenseTypeEnum.isMicro(licenseInfo.getType())) {
            throw new CommonPubBizException("营业执照类型不可以是小微");
        }
        Map bankAccount = getBankAccount(merchantId);
        if (MapUtils.isEmpty(bankAccount) || !BankAccountTypeEnum.isPersonal(MapUtils.getIntValue(bankAccount, MerchantBankAccount.TYPE))) {
            throw new CommonPubBizException("结算账户必须是对私");
        }
    }

    private String getMerchantIdByMerchantSn(String merchantSn) {
        Map merchantMap = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantMap)) {
            throw new CommonPubBizException("商户不存在");
        }
        String merchantId = MapUtils.getString(merchantMap, "id");
        return merchantId;
    }

    private Map getBankAccount(String merchantId) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId, MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_TRUE));
        Map bankAccount = null;
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        if (CollectionUtils.isEmpty(bankAccount)) {
            throw new CommonPubBizException("获取商户卡信息为空");
        }
        return bankAccount;
    }

    private MerchantBusinessLicenseInfo getMerchantBusinessLicenseInfo(String merchantId) {
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        if (Objects.isNull(licenseInfo)) {
            throw new CommonPubBizException("商户营业执照信息为空");
        }
        return licenseInfo;
    }

    private void isContractSuccess(String merchantSn) {
        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus) || StringUtils.isEmpty(contractStatus.getAcquirer())
                || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
            throw new CommonPubBizException("间连扫码进件未通过");
        }
    }

    private String getMerchantSnFromAuditParams(Map paramsMap) {
        if (MapUtils.isEmpty(paramsMap) || !paramsMap.containsKey("merchantInfo")) {
            throw new CommonPubBizException("参数错误");
        }
        Map merchant = (Map) paramsMap.get("merchantInfo");
        Map merchantSnData = (Map) merchant.get("merch_info.sn");
        if (MapUtils.isEmpty(merchantSnData)) {
            throw new CommonPubBizException("商户信息为空");
        }
        String merchantSn = (String) merchantSnData.get("value");
        if (WosaiStringUtils.isBlank(merchantSn)) {
            throw new CommonPubBizException("商户号不可为空!");
        }
        return merchantSn;
    }
}