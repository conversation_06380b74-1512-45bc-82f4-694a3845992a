package com.wosai.mc.service.crm;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mc.biz.MerchantBiz;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.dto.req.MerchantIdReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.Map;

/**
 * crm-商户服务
 *
 * <AUTHOR>
 * @date 2025/3/17 17:07
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class CrmMerchantServiceImpl implements CrmMerchantService{

    @Resource
    private MerchantBiz merchantBiz;

    @Override
    public MerchantInfo getMerchantInfoByMerchantId(ApiRequestParam<MerchantIdReqDTO, Map<String, Object>> applyReqDTO) {
        return merchantBiz.getMerchantInfoById(applyReqDTO.getBodyParams().getMerchantId(), null);

    }
}
