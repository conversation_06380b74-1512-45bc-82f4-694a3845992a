package com.wosai.mc.service.app;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.apolloBeans.model.FieldRuleConfig;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.app.LegalIdTypeModel;
import com.wosai.mc.model.app.LicenseTypeModel;
import com.wosai.mc.model.app.req.BusinessLicenseInfoQueryReq;
import com.wosai.mc.model.app.req.LegalIdTypesQueryReq;
import com.wosai.mc.model.app.req.LicenseTypeQueryReq;
import com.wosai.mc.model.app.resp.BusinessLicenseInfoQueryResp;
import com.wosai.mc.remote.bankinfo.BankInfoClient;
import com.wosai.mc.remote.bankinfo.model.LicenseDicInfo;
import com.wosai.mc.remote.bankinfo.model.LicenseTypeCode;
import com.wosai.mc.remote.bankinfo.model.PapersImageInfo;
import com.wosai.mc.remote.bankinfo.model.TradeLicenseDicInfo;
import com.wosai.tools.service.BusinessCheckService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class AppCommonServiceImpl implements AppCommonService {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private BankInfoClient bankInfoClient;
    @Autowired
    private BusinessCheckService businessCheckService;
    @Autowired
    private CommonApolloConfigs commonApolloConfigs;

    @Override
    public List<LicenseTypeModel> queryMerchantSupportLicenseTypes(BaseAppReq appReq) {
        Map merchant = merchantService.getMerchantByMerchantId(appReq.getMerchantId());
        String industryId = WosaiMapUtils.getString(merchant, Merchant.INDUSTRY);
        List<Integer> licenseTypes = bankInfoClient.queryIndustrySupportLicenseTypes(industryId);
        Map<Integer, LicenseDicInfo> licenseDicInfoMap = bankInfoClient.queryAllLicenseDic();
        return licenseTypes.stream().map(
                        r -> new LicenseTypeModel().setType(r).setName(licenseDicInfoMap.get(r).getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LegalIdTypeModel> queryLicenseSupportLegalIdTypes(LegalIdTypesQueryReq legalIdTypesQueryReq) {
        Map<Integer, LicenseDicInfo> licenseDicInfoMap = bankInfoClient.queryAllLicenseDic();
        LicenseDicInfo licenseDicInfo = licenseDicInfoMap.get(legalIdTypesQueryReq.getLicenseType());
        if (Objects.isNull(licenseDicInfo)) {
            throw new CommonInvalidParameterException("未找到营业执照类型相关的信息");
        }
        Map<Integer, PapersImageInfo> papersImageInfoMap = bankInfoClient.queryPapersImageInfo();
        List<Integer> legalPersonTypes = licenseDicInfo.getLegalPersonTypes();
        Collections.sort(legalPersonTypes);
        List<LegalIdTypeModel> result = new ArrayList<>();
        for (Integer legalPersonType : legalPersonTypes) {
            PapersImageInfo papersImageInfo = papersImageInfoMap.get(legalPersonType);
            result.add(new LegalIdTypeModel()
                    .setType(legalPersonType)
                    .setName(papersImageInfo.getName())
                    .setPhotoNum(papersImageInfo.getPhotoNum())
                    .setFront(papersImageInfo.getFront())
                    .setBack(papersImageInfo.getBack())
                    .setFrontImg(papersImageInfo.getFrontImg())
                    .setBackImg(papersImageInfo.getBackImg()));
        }
        return result;
    }

    @Override
    public List<LicenseTypeModel> queryMerchantSupportTradeLicenseTypes(BaseAppReq appReq) {
        Map merchant = merchantService.getMerchantByMerchantId(appReq.getMerchantId());
        String industryId = WosaiMapUtils.getString(merchant, Merchant.INDUSTRY);
        List<Integer> tradeLicenseTypes = bankInfoClient.queryIndustrySupportTradeLicenses(industryId);
        Map<Integer, TradeLicenseDicInfo> tradeLicenseDicInfoMap = bankInfoClient.queryAllTradeLicenseDic();
        Collections.sort(tradeLicenseTypes);
        return tradeLicenseTypes.stream().map(r -> new LicenseTypeModel()
                        .setType(r)
                        .setName(tradeLicenseDicInfoMap.get(r).getName()))
                .collect(Collectors.toList());
    }

    @Override
    public Integer queryLicenseType(LicenseTypeQueryReq req) {
        String number = req.getNumber();
        Collection<LicenseDicInfo> licenseDicInfos = bankInfoClient.queryAllLicenseDic().values();
        for (LicenseDicInfo licenseDicInfo : licenseDicInfos) {
            List<LicenseTypeCode> typeCodes = licenseDicInfo.getTypeCodes();
            if (WosaiCollectionUtils.isNotEmpty(typeCodes)) {
                for (LicenseTypeCode typeCode : typeCodes) {
                    //三合一类型规则
                    if (Integer.valueOf(1).equals(typeCode.getType()) && number.startsWith(typeCode.getCode())) {
                        return licenseDicInfo.getType();
                    }
                    //个体工商户、非三合一规则
                    if (Integer.valueOf(1).equals(licenseDicInfo.getType()) && number.length() == 15) {
                        return licenseDicInfo.getType();
                    }
                }
            }
        }
        throw new CommonInvalidParameterException("未找到营业证照注册号对应的营业证照类型");
    }

    @Override
    public BusinessLicenseInfoQueryResp queryBusinessLicenseInfo(BusinessLicenseInfoQueryReq req) {
        try {
            Map businessInfo = businessCheckService.getDetailsByName(req.getNumber());
            if (MapUtils.isEmpty(businessInfo)) {
                return null;
            }
            return new BusinessLicenseInfoQueryResp()
                    .setNumber(WosaiMapUtils.getString(businessInfo, "credit_code"))
                    .setName(WosaiMapUtils.getString(businessInfo, "name"))
                    .setLegalPersonName(WosaiMapUtils.getString(businessInfo, "oper_name"))
                    .setBusinessScope(WosaiMapUtils.getString(businessInfo, "scope"))
                    .setAddress(WosaiMapUtils.getString(businessInfo, "address"))
                    .setStartValidity(WosaiMapUtils.getString(businessInfo, "term_start"))
                    .setEndValidity(WosaiMapUtils.getString(businessInfo, "team_end"));
        } catch (Throwable e) {
            log.error("findBusinessInfo param:{}, error:", req.getNumber(), e);
            throw new CommonInvalidParameterException("营业执照查询失败,请重新上传");
        }
    }

    @Override
    public List<LicenseTypeModel> queryAllTradeLicenses(BaseAppReq baseAppReq) {
        return bankInfoClient.queryAllTradeLicenseDic().values().stream().map(
                r -> new LicenseTypeModel().setType(r.getType()).setName(r.getName())
        ).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> queryFieldRuleConfig(BaseAppReq appReq) {
        FieldRuleConfig fieldRuleConfig = commonApolloConfigs.getFieldRuleConfig();
        return JSON.parseObject(JSON.toJSONString(fieldRuleConfig), Map.class);
    }
}
