package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.businesslog.LogPlatformEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.shouqianba.cua.enums.core.LicenseSettlementAccountTypeEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.shouqianba.model.dto.response.McAcquirerRspDTO;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.mc.aop.annotation.RequireMask;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.biz.*;
import com.wosai.mc.biz.processor.PhotoProcessor;
import com.wosai.mc.biz.validator.BusinessLicenseUpgradeValidator;
import com.wosai.mc.config.exception.McException;
import com.wosai.mc.constants.CrmApplyConstant;
import com.wosai.mc.dao.BusinessLicenseEditDraftDAO;
import com.wosai.mc.entity.BusinessLicenseEditDraft;
import com.wosai.mc.entity.bo.EditLicenseTokenBO;
import com.wosai.mc.entity.bo.MicroUpgradeCheckResultBO;
import com.wosai.mc.entity.enums.BusinessLicenseVerifyStatusEnum;
import com.wosai.mc.model.*;
import com.wosai.mc.model.dto.BankAccountDTO;
import com.wosai.mc.model.dto.BusinessLicenseDTO;
import com.wosai.mc.model.dto.req.*;
import com.wosai.mc.model.dto.rsp.ApplyEditBusinessLicenseTokenResultRspDTO;
import com.wosai.mc.model.dto.rsp.BusinessLicenseAuditEditDraftDTO;
import com.wosai.mc.model.dto.rsp.CheckEditBusinessLicenseTokenResultRspDTO;
import com.wosai.mc.model.dto.rsp.SaveBusinessLicenseDraftResultRspDTO;
import com.wosai.mc.model.req.GetBusinessLicenseReq;
import com.wosai.mc.model.req.LogReq;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.model.resp.CheckBankAccountResp;
import com.wosai.mc.model.resp.LicenseApplyCheckResultRsp;
import com.wosai.mc.service.rpc.BankPayBusinessConfigurationService;
import com.wosai.mc.service.rpc.JobBusinessLicenceTaskService;
import com.wosai.mc.service.rpc.client.CustomerRelationClient;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.sales.merchant.business.bean.app.request.GetFieldAppStatusReq;
import com.wosai.sales.merchant.business.bean.app.response.GetFieldAppStatusResp;
import com.wosai.sales.merchant.business.service.common.CommonAppConfigService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.bank.info.api.dto.IdentificationCertificateManagementDTO;
import com.wosai.upay.bank.info.api.model.BusinessLicenseDicChangeVo;
import com.wosai.upay.bank.info.api.model.BusinessLicenseId;
import com.wosai.upay.bank.info.api.model.IdParam;
import com.wosai.upay.bank.info.api.service.AppBankInfoService;
import com.wosai.upay.bank.info.api.service.BusinessLicenseDicV2Service;
import com.wosai.upay.bank.info.api.service.IdentificationCertificateManagementService;
import com.wosai.upay.bank.info.api.service.PayBusinessConfigurationService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.exception.CommonInvalidParameterException;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.merchant.audit.api.model.BusinessLicenseAudit;
import com.wosai.upay.merchant.audit.api.pojo.resp.MerchantAuditInfo;
import com.wosai.upay.merchant.audit.api.service.IndirectPayAuditService;
import com.wosai.upay.merchant.audit.api.service.MerchantBusinessLicenseAuditService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import entity.common.OrganizationEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 营业执照服务2.0
 *
 * <AUTHOR>
 * @date 2025/1/16 16:07
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class BusinessLicenseV2ServiceImpl implements BusinessLicenseV2Service {

    @Resource
    private BusinessLicenseEditDraftDAO businessLicenseEditDraftDAO;

    @Resource
    private MerchantBiz merchantBiz;

    @Resource
    private RedisLock redisLock;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;

    @Autowired
    private CommonFieldService commonFieldService;

    @Resource
    private JobBusinessLicenceTaskService jobBusinessLicenceTaskService;

    @Resource
    private BusinessLicenseUpgradeValidator businessLicenceUpgradeValidator;

    @Autowired
    private ContractStatusService contractStatusService;

    @Autowired
    private AppBankInfoService appBankInfoService;

    @Autowired
    private PayBusinessConfigurationService payBusinessConfigurationService;

    @Resource
    private BankPayBusinessConfigurationService bankPayBusinessConfigurationService;

    @Autowired
    private CommonAppInfoService commonAppInfoService;

    @Autowired
    private BusinessLicenseDicV2Service businessLicenseDicV2Service;

    @Autowired
    private AcquirerService acquirerService;

    @Autowired
    @Lazy
    private BankAccountBiz bankAccountBiz;


    public static final String LICENSE_EDIT_TOKEN_KEY = "merchant-center:business-license-edit-token:";

    public static final String LICENSE_EDIT_LOCK_KEY = "merchant-center:business-license-edit-lock:";

    private static final String CRM_BUSINESS_LICENSE_FIELD_KEY = "business_license2";

    private static final Set<Integer> AUTHENTICITY_PROCESSING_STATUS = Sets.newHashSet(1, 2);

    private String getMerchantSn(String merchantId) {
        MerchantInfo merchantInfo = merchantBiz.getMerchantInfoById(merchantId, null);
        if (Objects.isNull(merchantInfo)) {
            throw new McException("商户不存在");
        }
        return merchantInfo.getSn();
    }

    private MerchantInfo getMerchant(String merchantId) {
        MerchantInfo merchantInfo = merchantBiz.getMerchantInfoById(merchantId, null);
        if (Objects.isNull(merchantInfo)) {
            throw new McException("商户不存在");
        }
        return merchantInfo;
    }

    /**
     * 获取商户营业执照信息
     *
     * @param merchantId 商户号
     * @return 商户营业执照信息
     */
    @Override
    public MerchantBusinessLicenseInfo getBusinessLicense(String merchantId) {
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(merchantId, null, true);
        if (Objects.isNull(licenseInfo)) {
            return null;
        }
        if (StringUtils.isBlank(licenseInfo.getRegistered_legal_person_name())) {
            licenseInfo.setRegistered_legal_person_name(licenseInfo.getLegal_person_name());
        }
        Map extra = licenseInfo.getExtra();
        if (!MapUtils.isEmpty(extra)) {
            licenseInfo.setAuxiliaryProofMaterials(PhotoProcessor.processUrls(MapUtils.getString(extra, MerchantBusinessLicenseInfo.AUXILIARY_PROOF_MATERIALS_KEY, null), true));
        }
        return licenseInfo;
    }

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Override
    public MerchantBusinessLicenseInfo getBusinessLicense(AppCommonFieldDTO appCommonFieldDTO) {
        MerchantBusinessLicenseInfo businessLicense = getBusinessLicense(appCommonFieldDTO.getMerchantId());
        return businessLicense;
    }

    @Override
    @RequireMask
    public MerchantBusinessLicenseInfo getEncryptBusinessLicense(GetBusinessLicenseReq businessLicenseReq) {
        AppCommonFieldDTO appCommonFieldDTO = new AppCommonFieldDTO();
        appCommonFieldDTO.setPlatform(businessLicenseReq.getPlatform());
        appCommonFieldDTO.setMerchantId(businessLicenseReq.getMerchantId());
        MerchantBusinessLicenseInfo businessLicense = getBusinessLicense(appCommonFieldDTO);
        if (appCommonFieldDTO.fromMsp()) {
            UcMerchantUserInfo userInfo = merchantUserServiceV2.getSuperAdminByMerchantId(appCommonFieldDTO.getMerchantId());
            String name = userInfo != null ? userInfo.getName() : "";
            String cellphone = "";
            if (userInfo != null && userInfo.getUcUserInfo() != null && userInfo.getUcUserInfo().getCellphone() != null) {
                String fullCellphone = userInfo.getUcUserInfo().getCellphone();
                cellphone = fullCellphone.length() >= 4 ? fullCellphone.substring(fullCellphone.length() - 4) : fullCellphone;
            }
            String watermarkText = name + "-" + cellphone;
            PhotoProcessor.signWithWatermark(businessLicense, watermarkText);
        } else {
            PhotoProcessor.sign(businessLicense);
        }
        return businessLicense;
    }

    /**
     * 获取商户营业执照认证状态
     * 需要兼容老的流程
     *
     * @param appCommonFieldDTO 请求dto
     * @return 认证状态 0-未知  1-未认证  2-认证成功  3-认证失败  4-认证中
     */
    @Override
    public Integer getLicenseCertificationStatus(AppCommonFieldDTO appCommonFieldDTO) {
        try {
            String merchantId = appCommonFieldDTO.getMerchantId();
            // 需要先查看申请单，再看营业执照表
            Optional<Integer> crmApplyStatusOpt = getCrmBusinessLicenseApplyStatus(merchantId);
            if (crmApplyStatusOpt.isPresent()) {
                return crmApplyStatusOpt.get();
            }
            // 申请单没有，兼容老的流程
            Boolean hasNotFinishedLicenseTask = isExistedOldLicenseUpgradingTask(merchantId);
            // Boolean hasNotFinishedLicenseTask = businessLicenceTaskService.hasLicenceUpdateNotFinishedTask(merchantSn);
            if (hasNotFinishedLicenseTask) {
                return BusinessLicenseVerifyStatusEnum.VERIFYING.getValue();
            }
            MerchantBusinessLicenseInfo existedLicenseInfo = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(merchantId, null, true);
            if (Objects.nonNull(existedLicenseInfo)) {
                if (Objects.equals(existedLicenseInfo.getType(), BusinessLicenseTypeEnum.MICRO.getValue())
                        || Objects.isNull(existedLicenseInfo.getType())) {
                    return BusinessLicenseVerifyStatusEnum.NOT_VERIFIED.getValue();
                }
                // 有营业执照，但是目前表中存在状态是0-未知或者1-未认证，已经和产品沟通，返回认证失败。
                if (Objects.equals(existedLicenseInfo.getVerify_status(), BusinessLicenseVerifyStatusEnum.UNKNOWN.getValue())
                        || Objects.equals(existedLicenseInfo.getVerify_status(), BusinessLicenseVerifyStatusEnum.NOT_VERIFIED.getValue())) {
                    return BusinessLicenseVerifyStatusEnum.VERIFY_FAILED.getValue();
                }
                return existedLicenseInfo.getVerify_status();
            }
            return BusinessLicenseVerifyStatusEnum.NOT_VERIFIED.getValue();
        } catch (Exception e) {
            log.error("getLicenseCertificationStatus error, merchantId:{}", appCommonFieldDTO.getMerchantId(), e);
            return BusinessLicenseVerifyStatusEnum.NOT_VERIFIED.getValue();
        }
    }

    private Boolean isExistedOldLicenseUpgradingTask(String merchantId) {
        Map businessLicenseAudit = merchantBusinessLicenseAuditService.getRecentAuditByMerchantId(merchantId);
        if (Objects.isNull(businessLicenseAudit)) {
            return false;
        }
        Integer status = MapUtils.getIntValue(businessLicenseAudit, BusinessLicenseAudit.BUSINESS_LICENSE_STATUS);
        return Objects.equals(status, BusinessLicenseAudit.STATUS_PENDING);
    }

    private Optional<Integer> getCrmBusinessLicenseApplyStatus(String merchantId) {
        GetFieldAppStatusReq getFieldAppStatusReq = new GetFieldAppStatusReq();
        getFieldAppStatusReq.setMerchantId(merchantId);
        getFieldAppStatusReq.setFieldType(CRM_BUSINESS_LICENSE_FIELD_KEY);
        GetFieldAppStatusResp fieldAppStatus = commonFieldService.getFieldAppStatus(getFieldAppStatusReq);
        if (Objects.isNull(fieldAppStatus)) {
            return Optional.empty();
        }
        if (Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_FAIL)) {
            return Optional.of(BusinessLicenseVerifyStatusEnum.VERIFY_FAILED.getValue());
        }
        if (Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_INIT) ||
                Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_PENDING)) {
            return Optional.of(BusinessLicenseVerifyStatusEnum.VERIFYING.getValue());
        }
        if (Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_SUCCESS)) {
            return Optional.of(BusinessLicenseVerifyStatusEnum.VERIFY_SUCCESS.getValue());
        }
        return Optional.empty();
    }

    @Autowired
    private IndirectPayAuditService indirectPayAuditService;

    @Autowired
    private com.shouqianba.service.AcquirerService accessAcquirerService;

    /**
     * 商户是否可以提交营业执照认证
     *    - 收单机构必须是三方
     *    - 真实性审核任务中不可以提交
     *    - 存在进行中的营业执照认证任务 （该逻辑不校验）
     *    - 未开通间连扫码
     *    - 如果是小微
     *      - 校验当前提供收款服务的收单机构是否支持营业执照认证
     *      - 校验商户是否存在进行中的支付开通业务
     *      - 校验商户是否正在切收单机构
     *      - 商户在富友 商户在拉卡拉或海科入网成功、且微信/支付宝子商户号已认证、且收单机构商户状态为有效
     *      - 有进行中的换卡任务，如有则不允许提交认证
     *      - 商户是否有生效「拉卡拉一体化刷卡」或「富友一体化刷卡」参数、且当前已绑定一体化终端，如有则不允许提交认证
     *      - 有预授权未完成交易，有风险冻结的资金，开通商户间分账 不允许提交认证
     *
     * @param appCommonFieldDTO 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp isMerchantAbleForLicenseCertify(AppCommonFieldDTO appCommonFieldDTO) {
        String merchantId = appCommonFieldDTO.getMerchantId();
        String merchantSn = getMerchantSn(merchantId);
        MerchantAuditInfo audit = indirectPayAuditService.getAuditByMerchantId(merchantId);
        if (Objects.nonNull(audit) && AUTHENTICITY_PROCESSING_STATUS.contains(audit.getStatus())) {
            return CommonResultResp.FAIL("真实性审核任务进行中");
        }
        CommonResultResp commonResultResp;
        try {
            commonResultResp = doCheckMerchantAbleForLicenseCertify(merchantSn, appCommonFieldDTO);
        } catch (Exception e) {
            log.error("校验是否可以提交营业执照认证失败, merchantSn:{}", merchantSn,  e);
            commonResultResp = CommonResultResp.FAIL(e.getMessage());
        }
        if (!commonResultResp.getResult()) {
            ErrorMsgViewEndpointTypeEnum typeEnum = appCommonFieldDTO.fromAppOrMsp() ? ErrorMsgViewEndpointTypeEnum.APP :
                    ErrorMsgViewEndpointTypeEnum.CRM;
            return CommonResultResp.FAIL(businessLicenceUpgradeValidator.getMicroUpgradeCheckFailPromptMessage(typeEnum, commonResultResp.getMessage()));
        }
        return CommonResultResp.SUCCESS();
    }

    private CommonResultResp doCheckMerchantAbleForLicenseCertify(String merchantSn, AppCommonFieldDTO appCommonFieldDTO) {
        // 1. 存在进行中的营业执照认证任务
//        MicroUpgradeCheckResultBO microUpgradeCheckResultBO = businessLicenceUpgradeValidator.checkIsExistedUpgradeProcessingTask(merchantId);
//        if (!microUpgradeCheckResultBO.isCanUpgrade()) {
//            return CommonResultResp.fail(microUpgradeCheckResultBO.getMessage());
//        }
        // 1. 未开通间连扫码
        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus)
                || !Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)
                || StringUtils.isBlank(contractStatus.getAcquirer())) {
            return CommonResultResp.FAIL("尚未开通间连扫码");
        }
        // 2. 必须是三方收单机构
        String acquirer = contractStatus.getAcquirer();
        McAcquirerRspDTO mcAcquirerByAcquirer = accessAcquirerService.getMcAcquirerByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerByAcquirer) ||
                !Objects.equals(mcAcquirerByAcquirer.getType(), AcquirerOrgTypeEnum.THIRD_PARTY.getValue())
                || commonApolloConfigs.getLicenseUpgradeUnSupportAcquirer().contains(acquirer)) {
            return CommonResultResp.FAIL("当前提供服务的收单机构不支持提交营业证照变更");
        }
        // 3 如果是小微
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(appCommonFieldDTO.getMerchantId(), null, false);
        if (Objects.isNull(licenseInfo) || Objects.isNull(licenseInfo.getType()) || BusinessLicenseTypeEnum.isMicro(licenseInfo.getType())) {
            MicroUpgradeCheckResultBO checkResult = businessLicenceUpgradeValidator.checkMicroEnableUpgrade(appCommonFieldDTO.getMerchantId());
            if (!checkResult.isCanUpgrade()) {
                return CommonResultResp.FAIL(checkResult.getMessage());
            }
        }
        // 4 获取不到可选的营业执照类型
        Map<Integer, String> selectedTypes = listSelectableLicenseType(appCommonFieldDTO);
        if (MapUtils.isEmpty(selectedTypes)) {
            return CommonResultResp.FAIL("当前所在收单机构可选的营业执照类型为空");
        }
        // 5 经营名称任务
        if (checkExistedBusinessNameAudit(appCommonFieldDTO.getMerchantId())) {
            return CommonResultResp.FAIL("存在进行中的商户经营名称变更任务，待经营名称审核通过后再提交证照变更");
        }
        return CommonResultResp.SUCCESS();
    }

    /**
     * 商户可选的营业执照类型
     * 行业支持的营业执照类型 +  商户已经开通的应用支持的营业执照类型 取交集
     *
     * @param appCommonFieldDTO 请求dto
     */
    @Override
    public Map<Integer, String> listSelectableLicenseType(AppCommonFieldDTO appCommonFieldDTO) {
        String merchantId = appCommonFieldDTO.getMerchantId();
        // 行业支持的营业执照类型
        List<Integer> supportedBusinessLicenseTypesByIndustry = getSupportedBusinessLicenseTypesByIndustry(merchantId);
        // 应用支持的营业执照类型
        Set<Integer> supportedBusinessLicenseTypesByApp = getSupportedBusinessLicenseTypesByApp(merchantId);

        Sets.SetView<Integer> intersection = Sets.intersection(Sets.newHashSet(supportedBusinessLicenseTypesByIndustry), Sets.newHashSet(supportedBusinessLicenseTypesByApp));
        // 证照管理，number对应特定的营业执照类型
        Set<Integer> selectableLicenseTypes = new HashSet<>(intersection);
        Set<Integer> supportedBusinessLicenseTypesByNum = Sets.newHashSet();
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(merchantId, null, false);
        List<BusinessLicenseDicChangeVo> allBusinessLicenses = businessLicenseDicV2Service.allBusinessLicenseDic(null);
        if (Objects.nonNull(licenseInfo) && StringUtils.isNotBlank(licenseInfo.getNumber())
                && (licenseInfo.getNumber().length() == 18
                || (licenseInfo.getNumber().length() == 15
                && StringUtils.equals(StringUtils.substring(licenseInfo.getNumber(), 0, 2), "92")))) {
            // 如果是15位，非三证合一，仅仅92支持读取配置，这里写死是因为非三证合一没有维护在后端，写死在了前端。。。
            String numberPrefix = StringUtils.substring(licenseInfo.getNumber(), 0, 2);
            for (BusinessLicenseDicChangeVo busLic : allBusinessLicenses) {
                typeCodeLoop:
                for (BusinessLicenseDicChangeVo.TypeCodes typeCode : busLic.getType_codes()) {
                    if (StringUtils.equals(numberPrefix, typeCode.getCode())) {
                        supportedBusinessLicenseTypesByNum.add(busLic.getId());
                        break typeCodeLoop;
                    }
                }
            }
            // 第一版本上线证照配置不会修改 92 配置，所以写死。
            if (StringUtils.equals("92", numberPrefix)) {
                supportedBusinessLicenseTypesByNum.add(BusinessLicenseTypeEnum.INDIVIDUAL.getValue());
                supportedBusinessLicenseTypesByNum.add(BusinessLicenseTypeEnum.ENTERPRISE.getValue());
            }
            selectableLicenseTypes = Sets.intersection(supportedBusinessLicenseTypesByNum, selectableLicenseTypes);
        }
        if (CollectionUtils.isEmpty(selectableLicenseTypes)) {
            return Maps.newHashMap();
        }
        Set<Integer> finalSelectableLicenseTypes = selectableLicenseTypes;
        return allBusinessLicenses.stream().filter(
                r -> finalSelectableLicenseTypes.contains(r.getId()) &&
                        !BusinessLicenseTypeEnum.isMicro(r.getId()))
                .collect(Collectors.toMap(BusinessLicenseDicChangeVo::getId, BusinessLicenseDicChangeVo::getName));
    }

    /**
     * 校验营业执照编号和所选的营业执照类型是否匹配
     *
     * @param req 请求dto
     * @return 校验结果
     */
    @Override
    public CommonResultResp isLicenseTypeMatchNumber(BusinessLicenseTypeNumReqDTO req) {
        Integer licenseType = req.getLicenseType();
        String number = req.getNumber();
        return doCheckLicenseTypeMatchNumber(number, licenseType);
    }

    private CommonResultResp doCheckLicenseTypeMatchNumber(String number, Integer licenseType) {
        List<BusinessLicenseDicChangeVo> allBusinessLicenses = businessLicenseDicV2Service.allBusinessLicenseDic(null);
        if (number.length() == 15
                && !StringUtils.equals(StringUtils.substring(number, 0, 2), "92")) {
            return CommonResultResp.SUCCESS();
        }
        for (BusinessLicenseDicChangeVo businessLicenseDicChangeVo : allBusinessLicenses) {
            if (businessLicenseDicChangeVo.getId().equals(licenseType)) {
                for (BusinessLicenseDicChangeVo.TypeCodes typeCode : businessLicenseDicChangeVo.getType_codes()) {
                    if (StringUtils.equals(StringUtils.substring(number, 0, 2), typeCode.getCode())) {
                        return CommonResultResp.SUCCESS();
                    }
                }
            }
        }
        // 目前证照配置还没有配置，暂时写死
        if (StringUtils.equals(StringUtils.substring(number, 0, 2), "92") && Objects.equals(licenseType, BusinessLicenseTypeEnum.ENTERPRISE.getValue())) {
            return CommonResultResp.SUCCESS();
        }
        return CommonResultResp.FAIL("营业执照类型和营业执照编号不匹配");
    }

    private Set<Integer> getSupportedBusinessLicenseTypesByApp(String merchantId) {
        List<Integer> paymentAppIds = getCuaPaymentAppIds(merchantId);
        if (CollectionUtils.isEmpty(paymentAppIds)) {
            return Sets.newHashSet();
        }
        Set<Long> supportedBusinessLicenseTypesByApp = bankPayBusinessConfigurationService.getSupportedBusinessLicenseTypesByPaymentApps(paymentAppIds);
        return supportedBusinessLicenseTypesByApp.stream().map(Long::intValue).collect(Collectors.toSet());
    }

    @Autowired
    private CommonAppConfigService commonAppConfigService;

    private List<Integer> getCuaPaymentAppIds(String merchantId) {
        //        ListResult successAppInfos = commonAppInfoService.findAppInfos(new PageInfo(1, 200),
        //                CollectionUtil.hashMap("merchant_id", merchantId,
        //                        "status", AppInfoModel.STATUS_SUCCESS, "open_way", BusinessLicenseUpgradeValidator.MERCHANT_DIMENSION));
        //        List<String> paymentAppIds = Lists.newArrayList();
        //        if (Objects.nonNull(successAppInfos) && CollectionUtils.isNotEmpty(successAppInfos.getRecords())) {
        //            successAppInfos.getRecords().forEach(record -> paymentAppIds.add(MapUtils.getString(record, "app_id")));
        //        }
        //        List<Integer> appIdKeys = Lists.newArrayList(1);
        //        for (String appId : paymentAppIds) {
        //            Map appConfig = commonAppConfigService.getAppConfigByAppId(appId);
        //            if (MapUtils.isEmpty(appConfig)) {
        //                continue;
        //            }
        //        }
        //        return appIdKeys;
        // 目前cua-bank_info_service的配置 0-间连扫码
        return Lists.newArrayList(0);
    }

    @NotNull
    private List<Integer> getSupportedBusinessLicenseTypesByIndustry(String merchantId) {
        MerchantInfo merchant = getMerchant(merchantId);
        String industryId = merchant.getIndustry();
        HashMap<Object, Object> queryMap = Maps.newHashMap();
        queryMap.put("industry_id", industryId);
        List<Map> industryBusinessLicenseTypes = appBankInfoService.getIndustryBusinessLicenseTypes(queryMap);
        List<Integer> supportedBusinessLicenseTypesByIndustry = Lists.newArrayList();
        if (CollectionUtils.isEmpty(industryBusinessLicenseTypes)) {
            return supportedBusinessLicenseTypesByIndustry;
        }
        industryBusinessLicenseTypes.forEach(map -> {
            Integer type = MapUtils.getInteger(map, "id");
            if (Objects.nonNull(type)) {
                supportedBusinessLicenseTypesByIndustry.add(type);
            }
        });
        return supportedBusinessLicenseTypesByIndustry;
    }

    @Resource
    private CommonApolloConfigs commonApolloConfigs;

    /**
     * 商户可选的法人证件类型
     * 执行事务合伙人硬编码，因为目前兼容老的逻辑
     *
     * @param req 请求dto
     * @return 可选的法人身份证类型
     */
    @Override
    public Map<Integer, String> listSelectableLegalPersonCertificateType(BusinessLicenseTypeReqDTO req) {
        Integer licenseType = req.getLicenseType();
        IdParam idParam = new IdParam();
        idParam.setId(licenseType);
        BusinessLicenseDicChangeVo businessLicenseDicChangeVo = businessLicenseDicV2Service.businessLicenseDicDetail(idParam);
        if (Objects.nonNull(businessLicenseDicChangeVo) && CollectionUtils.isNotEmpty(businessLicenseDicChangeVo.getLegal_person_types())) {
            List<Integer> legalPersonTypes = businessLicenseDicChangeVo.getLegal_person_types();
            Map<Integer, String> legalPersonCertificateType = legalPersonTypes.stream().collect(Collectors.toMap(Function.identity(),
                    type -> EnumUtils.getEnum(IdentificationTypeEnum.class, type).getText()));
            MerchantInfo merchantInfo = getMerchant(req.getMerchantId());
            String merchantAcquirer = acquirerService.getMerchantAcquirer(merchantInfo.getSn());
            if (supportExecutePartner(merchantInfo, merchantAcquirer) && commonApolloConfigs.isSupportExecutePartner()) {
                legalPersonCertificateType.put(IdentificationTypeEnum.EXECUTIVE_PARTNER.getValue(), IdentificationTypeEnum.EXECUTIVE_PARTNER.getText());
            }
            if (supportForeignerPermanent(merchantInfo, merchantAcquirer)) {
                legalPersonCertificateType.put(IdentificationTypeEnum.FOREIGNER_PERMANENT_RESIDENCE_ID_CARD.getValue(), IdentificationTypeEnum.FOREIGNER_PERMANENT_RESIDENCE_ID_CARD.getText());
            }
            return legalPersonCertificateType;
        }
        return Collections.emptyMap();
    }

    @Autowired
    private IdentificationCertificateManagementService identificationCertificateManagementService;

    /**
     * 商户可选的结算账户证件类型
     *
     * @param appCommonFieldDTO 请求dto
     * @return 可选的结算账户证件类型 1-中华人民共和国身份证 2-港澳居民往来内地通行证 3-台湾居民往来内地通行证 4-非中华人民共和国护照 5-中华人民共和国护照 6-港澳居民居住证 7-台湾居民居住证
     */
    @Override
    public Map<Integer, String> listSelectableSettlementAccountCertificateType(AppCommonFieldDTO appCommonFieldDTO) {
        return identificationCertificateManagementService
                .listAllIdentificationCertificateManagement(null)
                .stream().filter(IdentificationCertificateManagementDTO::isEnable)
                .collect(Collectors.toMap(IdentificationCertificateManagementDTO::getCertificateTypeCode,
        IdentificationCertificateManagementDTO::getCertificateName));
    }

    /**
     * 外国人永居身份证  lkl haike 富友 通联收银宝 支持
     */
    private boolean supportForeignerPermanent(MerchantInfo merchantInfo, String merchantAcquirer) {
        return StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.LKL.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.LKL_V3.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.HAI_KE.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.FU_YOU.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.TONG_LIAN_V2.getValue());
    }


    /**
     * 只有haike lkl，富友小微 支持  别的都不支持
     */
    private boolean supportExecutePartner(MerchantInfo merchantInfo, String merchantAcquirer) {
        boolean micro = merchantBiz.isMicro(merchantInfo.getId());
        if (StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.LKL.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.LKL_V3.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.HAI_KE.getValue())) {
            return true;
        }
        if (micro && StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.FU_YOU.getValue())) {
            return true;
        }
        return false;
    }

    /**
     * 商户可选的结算账户类型
     * 商户已经开通的应用支持的结算账户类型 取交集
     * 执行事务合伙人证件类型，不支持法人代表个人账户
     * 法人对私 - 法人代表个人账户
     *
     * @param req 请求dto
     * @return 商户可选的结算账户类型 可选的结算账户类型 1-法人代表个人账户 2-对公企业账户 3-其它个人账户 4-授权对公企业账户
     */
    @Override
    public Map<Integer, String> listSelectableSettlementAccountType(BusinessLicenseTypeReqDTO req) {
        List<Integer> paymentAppIds = getCuaPaymentAppIds(req.getMerchantId());
        Integer licenseType = req.getLicenseType();
        BusinessLicenseId businessLicenseId = new BusinessLicenseId();
        businessLicenseId.setBusinessLicenseId(Long.valueOf(licenseType));
        Set<Integer> types = bankPayBusinessConfigurationService.listSupportedSettlementAccountTypesByLicenseTypeAndApps(licenseType, paymentAppIds);
        if (CollectionUtils.isEmpty(types)) {
            return Maps.newHashMap();
        }
        // 只有拉卡拉支持授权对公
        MerchantInfo merchant = getMerchant(req.getMerchantId());
        String merchantAcquirer = acquirerService.getMerchantAcquirer(merchant.getSn());
        if (!(StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.LKL.getValue())
                || StringUtils.equals(merchantAcquirer, AcquirerTypeEnum.LKL_V3.getValue()))) {
            types.remove(LicenseSettlementAccountTypeEnum.AUTHORIZED_CORPORATE.getValue());
        }
        Integer legalPersonCertificateType = req.getLegalPersonCertificateType();
        if (Objects.equals(legalPersonCertificateType, IdentificationTypeEnum.EXECUTIVE_PARTNER.getValue())) {
            types.remove(LicenseSettlementAccountTypeEnum.LEGAL_PERSONAL.getValue());
        }
        return types.stream().collect(Collectors.toMap(Function.identity(),
                t -> EnumUtils.getEnum(LicenseSettlementAccountTypeEnum.class, t).getText()));
    }

    /**
     * 商户是否需要确认结算信息
     * 如改动了营业执照名称、法人姓名、法人证件号（新认证、变更场景判断规则一致）
     *
     * @param req 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp isMerchantNeedVerifySettlementAccount(BusinessLicenseAuditApplyReqDTO req) {
        return doSettlementAccountNeedVerify(req.getMerchantId(), req.getBusinessLicense());
    }

    private CommonResultResp doSettlementAccountNeedVerify(String merchantId, BusinessLicenseDTO businessLicenseDTO) {
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseBiz.getMerchantBusinessLicenseByMerchantId(merchantId, null, true);
        if (Objects.isNull(licenseInfo) || Objects.equals(licenseInfo.getType(), BusinessLicenseTypeEnum.MICRO.getValue())) {
            return CommonResultResp.SUCCESS("您已完成证照信息填写，为避免您的结算受影响，请确认结算信息是否准确");
        }
        if (Objects.isNull(businessLicenseDTO)) {
            return CommonResultResp.FAIL("营业执照信息为空");
        }
        List<String> changedFields = new ArrayList<>();
        if (!StringUtils.equals(licenseInfo.getName(), businessLicenseDTO.getName())) {
            changedFields.add("证照名称");
        }
        if (!StringUtils.equals(licenseInfo.getLegal_person_id_number(), businessLicenseDTO.getLegalPersonCertificateNumber())) {
            changedFields.add("法人证件号");
        }
        if (!StringUtils.equals(licenseInfo.getLegal_person_name(), businessLicenseDTO.getLegalPersonName())) {
            changedFields.add("法人姓名");
        }
        if (CollectionUtils.isNotEmpty(changedFields)) {
            String changedFieldsStr = String.join("、", changedFields);
            return CommonResultResp.SUCCESS("检测到您修改了" + changedFieldsStr + "，为避免您的结算受影响，请确认结算信息是否准确");
        }
        return CommonResultResp.FAIL("不需要确认结算信息");
    }

    @Override
    public CheckBankAccountResp checkoutAllowChangeCard(BusinessLicenseAuditApplyReqDTO req) {
        CheckBankAccountResp checkBankAccountResp = bankAccountBiz.checkoutAllowChangeCard(req);
        ErrorMsgViewEndpointTypeEnum typeEnum = req.fromAppOrMsp() ? ErrorMsgViewEndpointTypeEnum.APP :
                ErrorMsgViewEndpointTypeEnum.CRM;
        checkBankAccountResp.setResult(businessLicenceUpgradeValidator.getUpgradeCheckPromptMessage(typeEnum, checkBankAccountResp.getResult()));
        return checkBankAccountResp;
    }

    @Override
    public Map getCurrentBankCardData(BusinessLicenseAuditApplyReqDTO req) {
        Map map = bankAccountBiz.addBankCardExtraParams(req);
        BusinessLicenseTypeReqDTO businessLicenseTypeReqDTO = new BusinessLicenseTypeReqDTO();
        businessLicenseTypeReqDTO.setMerchantId(req.getMerchantId());
        businessLicenseTypeReqDTO.setLegalPersonCertificateType(req.getBusinessLicense().getLegalPersonCertificateType());
        businessLicenseTypeReqDTO.setLicenseType(req.getBusinessLicense().getType());
        Map<Integer, String> selectTypes = listSelectableSettlementAccountType(businessLicenseTypeReqDTO);
        if (MapUtils.isEmpty(selectTypes) || !selectTypes.containsKey(MapUtils.getInteger(map, LicenseSettlementAccountExtraParams.SETTLEMENT_ACCOUNT_TYPE))) {
            map.put(LicenseSettlementAccountExtraParams.SETTLEMENT_ACCOUNT_TYPE, null);
        }
        return map;
    }

    /**
     * 获取营业执照编辑草稿
     *
     * @param appCommonFieldDTO 请求dto
     * @return 编辑草稿
     */
    @Override
    public BusinessLicenseAuditEditDraftDTO getBusinessLicenseEditDraft(AppCommonFieldDTO appCommonFieldDTO) {
        String merchantId = appCommonFieldDTO.getMerchantId();
        BusinessLicenseEditDraft businessLicenseEditDraft = businessLicenseEditDraftDAO.getByMerchantSn(getMerchantSn(merchantId));
        if (Objects.nonNull(businessLicenseEditDraft)) {
            BusinessLicenseAuditEditDraftDTO businessLicenseAuditEditDraftDTO = businessLicenseEditDraft.convertDraftJsonToDTO();
            PhotoProcessor.sign(businessLicenseAuditEditDraftDTO);
            return businessLicenseAuditEditDraftDTO;
        }
        return null;
    }


    /**
     * 删除营业执照编辑草稿
     *
     * @param appCommonFieldDTO 请求dto
     */
    @Override
    public void deleteBusinessLicenseEditDraft(AppCommonFieldDTO appCommonFieldDTO) {
        String merchantId = appCommonFieldDTO.getMerchantId();
        businessLicenseEditDraftDAO.deleteByMerchantSn(getMerchantSn(merchantId));
    }

    /**
     * 保存营业执照编辑草稿
     * 需要校验token一致
     *
     * @param req 请求dto
     * @return 保存结果
     */
    @Override
    public SaveBusinessLicenseDraftResultRspDTO saveBusinessLicenseEditDraft(BusinessLicenseAuditEditDraftReqDTO req) {
        try {
            ErrorMsgViewEndpointTypeEnum typeEnum = req.fromAppOrMsp() ? ErrorMsgViewEndpointTypeEnum.APP :
                    ErrorMsgViewEndpointTypeEnum.CRM;
            BankAccountDTO bankAccount = req.getBankAccount();
            if (Objects.nonNull(bankAccount) && Objects.equals(bankAccount.getStatus(), BankAccountDTO.STATUS_CONFIRMED)) {
                CommonResultResp checkAccountRsp = checkBankAccountDraft(bankAccount);
                if (!checkAccountRsp.getResult()) {
                    return SaveBusinessLicenseDraftResultRspDTO.CHECK_FAIL(businessLicenceUpgradeValidator.getUpgradeCheckPromptMessage(typeEnum, checkAccountRsp.getMessage()));
                }
            }
            if (Objects.nonNull(req.getBusinessLicense()) && Objects.equals(req.getBusinessLicense().getStatus(), BusinessLicenseDTO.STATUS_CONFIRMED)) {
                CommonResultResp checkLicenseRsp = checkLicenseDraft(req.getMerchantId(), req.getBusinessLicense());
                if (!checkLicenseRsp.getResult()) {
                    return SaveBusinessLicenseDraftResultRspDTO.CHECK_FAIL(businessLicenceUpgradeValidator.getUpgradeCheckPromptMessage(typeEnum, checkLicenseRsp.getMessage()));
                }
            }
            SaveBusinessLicenseDraftResultRspDTO saveBusinessLicenseDraftResultRspDTO = doSaveBusinessLicenseEditDraft(req);
            // 需要警告，是否需要去确认结算信息
            if (Objects.nonNull(req.getBusinessLicense()) && Objects.equals(req.getBusinessLicense().getStatus(), BusinessLicenseDTO.STATUS_CONFIRMED)) {
                CommonResultResp commonResultResp = doSettlementAccountNeedVerify(req.getMerchantId(), req.getBusinessLicense());
                if (commonResultResp.getResult()) {
                    return SaveBusinessLicenseDraftResultRspDTO.WARN(commonResultResp.getMessage());
                }
            }
            return saveBusinessLicenseDraftResultRspDTO;
        } catch (Exception e) {
            log.error("保存营业执照编辑草稿失败, merchantId:{}",  req.getMerchantId(), e);
            return SaveBusinessLicenseDraftResultRspDTO.SYSTEM_ERROR();
        }
    }

    private boolean checkExistedBusinessNameAudit(String merchantId) {
        GetFieldAppStatusReq getFieldAppStatusReq = new GetFieldAppStatusReq();
        getFieldAppStatusReq.setMerchantId(merchantId);
        getFieldAppStatusReq.setFieldType("business_name");
        GetFieldAppStatusResp fieldAppStatus = commonFieldService.getFieldAppStatus(getFieldAppStatusReq);
        return Objects.nonNull(fieldAppStatus) && (
                Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_INIT) ||
                        Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_PENDING) ||
                        Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_FAIL));
    }

    private CommonResultResp checkLicenseDraft(String merchantId, BusinessLicenseDTO businessLicense) {
        // 存在进行中的商户经营名称变更任务，待经营名称审核通过后再提交证照变更
        if (checkExistedBusinessNameAudit(merchantId)) {
            return CommonResultResp.FAIL("您的商户经营名称正在审核中，待经营名称审核通过后再进行证照变更");
        }
        // 92 校验公司名称
        if (BusinessLicenseTypeEnum.ENTERPRISE.getValue().equals(businessLicense.getType())
                && businessLicense.getNumber().startsWith("92")) {
            if (StringUtils.isBlank(businessLicense.getName()) || !businessLicense.getName().contains("公司")) {
                return CommonResultResp.FAIL("企业类型营业执照，名称必须包含'公司'字样");
            }
        }
        return CommonResultResp.SUCCESS();
    }

    private CommonResultResp checkBankAccountDraft(BankAccountDTO bankAccount) {
        return CommonResultResp.SUCCESS();
    }

    /**
     * 保存营业执照编辑草稿
     * 如果已存在的token和请求的token都来自商户端（APP或MSP），则不校验token是否一致
     * 如果已存在的token和请求的token都来自销售端（CRM_APP或CRM_WEB），则不校验token是否一致
     * 其他情况下，需要校验token是否一致
     *
     * @param req 请求参数
     * @return 保存结果
     */
    @NotNull
    private SaveBusinessLicenseDraftResultRspDTO doSaveBusinessLicenseEditDraft(BusinessLicenseAuditEditDraftReqDTO req) {
        PhotoProcessor.removeSign(req);
        String merchantSn = getMerchantSn(req.getMerchantId());
        Optional<EditLicenseTokenBO> editLicenseTokenBO = getBusinessLicenseEditTokenFromRedis(merchantSn);
        if (!editLicenseTokenBO.isPresent()) {
            return SaveBusinessLicenseDraftResultRspDTO.tokenExpired();
        }
        // 如果token相同，直接允许保存
        String existingToken = editLicenseTokenBO.get().getToken();
        if (StringUtils.equals(existingToken, req.getToken())) {
            return saveBusinessLicenseEditDraftInternal(req, merchantSn);
        }
        EditLicenseTokenBO existingTokenBO = editLicenseTokenBO.get();
        // 如果已存在的token和请求的token都来自同一端（商户端或销售端），则不校验token是否一致
        if (isFromSameSide(existingTokenBO, req.getPlatform())) {
            setBusinessLicenseEditTokenToRedis(merchantSn, req.getToken(), req.getPlatform());
            return saveBusinessLicenseEditDraftInternal(req, merchantSn);
        }
        return SaveBusinessLicenseDraftResultRspDTO.tokenChanged();
    }

    /**
     * 实际保存营业执照编辑草稿的内部方法
     *
     * @param req 请求参数
     * @param merchantSn 商户编号
     * @return 保存结果
     */
    private SaveBusinessLicenseDraftResultRspDTO saveBusinessLicenseEditDraftInternal(BusinessLicenseAuditEditDraftReqDTO req, String merchantSn) {
        BusinessLicenseEditDraft businessLicenseEditDraft = businessLicenseEditDraftDAO.getByMerchantSn(merchantSn);
        if (Objects.isNull(businessLicenseEditDraft)) {
            businessLicenseEditDraft = new BusinessLicenseEditDraft();
            businessLicenseEditDraft.setMerchantSn(merchantSn);
            businessLicenseEditDraft.setOperator(req.getUserId());
            businessLicenseEditDraft.setDraft(JSON.toJSONString(new BusinessLicenseAuditEditDraftDTO(req.getBusinessLicense(), req.getBankAccount())));
            businessLicenseEditDraftDAO.insertSelective(businessLicenseEditDraft);
        } else {
            BusinessLicenseAuditEditDraftDTO businessLicenseAuditEditDraftDTO = JSON.parseObject(businessLicenseEditDraft.getDraft(), BusinessLicenseAuditEditDraftDTO.class);
            if (Objects.nonNull(businessLicenseAuditEditDraftDTO)) {
                if (Objects.isNull(req.getBusinessLicense())) {
                    req.setBusinessLicense(businessLicenseAuditEditDraftDTO.getBusinessLicense());
                }
                if (Objects.isNull(req.getBankAccount())) {
                    req.setBankAccount(businessLicenseAuditEditDraftDTO.getBankAccount());
                }
            }
            businessLicenseEditDraft.setMtime(new Timestamp(System.currentTimeMillis()));
            businessLicenseEditDraft.setDraft(JSON.toJSONString(new BusinessLicenseAuditEditDraftDTO(req.getBusinessLicense(), req.getBankAccount())));
            businessLicenseEditDraftDAO.updateByPrimaryKeySelective(businessLicenseEditDraft);
        }
        return SaveBusinessLicenseDraftResultRspDTO.success();
    }

    /**
     * 申请编辑营业执照token
     * 如果token已存在但来自同一端（商户端或销售端），则重新生成token并返回
     * 如果token已存在且来自不同端，则返回token已存在的错误
     *
     * @param req 请求dto
     * @return 申请结果
     */
    @Override
    public ApplyEditBusinessLicenseTokenResultRspDTO applyEditBusinessLicenseToken(BusinessLicenseApplyTokenReqDTO req) {
        String merchantSn = getMerchantSn(req.getMerchantId());
        boolean locked = false;
        try {
            locked = redisLock.lock(merchantSn, LICENSE_EDIT_LOCK_KEY + merchantSn, 3);
            if (!locked) {
                return ApplyEditBusinessLicenseTokenResultRspDTO.systemError("正在申请token，请稍后再试");
            }

            Optional<EditLicenseTokenBO> existedMerchantToken = getBusinessLicenseEditTokenFromRedis(merchantSn);
            if (existedMerchantToken.isPresent() && (Objects.isNull(req.getForce()) || !req.getForce())) {
                EditLicenseTokenBO existingTokenBO = existedMerchantToken.get();
                if (isFromSameSide(existingTokenBO, req.getPlatform())) {
                    String token = generateEditLicenseToken();
                    setBusinessLicenseEditTokenToRedis(merchantSn, token, req.getPlatform());
                    return ApplyEditBusinessLicenseTokenResultRspDTO.SUCCESS(token);
                }
                EditLicenseTokenBO requestTokenBO = new EditLicenseTokenBO(null, null, req.getPlatform());
                String errorMessage;
                if (requestTokenBO.fromMerchant() && existingTokenBO.fromSales()) {
                    errorMessage = "您的客户经理正在对您提交的证照信息进行核实校准，若您需要自行修改证照信息，可点击【自行编辑】进行操作";
                }
                else if (requestTokenBO.fromSales() && existingTokenBO.fromMerchant()) {
                    errorMessage = "商户正在编辑营业证照信息，如需辅助商户更正信息，提醒商户保存草稿后，点击【确认编辑】替换";
                }
                else {
                    errorMessage = "其他端正在编辑营业执照";
                }
                return ApplyEditBusinessLicenseTokenResultRspDTO.tokenExisted(errorMessage);
            }
            // 无已存在token或force=true，生成新token
            String token = generateEditLicenseToken();
            setBusinessLicenseEditTokenToRedis(merchantSn, token, req.getPlatform());
            return ApplyEditBusinessLicenseTokenResultRspDTO.SUCCESS(token);
        } catch (Exception e) {
            log.error("applyEditBusinessLicenseToken error, merchantSn:{}", merchantSn, e);
            return ApplyEditBusinessLicenseTokenResultRspDTO.systemError(e.getMessage());
        } finally {
            if (locked) {
                redisLock.unlock(merchantSn, LICENSE_EDIT_LOCK_KEY + merchantSn);
            }
        }
    }

    /**
     * 验证token是否有效
     * 如果已存在的token和请求的token都来自商户端（APP或MSP），则不校验token是否一致
     * 如果已存在的token和请求的token都来自销售端（CRM_APP或CRM_WEB），则不校验token是否一致
     * 其他情况下，需要校验token是否一致
     *
     * @param req req
     * @return 验证结果
     */
    @Override
    public CheckEditBusinessLicenseTokenResultRspDTO checkEditBusinessLicenseTokenValid(BusLicEditHeartbeatDetectionReqDTO req) {
        String merchantSn = getMerchantSn(req.getMerchantId());
        Optional<EditLicenseTokenBO> existedMerchantToken = getBusinessLicenseEditTokenFromRedis(merchantSn);
        if (!existedMerchantToken.isPresent()) {
            return CheckEditBusinessLicenseTokenResultRspDTO.tokenExpired();
        }

        String existingToken = existedMerchantToken.get().getToken();
        if (StringUtils.equals(existingToken, req.getToken())) {
            return CheckEditBusinessLicenseTokenResultRspDTO.tokenValid();
        }
        EditLicenseTokenBO existingTokenBO = existedMerchantToken.get();
        // 如果已存在的token和请求的token都来自同一端（商户端或销售端），则不校验token是否一致
        if (isFromSameSide(existingTokenBO, req.getPlatform())) {
            setBusinessLicenseEditTokenToRedis(merchantSn, req.getToken(), req.getPlatform());
            return CheckEditBusinessLicenseTokenResultRspDTO.tokenValid();
        }
        return CheckEditBusinessLicenseTokenResultRspDTO.tokenChanged();
    }

    private Optional<EditLicenseTokenBO> getBusinessLicenseEditTokenFromRedis(String merchantSn) {
        String tokenString = stringRedisTemplate.opsForValue().get(LICENSE_EDIT_TOKEN_KEY + merchantSn);
        if (StringUtils.isBlank(tokenString)) {
            return Optional.empty();
        }
        return Optional.of(JSON.parseObject(tokenString, EditLicenseTokenBO.class));
    }


    private void setBusinessLicenseEditTokenToRedis(String merchantSn, String token) {
        setBusinessLicenseEditTokenToRedis(merchantSn, token, null);
    }

    /**
     * 生成营业执照编辑token
     * 当前实现使用UUID，后续可以改造为其他生成方式
     *
     * @return 生成的token字符串
     */
    private String generateEditLicenseToken() {
        return UUID.randomUUID().toString();
    }

    /**
     * 判断两个token是否来自同一端（商户端或销售端）
     * 如果都来自商户端或都来自销售端，则返回true
     *
     * @param existingTokenBO 已存在的token对象
     * @param requestPlatform 请求的平台标识
     * @return 是否来自同一端
     */
    private boolean isFromSameSide(EditLicenseTokenBO existingTokenBO, String requestPlatform) {
        EditLicenseTokenBO requestTokenBO = new EditLicenseTokenBO(null, null, requestPlatform);
        boolean bothFromMerchant = existingTokenBO.fromMerchant() && requestTokenBO.fromMerchant();
        boolean bothFromSales = existingTokenBO.fromSales() && requestTokenBO.fromSales();
        return bothFromMerchant || bothFromSales;
    }

    /**
     * 将token保存到Redis中，并包含平台信息
     *
     * @param merchantSn 商户编号
     * @param token token值
     * @param platform 平台标识（APP/CRM_APP/CRM_WEB/MSP）
     */
    private void setBusinessLicenseEditTokenToRedis(String merchantSn, String token, String platform) {
        EditLicenseTokenBO editLicenseTokenBO = new EditLicenseTokenBO(token, new Timestamp(System.currentTimeMillis()), platform);
        stringRedisTemplate.opsForValue()
                .set(LICENSE_EDIT_TOKEN_KEY + merchantSn, JSON.toJSONString(editLicenseTokenBO), 10, TimeUnit.MINUTES);
    }


    private void updateBusinessLicenseEditTokenCreateTime(String merchantSn, String token) {
        Optional<EditLicenseTokenBO> editLicenseTokenBO = getBusinessLicenseEditTokenFromRedis(merchantSn);
        if (editLicenseTokenBO.isPresent() && StringUtils.equals(editLicenseTokenBO.get().getToken(), token)) {
            setBusinessLicenseEditTokenToRedis(merchantSn, token, editLicenseTokenBO.get().getPlatform());
            return;
        }
        throw new McException("token不存在");
    }


    /**
     * 营业执照编辑心跳检测
     *
     * @param reqDTO 请求dto
     * @return 心跳检测结果
     */
    @Override
    public CommonResultResp busLicEditHeartbeatDetection(BusLicEditHeartbeatDetectionReqDTO reqDTO) {
        String merchantSn = getMerchantSn(reqDTO.getMerchantId());
        try {
            updateBusinessLicenseEditTokenCreateTime(merchantSn, reqDTO.getToken());
            return CommonResultResp.SUCCESS();
        } catch (Exception e) {
            log.warn("busLicEditHeartbeatDetection fail, req:{}", JSON.toJSONString(reqDTO), e);
            return CommonResultResp.FAIL(e.getMessage());
        }
    }


    /**
     * 退出营业执照编辑
     *
     * @param reqDTO 请求dto
     * @return 退出结果
     */
    @Override
    public CommonResultResp exitBusinessLicenseEdit(BusLicEditHeartbeatDetectionReqDTO reqDTO) {
        Optional<EditLicenseTokenBO> editLicenseTokenBO = getBusinessLicenseEditTokenFromRedis(getMerchantSn(reqDTO.getMerchantId()));
        if (editLicenseTokenBO.isPresent() && StringUtils.equals(editLicenseTokenBO.get().getToken(), reqDTO.getToken())) {
            stringRedisTemplate.delete(LICENSE_EDIT_TOKEN_KEY + getMerchantSn(reqDTO.getMerchantId()));
            return CommonResultResp.SUCCESS();
        }
        return CommonResultResp.FAIL("token不存在");
    }

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;


    /**
     * 根据营业执照申请单保存或者更新营业执照
     * 仅仅配置给crm平台使用
     *
     * @param req 请求dto
     * @return 保存结果
     */
    @Override
    public CommonResultResp saveOrUpdateBusinessLicenseByApply(BusinessLicenseDTO req, LogParamsDto logParamsDto) {
        try {
            MerchantBusinessLicenseInfo existedLicenseInfo = merchantBusinessLicenseBiz
                    .getMerchantBusinessLicenseByMerchantId(req.getMerchantId(), null, true);
            Map updateLicense = saveOrUpdateLicense(req, existedLicenseInfo);
            updateBusinessName(req.getMerchantId(), req.getBusinessName(), logParamsDto);
            recordLicenseChangeLog(req, updateLicense, existedLicenseInfo, logParamsDto);
            return CommonResultResp.SUCCESS();
        } catch (Exception e) {
            log.error("根据营业执照申请单保存或者更新营业执照失败, merchantId:{}, req:{}", req.getMerchantId(), req, e);
            return CommonResultResp.FAIL(e.getMessage());
        }
    }



    @Resource
    private LogBiz logBiz;

    public static final String LICENSE_CHANGE_LOG_CODE = "GN0OUWH1VDCK";


    private void recordLicenseChangeLog(BusinessLicenseDTO req, Map updateMap, MerchantBusinessLicenseInfo existedLicenseInfo, LogParamsDto logParamsDto) {
        if (Objects.isNull(logParamsDto)) {
            logParamsDto = new LogParamsDto();
        }
        if (StringUtils.isBlank(logParamsDto.getSceneTemplateCode())) {
            logParamsDto.setSceneTemplateCode(LICENSE_CHANGE_LOG_CODE);
        }
        if (StringUtils.isBlank(logParamsDto.getUserId())) {
            logParamsDto.setUserId("system");
        }
        if (StringUtils.isBlank(logParamsDto.getUserName())) {
            logParamsDto.setUserName("merchant-center");
        }
        if (Objects.isNull(logParamsDto.getLogPlatformEnum())) {
            logParamsDto.setLogPlatformEnum(LogPlatformEnum.CRM);
        }
        try {
            Map oldLicense = JSON.parseObject(JSON.toJSONString(existedLicenseInfo));
            oldLicense = GetRealObject.filterParams(oldLicense, GetRealObject.MERCHANT_BUSINESS_LICENSE_DATA);
            // 新增辅助证明材料字段
            String newAuxiliaryProofMaterials = req.getAuxiliaryProofMaterials();
            String existedAuxiliaryProofMaterials = existedLicenseInfo.exactAuxiliaryProofMaterialsFromExtra();
            updateMap.put(MerchantBusinessLicenseInfo.AUXILIARY_PROOF_MATERIALS_KEY, PhotoUtils.baseUrl(newAuxiliaryProofMaterials));
            oldLicense.put(MerchantBusinessLicenseInfo.AUXILIARY_PROOF_MATERIALS_KEY, PhotoUtils.baseUrl(existedAuxiliaryProofMaterials));
            oldLicense.remove("deleted");
            updateMap.remove("deleted");
            logBiz.saveLogWithLogParamsDto(oldLicense, updateMap, "merchant_business_license",  existedLicenseInfo.getMerchant_id(),null, logParamsDto);
        } catch (Exception e) {
            log.error("记录营业执照变更日志失败", e);
        }

    }

    private void updateBusinessName(String merchantId, String businessName, LogParamsDto logParamsDto) {
        try {
            MerchantInfo merchant = getMerchant(merchantId);
            if (StringUtils.isNotBlank(businessName) && !StringUtils.equals(merchant.getBusiness_name(), businessName)) {
                log.info("营业执照更新场景商户经营名称修改, merchantId:{}, oldBusinessName:{}, newBusinessName:{}", merchantId, businessName, merchant.getBusiness_name());
                UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq();
                updateMerchantReq.setId(merchantId);
                updateMerchantReq.setBusinessName(businessName);
                LogReq logReq = new LogReq();
                logReq.setLogTemplateCode("DGS4K8MPS2PB");
                if (Objects.nonNull(logParamsDto)) {
                    logReq.setOpUserName(logParamsDto.getUserName());
                    logReq.setOpUserId(logParamsDto.getUserId());
                    logReq.setPlatformCode(logParamsDto.getLogPlatformEnum().getCode());
                }
                merchantBiz.updateMerchant(updateMerchantReq, null, logReq);
                // businessLicenceTaskService.updateBusinessNameInLicenseCertificate(merchant.getId(), businessName);
            }
        } catch (Exception e) {
            log.error("营业执照认证-提交经营名称变更失败, merchantId:{}", merchantId, e);
        }
    }

    /**
     * 确保关键字段存在于营业执照Map中
     * 即使这些字段的值为null，也会被显式包含在Map中
     *
     * @param merchantLicense  营业执照Map
     */
    private void ensureRequiredFieldsExist(Map merchantLicense) {
        String[] requiredFields = {
                MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO,
                MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO,
                MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ADDRESS,
                MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ISSUING_AUTHORITY
        };
        for (String field : requiredFields) {
            if (!merchantLicense.containsKey(field)) {
                merchantLicense.put(field, null);
            }
        }
    }

    private Map saveOrUpdateLicense(BusinessLicenseDTO req, MerchantBusinessLicenseInfo existedLicenseInfo) {
        PhotoProcessor.removeSign(req);
        Map requestMap = JSON.parseObject(JSON.toJSONString(req));
        requestMap = GetRealObject.filterParams(requestMap, GetRealObject.MERCHANT_BUSINESS_LICENSE_DATA);
        ensureRequiredFieldsExist(requestMap);
        requestMap.put("verify_status", 2);
        if (Objects.isNull(existedLicenseInfo)) {
            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put(MerchantBusinessLicenseInfo.AUXILIARY_PROOF_MATERIALS_KEY, req.getAuxiliaryProofMaterials());
            extraMap.put(MerchantBusinessLicenseInfo.BUSINESS_LICENSE_VERIFY_V2_KEY, "1");
            requestMap.put("extra", extraMap);
            merchantBusinessLicenseService.save(requestMap);
        } else {
            requestMap.put("id", existedLicenseInfo.getId());
            Map extra = existedLicenseInfo.getExtra();
            Map<String, Object> extraMap = new HashMap<>();
            if (Objects.nonNull(extra)) {
                extraMap.putAll(extra);
            }
            extraMap.put(MerchantBusinessLicenseInfo.AUXILIARY_PROOF_MATERIALS_KEY, req.getAuxiliaryProofMaterials());
            extraMap.put(MerchantBusinessLicenseInfo.BUSINESS_LICENSE_VERIFY_V2_KEY, "1");
            requestMap.put("extra", extraMap);
            merchantBusinessLicenseService.updateMerchantBusinessLicense(requestMap);
        }
        return requestMap;
    }

    /**
     * 获取营业执照认证进度文案
     *
     * @param appCommonFieldDTO 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp showBusinessLicenseCertificationProgressText(AppCommonFieldDTO appCommonFieldDTO) {
        try {
            MerchantInfo merchant = getMerchant(appCommonFieldDTO.getMerchantId());
            String merchantSn = merchant.getSn();
            Boolean existed = jobBusinessLicenceTaskService.hasLicenseUpdateNotFinishedV2Task(merchantSn);
            ErrorMsgViewEndpointTypeEnum typeEnum = appCommonFieldDTO.fromAppOrMsp() ? ErrorMsgViewEndpointTypeEnum.APP :
                    ErrorMsgViewEndpointTypeEnum.CRM;
            String promptMessage;
            if (existed) {
                promptMessage = businessLicenceUpgradeValidator.getUpgradeCheckPromptMessage(typeEnum, "进件审核中");
            } else {
                promptMessage = businessLicenceUpgradeValidator.getUpgradeCheckPromptMessage(typeEnum, "风控审核中");
            }
            return CommonResultResp.SUCCESS(promptMessage);
        } catch (Exception e) {
            log.error("获取审核进度异常, merchantId:{}", appCommonFieldDTO.getMerchantId(), e);
            return CommonResultResp.FAIL("系统异常");
        }
    }

    /**
     * 是否存在带有换卡的营业执照变更任务
     *
     * @param merchantId 商户号
     * @return 校验结果
     */
    public boolean isExistedLicenseUpdateV2ChangeAccountApply(String merchantId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        pageInfo.setPage(1);
        Map<String, Object> queryFilterMap = Maps.newHashMap();
        queryFilterMap.put("merchant_id", merchantId);
        queryFilterMap.put("field_type", "business_license2");
        ListResult fieldAppInfos = commonFieldService.findFieldAppInfos(pageInfo, queryFilterMap);
        if (Objects.isNull(fieldAppInfos)
                || org.apache.commons.collections4.CollectionUtils.isEmpty(fieldAppInfos.getRecords())
                || org.apache.commons.collections.MapUtils.isEmpty(fieldAppInfos.getRecords().get(0))) {
            return false;
        }
        Map fieldAppInfo = fieldAppInfos.getRecords().get(0);
        Integer status = MapUtils.getInteger(fieldAppInfo, "status"); // findFieldAppInfos
        if (!(Objects.equals(CrmApplyConstant.STATUS_INIT, status) || Objects.equals(CrmApplyConstant.STATUS_PENDING, status))) {
            return false;
        }
        if (org.apache.commons.collections.MapUtils.isNotEmpty(fieldAppInfo)
                && org.apache.commons.collections.MapUtils.isNotEmpty(org.apache.commons.collections.MapUtils.getMap(fieldAppInfo, "business_app_info"))) {
            Map businessAppInfo = org.apache.commons.collections.MapUtils.getMap(fieldAppInfo, "business_app_info");
            if (org.apache.commons.collections.MapUtils.isNotEmpty(businessAppInfo) && businessAppInfo.containsKey("account")) {
                return true;
            }
        }
        return false;
    }


    @Resource BankAccountCompareBiz bankAccountCompareBiz;

    /**
     * 校验营业执照申请单
     * 营业执照类型和营业执照编号是否匹配
     * 如果有结算信息，结算信息是否修改
     * 92包含公司校验，
     *
     * @param applyReqDTO 营业执照申请单
     * @return 校验结果
     */
    @Override
    public LicenseApplyCheckResultRsp checkBusinessLicenseAuditApplyReqDTO(BusinessLicenseAuditApplyReqDTO applyReqDTO) {
        try {
            return doCheckBusinessLicenseAuditApply(applyReqDTO);
        } catch (Exception e) {
            log.error("校验营业执照申请单异常, applyReqDTO:{}", applyReqDTO, e);
            return LicenseApplyCheckResultRsp.FAIL("系统异常", LicenseApplyCheckResultRsp.CHECK_FAIL);
        }
    }

    private LicenseApplyCheckResultRsp doCheckBusinessLicenseAuditApply(BusinessLicenseAuditApplyReqDTO applyReqDTO) {
        // 营业执照校验
        BusinessLicenseDTO businessLicense = applyReqDTO.getBusinessLicense();
        if (Objects.isNull(businessLicense)) {
            throw new CommonInvalidParameterException("营业执照信息不能为空");
        }
        // 92
        if (BusinessLicenseTypeEnum.ENTERPRISE.getValue().equals(businessLicense.getType())
                && applyReqDTO.getBusinessLicense().getNumber().startsWith("92")) {
            if (StringUtils.isBlank(businessLicense.getName()) || !businessLicense.getName().contains("公司")) {
                return LicenseApplyCheckResultRsp.COMPANY_92_NOT_MATCH();
            }
        }
        // 营业执照类型和营业执照编号是否匹配
        CommonResultResp commonResultResp = doCheckLicenseTypeMatchNumber(businessLicense.getNumber(), businessLicense.getType());
        if (!commonResultResp.getResult()) {
            return LicenseApplyCheckResultRsp.LICENSE_TYPE_NUMBER_NOT_MATCH();
        }
        // 结算信息校验 如果没有变化，前端需要从审批单中去掉结算信息（此情况前端不用抛出异常）
        // 如果前端入参有变化，改方法需要同步维护
        if (!bankAccountCompareBiz.checkBankAccountIsChange(applyReqDTO)) {
            return LicenseApplyCheckResultRsp.ACCOUNT_NOT_CHANGE();
        }
        return LicenseApplyCheckResultRsp.SUCCESS();
    }


    /**
     * 校验结算信息有没有变更
     *
     * @param req 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp isSettlementInfoChanged(BusinessLicenseAuditApplyReqDTO req) {
        if (Objects.isNull(req)
                || Objects.isNull(req.getBusinessLicense())
                || Objects.isNull(req.getBankAccount())) {
            throw new CommonInvalidParameterException("营业执照信息或结算信息不能为空");
        }
        boolean change = bankAccountCompareBiz.checkBankAccountIsChange(req);
        if (change) {
            return CommonResultResp.SUCCESS("结算信息变更");
        }
        return CommonResultResp.FAIL("结算信息没有变更");
    }

    private void checkNameContainsCompany(String name, String errorMessage) {
        if (StringUtils.isBlank(name) || !name.contains("公司")) {
            throw new ContractBizException(errorMessage);
        }
    }

    @Resource
    private CustomerRelationClient customerRelationClient;

    /**
     * 判断商户是否进入营业执照新页面
     * 先读取v2页面打开开关
     * 判断商户是否已有老的营业执照认证流程
     * 判断是否全量放开
     * 根据灰度组织判断
     *
     * @param merchantId 商户号
     * @return true-进入新页面 false-未进入新页面
     */
    @Override
    public boolean isMerchantInNewBusinessLicensePage(String merchantId) {
        try {
            if (!commonApolloConfigs.isBusinessLicenseV2PageOpen()) {
                return false;
            }
            Optional<Integer> existedProcessingLicenseUpdateApplyType = existedProcessingLicenseUpdateApplyType(merchantId);
            if (existedProcessingLicenseUpdateApplyType.isPresent()) {
                log.info("商户存在进行中的营业执照审批,merchantId:{}, 审批类型:{}", merchantId, Objects.equals(existedProcessingLicenseUpdateApplyType.get(),
                        LICENSE_PROCESSING_APPLY_NEW_TYPE) ? "新流程" : "老流程");
                return Objects.equals(existedProcessingLicenseUpdateApplyType.get(), LICENSE_PROCESSING_APPLY_NEW_TYPE);
            }
            if (commonApolloConfigs.isBusinessLicenseV2PageAllOpen()) {
                return true;
            }
            return isGrayOrgMerchant(merchantId);
        } catch (Exception e) {
            log.error("判断商户是否进入营业执照新页面异常, merchantId:{}", merchantId, e);
            return false;
        }
    }

    private boolean isGrayOrgMerchant(String merchantId) {
        Set<String> grayOrgList = getGrayOrgList();
        if (CollectionUtils.isEmpty(grayOrgList)) {
            return false;
        }
        OrganizationEs mchIndirectOrg = customerRelationClient.getMchIndirectOrg(merchantId);
        if (Objects.isNull(mchIndirectOrg) || StringUtils.isBlank(mchIndirectOrg.getPath())) {
            return false;
        }
        String path = mchIndirectOrg.getPath();
        for (String grayOrg : grayOrgList) {
            if (path.startsWith(grayOrg)) {
                return true;
            }
        }
        return false;
    }

    @Autowired
    private MerchantBusinessLicenseAuditService merchantBusinessLicenseAuditService;

    // 老流程
    public static final int LICENSE_PROCESSING_APPLY_OLD_TYPE = 1;

    // 新流程
    public static final int LICENSE_PROCESSING_APPLY_NEW_TYPE = 2;

    /**
     * 商户存在进行中的营业执照审批类型
     *
     * @param merchantId 商户号
     * @return 审批类型 1-老的审批 2-新的审批
     */
    private Optional<Integer> existedProcessingLicenseUpdateApplyType(String merchantId) {
        Optional<Integer> crmBusinessLicenseApplyStatus = getCrmBusinessLicenseApplyStatus(merchantId);
        if (crmBusinessLicenseApplyStatus.isPresent()
                && Objects.equals(crmBusinessLicenseApplyStatus.get(), BusinessLicenseVerifyStatusEnum.VERIFYING.getValue())) {
            return Optional.of(LICENSE_PROCESSING_APPLY_NEW_TYPE);
        }
        Map businessLicenseAudit = merchantBusinessLicenseAuditService.getRecentAuditByMerchantId(merchantId);
        if (MapUtils.isNotEmpty(businessLicenseAudit) &&
                Objects.equals(MapUtils.getIntValue(businessLicenseAudit, BusinessLicenseAudit.BUSINESS_LICENSE_STATUS), BusinessLicenseAudit.STATUS_PENDING)) {
            log.warn("商户存在进行中老的营业执照认证流程, merchantId:{}", merchantId);
            return Optional.of(LICENSE_PROCESSING_APPLY_OLD_TYPE);
        }
        return Optional.empty();
    }

    // 获取灰度的组织列表
    private Set<String> getGrayOrgList() {
        List<String> businessLicenseV2GrayOrgList = commonApolloConfigs.getBusinessLicenseV2GrayOrgList();
        if (CollectionUtils.isNotEmpty(businessLicenseV2GrayOrgList)) {
            return new HashSet<>(businessLicenseV2GrayOrgList);
        }
        return new HashSet<>();
    }

    /**
     * MSP是否展示营业执照tab栏位
     *
     * @param appCommonFieldDTO
     */
    @Override
    public CommonResultResp isShowMSPBusinessLicenseTab(AppCommonFieldDTO appCommonFieldDTO) {
        // TODO com.wosai.mc.biz.LicenseAuditBiz.licenseModuleInfoFromApp 参考这个方法，如果AUTH_AUDIT_UNPASS未通过或者通道校验未通过，返回false
        return null;
    }
}
