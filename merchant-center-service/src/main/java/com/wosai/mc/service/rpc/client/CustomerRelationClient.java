package com.wosai.mc.service.rpc.client;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import entity.common.OrganizationEs;
import entity.common.PageListResult;
import entity.request.CustomerRelationOriginReq;
import entity.response.CustomerRelationOriginResp;
import enums.CustomerTypeEnum;
import enums.SellerTypeEnum;
import facade.ICustomerRelationFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */
@Component
public class CustomerRelationClient {

    @Autowired
    private ICustomerRelationFacade customerRelationFacade;

    @Value("${crm.indirect.customer_relation}")
    public String indirectCrmCustomerRelation;

    /**
     * 获取商户支付业务bd的组织
     *
     * @param merchantId 商户ID
     * @return 商户间连扫码组织
     */
    public OrganizationEs getMchIndirectOrg(String merchantId) {
        CustomerRelationOriginReq req = new CustomerRelationOriginReq();
        req.setCustomerIds(Collections.singletonList(merchantId));
        req.setBusinessId("25c3032e-6316-493b-be71-53f4dfa06704");
        req.setAllowRelationUpdate(true);
        req.setSellerType(SellerTypeEnum.TYPE_ORGANIZATION);
        req.setCustomerType(CustomerTypeEnum.TYPE_MERCHANT);
        // req.setConfigCodes(Collections.singletonList(indirectCrmCustomerRelation));
        PageListResult<CustomerRelationOriginResp> customerRelationOrigin;
        try {
            customerRelationOrigin = customerRelationFacade.findCustomerRelationOrigin(req);
        } catch (IOException e) {
            throw new ContractBizException("查询商户客户关系异常");
        }
        if (WosaiCollectionUtils.isEmpty(customerRelationOrigin.getRecords())) {
            throw new ContractBizException("查不到商户所属组织");
        }
        return customerRelationOrigin.getRecords().get(0).getOrganization();
    }
}
