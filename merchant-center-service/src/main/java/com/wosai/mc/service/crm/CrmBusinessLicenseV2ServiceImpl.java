package com.wosai.mc.service.crm;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.mc.model.AppCommonFieldDTO;
import com.wosai.mc.model.CommonResultResp;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.dto.req.*;
import com.wosai.mc.model.dto.rsp.ApplyEditBusinessLicenseTokenResultRspDTO;
import com.wosai.mc.model.dto.rsp.BusinessLicenseAuditEditDraftDTO;
import com.wosai.mc.model.dto.rsp.CheckEditBusinessLicenseTokenResultRspDTO;
import com.wosai.mc.model.dto.rsp.SaveBusinessLicenseDraftResultRspDTO;
import com.wosai.mc.model.resp.CheckBankAccountResp;
import com.wosai.mc.model.resp.LicenseApplyCheckResultRsp;
import com.wosai.mc.service.BusinessLicenseV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * crm营业执照v2服务
 * TODO 待优化，如何一个接口兼容crmApp和收钱吧app
 *
 * <AUTHOR>
 * @date 2025/2/7 15:51
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class CrmBusinessLicenseV2ServiceImpl implements CrmBusinessLicenseV2Service{

    @Resource
    private BusinessLicenseV2Service businessLicenseV2Service;


    private <T extends AppCommonFieldDTO> T getCommonFieldDTOByCrmReq(ApiRequestParam<T, Map<String, Object>> req) {
        T bodyParams = req.getBodyParams();
        bodyParams.setPlatform(AppCommonFieldDTO.PLATFORM_CRM_APP);
        bodyParams.setUserId(req.getUser().getId());
        return bodyParams;
    }

    /**
     * 获取加密后的商户营业执照信息
     *
     * @param req 请求dto
     * @return 加密后的商户营业执照信息
     */
    @Override
    public MerchantBusinessLicenseInfo getBusinessLicense(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.getBusinessLicense(getCommonFieldDTOByCrmReq(req));
    }


    /**
     * 获取商户营业执照认证状态
     *
     * @param req 请求dto
     * @return 认证状态 0-未知  1-未认证  2-认证成功  3-认证失败  4-认证中
     */
    @Override
    public Integer getLicenseCertificationStatus(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.getLicenseCertificationStatus(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 判断商户是否可以进行营业执照认证
     *
     * @param req 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp isMerchantAbleForLicenseCertify(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.isMerchantAbleForLicenseCertify(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 商户可选的营业执照类型
     *
     * @param req 请求dto
     * @return 可选的营业执照类型
     */
    @Override
    public Map<Integer, String> listSelectableLicenseType(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.listSelectableLicenseType(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 校验营业执照编号和所选的营业执照类型是否匹配
     *
     * @param req 请求dto
     * @return 校验结果
     */
    @Override
    public CommonResultResp isLicenseTypeMatchNumber(ApiRequestParam<BusinessLicenseTypeNumReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.isLicenseTypeMatchNumber(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 商户可选的法人证件类型
     *
     * @param req 请求dto
     * @return 身份证类型 1-中华人民共和国身份证 2-港澳居民往来内地通行证 3-台湾居民往来内地通行证  4-非中华人民共和国护照 5-中华人民共和国护照 6-港澳居民居住证 7-台湾居民居住证 8-执行事务合伙人
     */
    @Override
    public Map<Integer, String> listSelectableLegalPersonCertificateType(ApiRequestParam<BusinessLicenseTypeReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.listSelectableLegalPersonCertificateType(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 结算账户-商户可选的证件类型
     *
     * @param req 请求dto
     * @return 可选的结算账户证件类型 1-中华人民共和国身份证 2-港澳居民往来内地通行证 3-台湾居民往来内地通行证 4-非中华人民共和国护照 5-中华人民共和国护照 6-港澳居民居住证 7-台湾居民居住证
     */
    @Override
    public Map<Integer, String> listSelectableSettlementAccountCertificateType(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.listSelectableSettlementAccountCertificateType(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 商户可选的结算账户类型
     *
     * @param req 请求dto
     * @return 可选的结算账户类型 1-法人代表个人账户 2-对公企业账户 3-其它个人账户 4-授权对公企业账户
     */
    @Override
    public Map<Integer, String> listSelectableSettlementAccountType(ApiRequestParam<BusinessLicenseTypeReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.listSelectableSettlementAccountType(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 获取营业执照编辑草稿
     *
     * @param req 请求dto
     * @return 编辑草稿
     */
    @Override
    public BusinessLicenseAuditEditDraftDTO getBusinessLicenseEditDraft(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.getBusinessLicenseEditDraft(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 删除营业执照编辑草稿
     *
     * @param req 请求dto
     */
    @Override
    public void deleteBusinessLicenseEditDraft(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        businessLicenseV2Service.deleteBusinessLicenseEditDraft(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 保存营业执照编辑草稿
     *
     * @param req 请求dto
     * @return 保存结果
     */
    @Override
    public SaveBusinessLicenseDraftResultRspDTO saveBusinessLicenseEditDraft(ApiRequestParam<BusinessLicenseAuditEditDraftReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.saveBusinessLicenseEditDraft(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 申请编辑营业执照token
     * 对于BD来说，必须有草稿才可以申请token
     *
     * @param req 请求dto
     * @return 申请结果
     */
    @Override
    public ApplyEditBusinessLicenseTokenResultRspDTO applyEditBusinessLicenseToken(ApiRequestParam<BusinessLicenseApplyTokenReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.applyEditBusinessLicenseToken(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 验证token是否有效
     *
     * @param req req
     * @return 验证结果
     */
    @Override
    public CheckEditBusinessLicenseTokenResultRspDTO checkEditBusinessLicenseTokenValid(ApiRequestParam<BusLicEditHeartbeatDetectionReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.checkEditBusinessLicenseTokenValid(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 营业执照编辑心跳检测
     *
     * @param req 请求dto
     * @return 心跳检测结果
     */
    @Override
    public CommonResultResp busLicEditHeartbeatDetection(ApiRequestParam<BusLicEditHeartbeatDetectionReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.busLicEditHeartbeatDetection(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 退出营业执照编辑
     *
     * @param req 请求dto
     * @return 退出结果
     */
    @Override
    public CommonResultResp exitBusinessLicenseEdit(ApiRequestParam<BusLicEditHeartbeatDetectionReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.exitBusinessLicenseEdit(getCommonFieldDTOByCrmReq(req));
    }

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Value("${license.invite.dev_code}")
    private String inviteDevCode;

    @Value("${license.invite.template_code}")
    private String inviteTemplateCode;

    @Value("${license.invite.msp_template_code}")
    private String mspInviteTemplateCode;


    /**
     * 给商户发通知
     *
     * @param req 请求
     */
    @Override
    public CommonResultResp noticeInviteMerchantUpdateLicense(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> req) {
        try {
            String merchantId = req.getBodyParams().getMerchantId();
            UcMerchantUserSimpleInfo userInfo = merchantUserServiceV2.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (Objects.nonNull(userInfo)) {
                sendNotice(inviteTemplateCode, userInfo, merchantId);
                sendNotice(mspInviteTemplateCode, userInfo, merchantId);
                return CommonResultResp.SUCCESS();
            }
            return CommonResultResp.FAIL("商户不存在");
        } catch (Exception e) {
            log.error("noticeInviteMerchantUpdateLicense fail, req:{}", JSON.toJSONString(req), e);
            return CommonResultResp.FAIL(e.getMessage());
        }
    }

    private void sendNotice(String templateCode, UcMerchantUserSimpleInfo userInfo, String merchantId) {
        MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
        sendModel.setDevCode(inviteDevCode);
        sendModel.setTemplateCode(templateCode);
        sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
        sendModel.setTimestamp(System.currentTimeMillis());
        clientSideNoticeService.sendToMerchantUser(sendModel);
        log.info("推送通知给老板成功 {} {}", merchantId, JSON.toJSONString(sendModel));
    }

    /**
     * 商户是否需要确认结算信息
     *
     * @param req 请求dto
     */
    @Override
    public CommonResultResp isMerchantNeedVerifySettlementAccount(ApiRequestParam<BusinessLicenseAuditApplyReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.isMerchantNeedVerifySettlementAccount(getCommonFieldDTOByCrmReq(req));
    }

    @Override
    public CheckBankAccountResp checkoutAllowChangeCard(ApiRequestParam<BusinessLicenseAuditApplyReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.checkoutAllowChangeCard(getCommonFieldDTOByCrmReq(req));
    }

    @Override
    public Map getCurrentBankCardData(ApiRequestParam<BusinessLicenseAuditApplyReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.getCurrentBankCardData(getCommonFieldDTOByCrmReq(req));
    }

    /**
     * 获取营业执照认证进度文案
     *
     * @param appCommonFieldDTO 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp showBusinessLicenseCertificationProgressText(ApiRequestParam<AppCommonFieldDTO, Map<String, Object>> appCommonFieldDTO) {
        return businessLicenseV2Service.showBusinessLicenseCertificationProgressText(getCommonFieldDTOByCrmReq(appCommonFieldDTO));
    }

    /**
     * 校验营业执照申请单
     *
     * @param applyReqDTO 营业执照申请单
     * @return 校验结果
     */
    @Override
    public LicenseApplyCheckResultRsp checkBusinessLicenseAuditApplyReqDTO(ApiRequestParam<BusinessLicenseAuditApplyReqDTO, Map<String, Object>> applyReqDTO) {
        return businessLicenseV2Service.checkBusinessLicenseAuditApplyReqDTO(getCommonFieldDTOByCrmReq(applyReqDTO));
    }

    /**
     * 校验结算信息有没有变更
     *
     * @param req 请求dto
     * @return 结果
     */
    @Override
    public CommonResultResp isSettlementInfoChanged(ApiRequestParam<BusinessLicenseAuditApplyReqDTO, Map<String, Object>> req) {
        return businessLicenseV2Service.isSettlementInfoChanged(getCommonFieldDTOByCrmReq(req));
    }
}
