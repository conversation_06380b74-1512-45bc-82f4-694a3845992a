package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.biz.LicenseBiz;
import com.wosai.mc.biz.McPreBiz;
import com.wosai.mc.config.exception.McException;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.req.CreateLicenseReq;
import com.wosai.mc.model.req.UpdateLicenseReq;
import com.wosai.mc.model.req.VerifyLicenseReq;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.upay.core.model.License;
import com.wosai.upay.core.service.McPreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class LicenseServiceImpl implements LicenseService {
    @Autowired
    private com.wosai.upay.core.service.LicenseService licenseService;
    @Autowired
    private McPreBiz mcPreBiz;
    @Autowired
    private LicenseBiz licenseBiz;
    @Autowired
    private McPreService mcPreService;

    @Override
    public int saveLicense(CreateLicenseReq req) {
        Map license = MyBeanUtil.toMap(req);
        licenseService.saveLicense(license);
        return 1;
    }

    @Override
    public List<Map> getLicenseByBusinessLicenseId(String businessLicenseId, String dev_code) {
        if (WosaiStringUtils.isNotBlank(dev_code)) {
            List<Map> preDataList = mcPreBiz.getMcPreDataList(businessLicenseId, dev_code, TableNameEnum.LICENSE.getTableName());
            if (WosaiCollectionUtils.isNotEmpty(preDataList)) {
                preDataList = preDataList.stream().map(map -> {
                    CommonUtils.handPhotos(map, "license_photo");
                    return map;
                }).collect(Collectors.toList());

//                List<String> idsFromPre = preDataList.stream().map(map -> MapUtils.getString(map, "id")).collect(Collectors.toList());
//                //返回中间表数据 + 原表有但中间表可能没有的数据
//                List<Map> originLicense = licenseService.getLicenseByBusinessLicenseId(businessLicenseId);
//
//                List<Map> needAddToo = originLicense.stream().filter(map -> !idsFromPre.contains(MapUtils.getString(map, "id"))).collect(Collectors.toList());
//                if (WosaiCollectionUtils.isNotEmpty(needAddToo)) {
//                    preDataList.addAll(needAddToo);
//                }
                return preDataList;
            }
        }
        //查原表
        return licenseService.getLicenseByBusinessLicenseId(businessLicenseId);
    }


    public int updateLicenseOrigin(List<UpdateLicenseReq> license, String dev_code, String str) {
        Map update = MyBeanUtil.toMap(license);
        update.remove("business_license_id");

        if (WosaiStringUtils.isNotBlank(dev_code)) {
            Map<String, Object> licenseById = licenseService.getLicenseById("license.getId()");
            if (WosaiMapUtils.isEmpty(licenseById)) {
                log.error("更新到中间表失败,无原表信息 req : {}", license);
                throw new McException("未找到该许可证信息,无法更新到中间表");
            }
            licenseById.putAll(update);
            licenseBiz.updateToMcPre((String) licenseById.get("business_license_id"), dev_code, licenseById);
            return 1;
        }
        licenseService.updateLicense(update);
        return 1;
    }

    @Override
    public int updateLicense(List<UpdateLicenseReq> license, String businessId, String dev_code) {

        //参数为空list,代表需要删除许可证
        if (license != null && license.size() == 0) {
            List<Map<String, Object>> maps = licenseService.deleteAllLicenseByBusinessLicenseIdTruly(businessId);
            log.info("删除的旧许可证数据Q1 : {}", maps);
            return 1;
        }
        //null不做操作
        if (license == null) {
            return 0;
        }
        List<Map> list = new ArrayList<>();
        for (UpdateLicenseReq updateLicenseReq : license) {
            list.add(MyBeanUtil.toMap(updateLicenseReq));
        }
        //统一设置对应的营业执照id
        List<Map> date = list.stream().map(map -> {
            map.put(License.BUSINESS_LICENSE_ID, businessId);
            return map;
        }).collect(Collectors.toList());

        if (WosaiStringUtils.isNotBlank(dev_code)) {
//            //原表操作逻辑
//            List<Map<String, Object>> maps = licenseService.deleteAllLicenseByBusinessLicenseIdTruly(businessId);
//            log.info("删除的旧许可证数据Q2-1 : {}", maps);
//            licenseService.updateLicenseComplete(date);
            licenseBiz.updateToMcPreComplete(businessId, dev_code, date);
            return 1;
        }
        //原表操作逻辑
        List<Map<String, Object>> maps = licenseService.deleteAllLicenseByBusinessLicenseIdTruly(businessId);
        log.info("删除的旧许可证数据Q2-2 : {}", maps);
        licenseService.updateLicenseComplete(date);
        //直接更新原表的情况下,依然需要检查一下中间表,中间表有数据也更新当前中间表数据
        Map beForeMcPre = mcPreBiz.findMcPre(TableNameEnum.LICENSE.getTableName(), businessId);
        if (WosaiMapUtils.isNotEmpty(beForeMcPre)) {
            licenseBiz.updateToMcPreComplete(businessId, "updateOrigin", date);
        }
        return 1;
    }

    @Override
    public int deleteLicenseById(String id) {
        //先删除中间表
        Map<String, Object> licenseById = licenseService.getLicenseById(id);
        String business_license_id = (String) licenseById.get("business_license_id");
        Map licenseMC = mcPreBiz.getMcPreListDataWithMcId(business_license_id, "deleteLicenseById", TableNameEnum.LICENSE.getTableName());
        if (WosaiMapUtils.isNotEmpty(licenseMC)) {
            List<Map> mcPreList = JSONObject.parseArray(licenseMC.get("data").toString(), Map.class);
            if (WosaiCollectionUtils.isNotEmpty(mcPreList)) {
                List<Map> willDeleteMc = mcPreList.stream().filter(map -> WosaiStringUtils.equals(id, (String) map.get("id"))).collect(Collectors.toList());
                if (WosaiCollectionUtils.isNotEmpty(willDeleteMc)) {
                    //删除指定许可证
                    mcPreList.remove(willDeleteMc.get(0));

                    licenseMC.put("data", JSON.toJSONString(mcPreList));
                    mcPreService.updateMcPre(licenseMC);
                }

            }
        }

        //删除原表
        licenseService.deleteLicenseById(id);
        return 1;
    }

    @Override
    public int deleteAllLicenseByBusinessLicenseId(String businessLicenseId) {
        mcPreService.deleteExcessData(TableNameEnum.LICENSE.getTableName(), businessLicenseId);
        licenseService.deleteAllLicenseByBusinessLicenseId(businessLicenseId);
        return 1;
    }

    @Override
    public int verifyLicense(VerifyLicenseReq req) {
        if (WosaiStringUtils.isBlank(req.getId()) && WosaiStringUtils.isBlank(req.getBusiness_license_id())) {
            throw new McException("认证许可证时,许可证id和营业执照id不能全为空");
        }
        if (WosaiStringUtils.isNotBlank(req.getId())) {
            licenseBiz.verifyLicense(req.getId(), req.getVerifyStatus(), req.getVerifyParams());
            return 1;
        } else if (WosaiStringUtils.isNotBlank(req.getBusiness_license_id())) {
            List<Map> licenses = getLicenseByBusinessLicenseId(req.getBusiness_license_id(), null);
            if (WosaiCollectionUtils.isNotEmpty(licenses)) {
                for (Map licens : licenses) {
                    licenseBiz.verifyLicense((String) licens.get("id"), req.getVerifyStatus(), req.getVerifyParams());
                }
                return licenses.size();
            }
        }

        return 0;
    }

    @Override
    public int copyMcInfoToLicenseByBusinessLicenseId(String businessLicenseId, String devCode) {
        //先查出中间表数据
        Map mcPre = mcPreBiz.findMcPre(TableNameEnum.LICENSE.getTableName(), businessLicenseId);
        if (WosaiMapUtils.isNotEmpty(mcPre)) {

            List<Map> mcPreDataList = JSONObject.parseArray(mcPre.get("data").toString(), Map.class);

            List<Map> data = mcPreDataList.stream().map(map -> GetRealObject.filterParams(map, GetRealObject.LICENSE_DATA)).collect(Collectors.toList());

            //该接口会删除  原表+中间表  的许可证数据
            List<Map<String, Object>> deletes = licenseService.deleteAllLicenseByBusinessLicenseIdTruly(businessLicenseId);
            log.info("删除的旧许可证数据: {}", JSON.toJSONString(deletes));

            //重新存一分数据
            licenseService.updateLicenseComplete(data);
            return 1;

        }

        return 0;
    }
}
