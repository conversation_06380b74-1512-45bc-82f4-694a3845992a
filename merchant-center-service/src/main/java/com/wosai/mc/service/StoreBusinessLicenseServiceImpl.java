package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.mc.biz.LicenseBiz;
import com.wosai.mc.biz.McPreBiz;
import com.wosai.mc.biz.StoreBusinessLicenseBiz;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.StoreBusinessLicenseInfo;
import com.wosai.mc.model.req.CreateStoreBusinessLicenseReq;
import com.wosai.mc.model.req.UpdateStoreBusinessLicenseReq;
import com.wosai.mc.model.req.VerifyLicenseReq;
import com.wosai.mc.model.req.VerifyStoreBusinessLicenseReq;
import com.wosai.mc.utils.GetRealObject;
import com.wosai.mc.utils.MyBeanUtil;
import com.wosai.upay.core.service.McPreService;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@AutoJsonRpcServiceImpl
public class StoreBusinessLicenseServiceImpl implements StoreBusinessLicenseService {
    @Autowired
    private com.wosai.upay.core.service.StoreBusinessLicenseService storeBusinessLicenseService;
    @Autowired
    private StoreBusinessLicenseBiz storeBusinessLicenseBiz;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private McPreService mcPreService;
    @Autowired
    private McPreBiz mcPreBiz;

    @Override
    public StoreBusinessLicenseInfo createStoreBusinessLicense(CreateStoreBusinessLicenseReq req) {
        Map storeBusinessLicense = MyBeanUtil.toMap(req);
        Map<String, Object> check = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(req.getStoreId());
        if (WosaiMapUtils.isNotEmpty(check)) {
            throw new CommonInvalidParameterException("该门店已有门店营业执照信息,不可重复创建");
        }
//        if (req.isUse_merchant_business_license()) {
//            //如果是复用商户维度,那么门店许可证存入商户维度,
//            storeBusinessLicense.remove("trade_license_list");
//            List<CreateLicenseWithBusinessReq> trade_license_list = req.getTrade_license_list();
//            if (WosaiCollectionUtils.isNotEmpty(trade_license_list)) {
//                MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(req.getMerchantId(), null);
//                for (CreateLicenseWithBusinessReq createLicenseWithBusinessReq : trade_license_list) {
//                    CreateLicenseReq createLicenseReq = new CreateLicenseReq();
//                    BeanUtils.copyProperties(createLicenseWithBusinessReq, createLicenseReq);
//                    licenseService.saveLicense(createLicenseReq.setBusinessLicenseId(merchantBusinessLicense.getId()));
//                }
//            }
//        }
        storeBusinessLicenseService.saveStoreBusinessLicense(storeBusinessLicense);

        storeBusinessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(req.getStoreId());
        //复用时,直接返回商户维度信息
        if ((boolean) storeBusinessLicense.get("use_merchant_business_license")) {
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(req.getMerchantId(), null);
            if (merchantBusinessLicense != null) {
                StoreBusinessLicenseInfo storeBusinessLicenseInfo = JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);
                BeanUtils.copyProperties(merchantBusinessLicense, storeBusinessLicenseInfo);
                return JSON.parseObject(JSON.toJSONString(storeBusinessLicenseInfo), StoreBusinessLicenseInfo.class);
            }
            return null;
        }
        StoreBusinessLicenseInfo storeBusinessLicenseInfo = JSON.parseObject(JSON.toJSONString(storeBusinessLicense), StoreBusinessLicenseInfo.class);

        return storeBusinessLicenseInfo;
    }

    @Override
    public int updateStoreBusinessLicense(UpdateStoreBusinessLicenseReq req, String devCode) {
        storeBusinessLicenseBiz.updateStoreBusinessLicense(req, devCode);
        return 1;
    }

    @Override
    public int deleteStoreBusinessLicenseById(String id) {
        return storeBusinessLicenseService.deleteStoreBusinessLicenseById(id);
    }

    @Override
    public int deleteStoreBusinessLicenseByStoreId(String storeId) {
        return storeBusinessLicenseService.deleteStoreBusinessLicenseByStoreId(storeId);

    }

    @Override
    public StoreBusinessLicenseInfo getStoreBusinessLicenseByStoreId(String storeId, String devCode) {
        return storeBusinessLicenseBiz.getStoreBusinessLicenseByStoreId(storeId, devCode, true);


    }

    @Override
    public int copyMcInfoToStoreBusinessLicenseByStoreId(String storeId, String devCode) {
        Map data = mcPreBiz.getMcPreData(storeId, devCode, TableNameEnum.STORE_BUSINESS_LICENSE.getTableName());
        if (WosaiMapUtils.isNotEmpty(data)) {
            data = GetRealObject.filterParams(data, GetRealObject.STORE_BUSINESS_LICENSE_DATA);
            storeBusinessLicenseService.updateStoreBusinessLicense(data);
            mcPreService.deleteExcessData(TableNameEnum.STORE_BUSINESS_LICENSE.getTableName(), storeId);
            StoreBusinessLicenseInfo storeBusinessLicense = getStoreBusinessLicenseByStoreId(storeId, null);
            licenseService.copyMcInfoToLicenseByBusinessLicenseId(storeBusinessLicense.getId(), "copyStoreBusinessLicense");

            return 1;
        }
        return 0;
    }

    @Override
    public int verifyStoreBusinessLicense(VerifyStoreBusinessLicenseReq req) {

        List<String> verifyStatuss = Lists.newArrayList("1", "2", "3");
        StoreBusinessLicenseInfo storeBusinessLicense = getStoreBusinessLicenseByStoreId(req.getStoreId(), null);
        Map<String, List<String>> extra = storeBusinessLicense.getExtra();
        extra = Optional.ofNullable(extra).orElse(new HashMap<String, List<String>>(16));
        for (String verifyStatus : verifyStatuss) {
            if (req.getVerifyStatus().equals(verifyStatus)) {
                extra.put(verifyStatus, req.getVerifyParams());
                verifyStatuss.remove(verifyStatus);
                for (String status : verifyStatuss) {
                    if (extra.get(status) != null) {
                        extra.get(status).removeAll(req.getVerifyParams());
                    }
                }
                break;
            }
        }
        storeBusinessLicense.setTrade_license_list(null);
        storeBusinessLicense.setExtra(extra);
        storeBusinessLicense.setVerify_status(Integer.parseInt(req.getVerifyStatus()));
        storeBusinessLicenseService.updateStoreBusinessLicense(JSONObject.parseObject(JSONObject.toJSONString(storeBusinessLicense), Map.class));
        return 1;
    }

    @Override
    public int storeBusinessLicenseVerifySuccess(String storeId) {
        StoreBusinessLicenseInfo storeBusinessLicense = getStoreBusinessLicenseByStoreId(storeId, "StoreBusinessLicenseAuditEvent");

        if (storeBusinessLicense.isUse_merchant_business_license()) {
            //复用商户营业执照情况下, 更新商户维度数据
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(storeBusinessLicense.getMerchant_id(), null);
            //复用场景下,无需审核的状态(4),执行和 (2) 一样的逻辑
            copyMcInfoToStoreBusinessLicenseByStoreId(storeId, "StoreBusinessLicenseAudit");
            licenseService.copyMcInfoToLicenseByBusinessLicenseId(merchantBusinessLicense.getId(), "LicenseAuditEvent");
            licenseService.verifyLicense(new VerifyLicenseReq().setBusiness_license_id(merchantBusinessLicense.getId()).setVerifyStatus("2").setVerifyParams(LicenseBiz.getAllAuditProp()));
            //复用关系时,审核通过时合并商户维度数据后,删除门店维度许可证中间表数据(如有)
            Map<String, Object> storeBusinessLicenseOrigin = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeId);
            mcPreService.deleteExcessData("license", MapUtils.getString(storeBusinessLicenseOrigin, "id"));
        } else {
            copyMcInfoToStoreBusinessLicenseByStoreId(storeId, "StoreBusinessLicenseAuditEvent");
            verifyStoreBusinessLicense(new VerifyStoreBusinessLicenseReq().setStoreId(storeId).setVerifyStatus("2").setVerifyParams(StoreBusinessLicenseBiz.getAllAuditProp()));
            licenseService.verifyLicense(new VerifyLicenseReq().setBusiness_license_id(storeBusinessLicense.getId()).setVerifyStatus("2").setVerifyParams(LicenseBiz.getAllAuditProp()));

            MerchantBusinessLicenseInfo merchantBusinessLicenseOrigin = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(storeBusinessLicense.getMerchant_id(), null);
            mcPreService.deleteExcessData("license", merchantBusinessLicenseOrigin.getId());
        }
        return 1;
    }

}
