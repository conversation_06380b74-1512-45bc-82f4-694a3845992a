package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.service.VideoService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.biz.SensorSendBiz;
import com.wosai.mc.biz.StoreExtBiz;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.req.StoreExtReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-28
 * @Description:
 */

@Component
@AutoJsonRpcServiceImpl
public class StoreExtServiceImpl implements StoreExtService {
    @Autowired
    StoreExtBiz storeExtBiz;
    @Autowired
    SensorSendBiz sensorSendBiz;
    @Autowired
    PhotoInfoService photoInfoService;
    @Autowired
    VideoService videoService;
    @Autowired
    StoreService cbStoreService;

    @Override
    public int updateStoreExt(StoreExtReq storeExtReq, String devCode) {
        StoreExtInfo oldStoreExtInfo = storeExtBiz.findStoreExtByStoreId(storeExtReq.getStoreId(), devCode);
        storeExtBiz.updateStoreExt(storeExtReq, devCode);
        sensorSendBiz.sendChangeStore(null, oldStoreExtInfo, null, devCode, storeExtReq.getOperator(), storeExtReq.getPlatform());
        return 1;
    }


    @Override
    public StoreExtInfo findStoreExt(String id, String devCode) {
        return storeExtBiz.findStoreExt(id, devCode);
    }

    @Override
    public StoreExtInfo findStoreExtByStoreId(String storeId, String devCode) {
        return storeExtBiz.findStoreExtByStoreId(storeId, devCode);
    }

    @Override
    public StoreExtInfo findLastStoreExtByStoreId(String storeId) {
        return storeExtBiz.findLastStoreExtByStoreId(storeId);
    }

    @Override
    public StotreExtInfoAndPictures findStoreExtAndPicturesByStoreId(String storeId, String devCode) {
        return storeExtBiz.findStoreExtAndPicturesByStoreId(storeId, devCode);
    }

    @Override
    public StotreExtInfoAndPictures findLastStoreExtAndPicturesByStoreId(String storeId) {
        return storeExtBiz.findLastStoreExtAndPicturesByStoreId(storeId);
    }

    @Override
    public StotreExtInfoAndPictures findLastStoreExtAndPicturesByMerchantId(String merchantId) {
        PageInfo pageInfo = new PageInfo(1, 1);
        pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));
        ListResult listResult = cbStoreService.getSimpleStoreListByMerchantId(merchantId, pageInfo);
        if (WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            return null;
        }
        String storeId = BeanUtil.getPropString(listResult.getRecords().get(0), DaoConstants.ID);
        if (WosaiStringUtils.isEmpty(storeId)) {
            return null;
        }
        return storeExtBiz.findLastStoreExtAndPicturesByStoreId(storeId);
    }

    @Override
    public Map<String, String> getBrandOnlyScenePhotoBatchByStoreIds(List<String> storeIds) {

        return storeExtBiz.getBrandOnlyScenePhotos(storeIds);
    }

}
