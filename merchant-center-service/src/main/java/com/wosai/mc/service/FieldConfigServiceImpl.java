package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mc.biz.FieldConfigBiz;
import com.wosai.mc.model.FieldConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class FieldConfigServiceImpl implements FieldConfigService {

    @Autowired
    private FieldConfigBiz fieldConfigBiz;

    @Override
    public List<FieldConfig> queryFieldConfigs(List<String> tableNames) {

        return fieldConfigBiz.queryByTables(tableNames);
    }
}
