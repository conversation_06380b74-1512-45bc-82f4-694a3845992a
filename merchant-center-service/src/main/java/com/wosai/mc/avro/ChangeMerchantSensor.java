/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.mc.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class ChangeMerchantSensor extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -6154629247480176061L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"ChangeMerchantSensor\",\"namespace\":\"com.wosai.mc.avro\",\"fields\":[{\"name\":\"merchant_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_sn\",\"type\":[\"string\",\"null\"]},{\"name\":\"mtime\",\"type\":[\"long\",\"null\"]},{\"name\":\"operator\",\"type\":[\"string\",\"null\"]},{\"name\":\"platform\",\"type\":[\"string\",\"null\"]},{\"name\":\"name\",\"type\":[\"string\",\"null\"]},{\"name\":\"alias\",\"type\":[\"string\",\"null\"]},{\"name\":\"industry\",\"type\":[\"string\",\"null\"]},{\"name\":\"status\",\"type\":[\"int\",\"null\"]},{\"name\":\"rank\",\"type\":[\"int\",\"null\"]},{\"name\":\"withdraw_mode\",\"type\":[\"int\",\"null\"]},{\"name\":\"longitude\",\"type\":[\"string\",\"null\"]},{\"name\":\"latitude\",\"type\":[\"string\",\"null\"]},{\"name\":\"country\",\"type\":[\"string\",\"null\"]},{\"name\":\"province\",\"type\":[\"string\",\"null\"]},{\"name\":\"city\",\"type\":[\"string\",\"null\"]},{\"name\":\"district\",\"type\":[\"string\",\"null\"]},{\"name\":\"street_address\",\"type\":[\"string\",\"null\"]},{\"name\":\"contact_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"contract_cellphone\",\"type\":[\"string\",\"null\"]},{\"name\":\"contact_phone\",\"type\":[\"string\",\"null\"]},{\"name\":\"contact_email\",\"type\":[\"string\",\"null\"]},{\"name\":\"concat_id_card_front_photo\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"legal_person_type\",\"type\":[\"int\",\"null\"]},{\"name\":\"legal_person_register_no\",\"type\":[\"string\",\"null\"]},{\"name\":\"business_license_photo\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"business\",\"type\":[\"string\",\"null\"]},{\"name\":\"currency\",\"type\":[\"string\",\"null\"]},{\"name\":\"owner_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"owner_cellphone\",\"type\":[\"string\",\"null\"]},{\"name\":\"customer_phone\",\"type\":[\"string\",\"null\"]},{\"name\":\"logo\",\"type\":[\"string\",\"null\"]},{\"name\":\"business_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"concat_identity\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_type\",\"type\":[\"int\",\"null\"]},{\"name\":\"type\",\"type\":[\"int\",\"null\"]},{\"name\":\"photo\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"number\",\"type\":[\"string\",\"null\"]},{\"name\":\"license_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"validity\",\"type\":[\"string\",\"null\"]},{\"name\":\"address\",\"type\":[\"string\",\"null\"]},{\"name\":\"registered_legal_person_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"letter_of_authorization\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"trade_license\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"legal_person_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"legal_person_id_type\",\"type\":[\"int\",\"null\"]},{\"name\":\"legal_person_id_number\",\"type\":[\"string\",\"null\"]},{\"name\":\"legal_person_id_card_front_photo\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"legal_person_id_card_back_photo\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"legal_person_id_card_address\",\"type\":[\"string\",\"null\"]},{\"name\":\"legal_person_id_card_issuing_authority\",\"type\":[\"string\",\"null\"]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<ChangeMerchantSensor> ENCODER =
      new BinaryMessageEncoder<ChangeMerchantSensor>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<ChangeMerchantSensor> DECODER =
      new BinaryMessageDecoder<ChangeMerchantSensor>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<ChangeMerchantSensor> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<ChangeMerchantSensor> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<ChangeMerchantSensor>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this ChangeMerchantSensor to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a ChangeMerchantSensor from a ByteBuffer. */
  public static ChangeMerchantSensor fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.Long mtime;
  @Deprecated public java.lang.CharSequence operator;
  @Deprecated public java.lang.CharSequence platform;
  @Deprecated public java.lang.CharSequence name;
  @Deprecated public java.lang.CharSequence alias;
  @Deprecated public java.lang.CharSequence industry;
  @Deprecated public java.lang.Integer status;
  @Deprecated public java.lang.Integer rank;
  @Deprecated public java.lang.Integer withdraw_mode;
  @Deprecated public java.lang.CharSequence longitude;
  @Deprecated public java.lang.CharSequence latitude;
  @Deprecated public java.lang.CharSequence country;
  @Deprecated public java.lang.CharSequence province;
  @Deprecated public java.lang.CharSequence city;
  @Deprecated public java.lang.CharSequence district;
  @Deprecated public java.lang.CharSequence street_address;
  @Deprecated public java.lang.CharSequence contact_name;
  @Deprecated public java.lang.CharSequence contract_cellphone;
  @Deprecated public java.lang.CharSequence contact_phone;
  @Deprecated public java.lang.CharSequence contact_email;
  @Deprecated public java.lang.Boolean concat_id_card_front_photo;
  @Deprecated public java.lang.Integer legal_person_type;
  @Deprecated public java.lang.CharSequence legal_person_register_no;
  @Deprecated public java.lang.Boolean business_license_photo;
  @Deprecated public java.lang.CharSequence business;
  @Deprecated public java.lang.CharSequence currency;
  @Deprecated public java.lang.CharSequence owner_name;
  @Deprecated public java.lang.CharSequence owner_cellphone;
  @Deprecated public java.lang.CharSequence customer_phone;
  @Deprecated public java.lang.CharSequence logo;
  @Deprecated public java.lang.CharSequence business_name;
  @Deprecated public java.lang.CharSequence concat_identity;
  @Deprecated public java.lang.Integer merchant_type;
  @Deprecated public java.lang.Integer type;
  @Deprecated public java.lang.Boolean photo;
  @Deprecated public java.lang.CharSequence number;
  @Deprecated public java.lang.CharSequence license_name;
  @Deprecated public java.lang.CharSequence validity;
  @Deprecated public java.lang.CharSequence address;
  @Deprecated public java.lang.CharSequence registered_legal_person_name;
  @Deprecated public java.lang.Boolean letter_of_authorization;
  @Deprecated public java.lang.Boolean trade_license;
  @Deprecated public java.lang.CharSequence legal_person_name;
  @Deprecated public java.lang.Integer legal_person_id_type;
  @Deprecated public java.lang.CharSequence legal_person_id_number;
  @Deprecated public java.lang.Boolean legal_person_id_card_front_photo;
  @Deprecated public java.lang.Boolean legal_person_id_card_back_photo;
  @Deprecated public java.lang.CharSequence legal_person_id_card_address;
  @Deprecated public java.lang.CharSequence legal_person_id_card_issuing_authority;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public ChangeMerchantSensor() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param merchant_sn The new value for merchant_sn
   * @param mtime The new value for mtime
   * @param operator The new value for operator
   * @param platform The new value for platform
   * @param name The new value for name
   * @param alias The new value for alias
   * @param industry The new value for industry
   * @param status The new value for status
   * @param rank The new value for rank
   * @param withdraw_mode The new value for withdraw_mode
   * @param longitude The new value for longitude
   * @param latitude The new value for latitude
   * @param country The new value for country
   * @param province The new value for province
   * @param city The new value for city
   * @param district The new value for district
   * @param street_address The new value for street_address
   * @param contact_name The new value for contact_name
   * @param contract_cellphone The new value for contract_cellphone
   * @param contact_phone The new value for contact_phone
   * @param contact_email The new value for contact_email
   * @param concat_id_card_front_photo The new value for concat_id_card_front_photo
   * @param legal_person_type The new value for legal_person_type
   * @param legal_person_register_no The new value for legal_person_register_no
   * @param business_license_photo The new value for business_license_photo
   * @param business The new value for business
   * @param currency The new value for currency
   * @param owner_name The new value for owner_name
   * @param owner_cellphone The new value for owner_cellphone
   * @param customer_phone The new value for customer_phone
   * @param logo The new value for logo
   * @param business_name The new value for business_name
   * @param concat_identity The new value for concat_identity
   * @param merchant_type The new value for merchant_type
   * @param type The new value for type
   * @param photo The new value for photo
   * @param number The new value for number
   * @param license_name The new value for license_name
   * @param validity The new value for validity
   * @param address The new value for address
   * @param registered_legal_person_name The new value for registered_legal_person_name
   * @param letter_of_authorization The new value for letter_of_authorization
   * @param trade_license The new value for trade_license
   * @param legal_person_name The new value for legal_person_name
   * @param legal_person_id_type The new value for legal_person_id_type
   * @param legal_person_id_number The new value for legal_person_id_number
   * @param legal_person_id_card_front_photo The new value for legal_person_id_card_front_photo
   * @param legal_person_id_card_back_photo The new value for legal_person_id_card_back_photo
   * @param legal_person_id_card_address The new value for legal_person_id_card_address
   * @param legal_person_id_card_issuing_authority The new value for legal_person_id_card_issuing_authority
   */
  public ChangeMerchantSensor(java.lang.CharSequence merchant_id, java.lang.CharSequence merchant_sn, java.lang.Long mtime, java.lang.CharSequence operator, java.lang.CharSequence platform, java.lang.CharSequence name, java.lang.CharSequence alias, java.lang.CharSequence industry, java.lang.Integer status, java.lang.Integer rank, java.lang.Integer withdraw_mode, java.lang.CharSequence longitude, java.lang.CharSequence latitude, java.lang.CharSequence country, java.lang.CharSequence province, java.lang.CharSequence city, java.lang.CharSequence district, java.lang.CharSequence street_address, java.lang.CharSequence contact_name, java.lang.CharSequence contract_cellphone, java.lang.CharSequence contact_phone, java.lang.CharSequence contact_email, java.lang.Boolean concat_id_card_front_photo, java.lang.Integer legal_person_type, java.lang.CharSequence legal_person_register_no, java.lang.Boolean business_license_photo, java.lang.CharSequence business, java.lang.CharSequence currency, java.lang.CharSequence owner_name, java.lang.CharSequence owner_cellphone, java.lang.CharSequence customer_phone, java.lang.CharSequence logo, java.lang.CharSequence business_name, java.lang.CharSequence concat_identity, java.lang.Integer merchant_type, java.lang.Integer type, java.lang.Boolean photo, java.lang.CharSequence number, java.lang.CharSequence license_name, java.lang.CharSequence validity, java.lang.CharSequence address, java.lang.CharSequence registered_legal_person_name, java.lang.Boolean letter_of_authorization, java.lang.Boolean trade_license, java.lang.CharSequence legal_person_name, java.lang.Integer legal_person_id_type, java.lang.CharSequence legal_person_id_number, java.lang.Boolean legal_person_id_card_front_photo, java.lang.Boolean legal_person_id_card_back_photo, java.lang.CharSequence legal_person_id_card_address, java.lang.CharSequence legal_person_id_card_issuing_authority) {
    this.merchant_id = merchant_id;
    this.merchant_sn = merchant_sn;
    this.mtime = mtime;
    this.operator = operator;
    this.platform = platform;
    this.name = name;
    this.alias = alias;
    this.industry = industry;
    this.status = status;
    this.rank = rank;
    this.withdraw_mode = withdraw_mode;
    this.longitude = longitude;
    this.latitude = latitude;
    this.country = country;
    this.province = province;
    this.city = city;
    this.district = district;
    this.street_address = street_address;
    this.contact_name = contact_name;
    this.contract_cellphone = contract_cellphone;
    this.contact_phone = contact_phone;
    this.contact_email = contact_email;
    this.concat_id_card_front_photo = concat_id_card_front_photo;
    this.legal_person_type = legal_person_type;
    this.legal_person_register_no = legal_person_register_no;
    this.business_license_photo = business_license_photo;
    this.business = business;
    this.currency = currency;
    this.owner_name = owner_name;
    this.owner_cellphone = owner_cellphone;
    this.customer_phone = customer_phone;
    this.logo = logo;
    this.business_name = business_name;
    this.concat_identity = concat_identity;
    this.merchant_type = merchant_type;
    this.type = type;
    this.photo = photo;
    this.number = number;
    this.license_name = license_name;
    this.validity = validity;
    this.address = address;
    this.registered_legal_person_name = registered_legal_person_name;
    this.letter_of_authorization = letter_of_authorization;
    this.trade_license = trade_license;
    this.legal_person_name = legal_person_name;
    this.legal_person_id_type = legal_person_id_type;
    this.legal_person_id_number = legal_person_id_number;
    this.legal_person_id_card_front_photo = legal_person_id_card_front_photo;
    this.legal_person_id_card_back_photo = legal_person_id_card_back_photo;
    this.legal_person_id_card_address = legal_person_id_card_address;
    this.legal_person_id_card_issuing_authority = legal_person_id_card_issuing_authority;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return merchant_sn;
    case 2: return mtime;
    case 3: return operator;
    case 4: return platform;
    case 5: return name;
    case 6: return alias;
    case 7: return industry;
    case 8: return status;
    case 9: return rank;
    case 10: return withdraw_mode;
    case 11: return longitude;
    case 12: return latitude;
    case 13: return country;
    case 14: return province;
    case 15: return city;
    case 16: return district;
    case 17: return street_address;
    case 18: return contact_name;
    case 19: return contract_cellphone;
    case 20: return contact_phone;
    case 21: return contact_email;
    case 22: return concat_id_card_front_photo;
    case 23: return legal_person_type;
    case 24: return legal_person_register_no;
    case 25: return business_license_photo;
    case 26: return business;
    case 27: return currency;
    case 28: return owner_name;
    case 29: return owner_cellphone;
    case 30: return customer_phone;
    case 31: return logo;
    case 32: return business_name;
    case 33: return concat_identity;
    case 34: return merchant_type;
    case 35: return type;
    case 36: return photo;
    case 37: return number;
    case 38: return license_name;
    case 39: return validity;
    case 40: return address;
    case 41: return registered_legal_person_name;
    case 42: return letter_of_authorization;
    case 43: return trade_license;
    case 44: return legal_person_name;
    case 45: return legal_person_id_type;
    case 46: return legal_person_id_number;
    case 47: return legal_person_id_card_front_photo;
    case 48: return legal_person_id_card_back_photo;
    case 49: return legal_person_id_card_address;
    case 50: return legal_person_id_card_issuing_authority;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_id = (java.lang.CharSequence)value$; break;
    case 1: merchant_sn = (java.lang.CharSequence)value$; break;
    case 2: mtime = (java.lang.Long)value$; break;
    case 3: operator = (java.lang.CharSequence)value$; break;
    case 4: platform = (java.lang.CharSequence)value$; break;
    case 5: name = (java.lang.CharSequence)value$; break;
    case 6: alias = (java.lang.CharSequence)value$; break;
    case 7: industry = (java.lang.CharSequence)value$; break;
    case 8: status = (java.lang.Integer)value$; break;
    case 9: rank = (java.lang.Integer)value$; break;
    case 10: withdraw_mode = (java.lang.Integer)value$; break;
    case 11: longitude = (java.lang.CharSequence)value$; break;
    case 12: latitude = (java.lang.CharSequence)value$; break;
    case 13: country = (java.lang.CharSequence)value$; break;
    case 14: province = (java.lang.CharSequence)value$; break;
    case 15: city = (java.lang.CharSequence)value$; break;
    case 16: district = (java.lang.CharSequence)value$; break;
    case 17: street_address = (java.lang.CharSequence)value$; break;
    case 18: contact_name = (java.lang.CharSequence)value$; break;
    case 19: contract_cellphone = (java.lang.CharSequence)value$; break;
    case 20: contact_phone = (java.lang.CharSequence)value$; break;
    case 21: contact_email = (java.lang.CharSequence)value$; break;
    case 22: concat_id_card_front_photo = (java.lang.Boolean)value$; break;
    case 23: legal_person_type = (java.lang.Integer)value$; break;
    case 24: legal_person_register_no = (java.lang.CharSequence)value$; break;
    case 25: business_license_photo = (java.lang.Boolean)value$; break;
    case 26: business = (java.lang.CharSequence)value$; break;
    case 27: currency = (java.lang.CharSequence)value$; break;
    case 28: owner_name = (java.lang.CharSequence)value$; break;
    case 29: owner_cellphone = (java.lang.CharSequence)value$; break;
    case 30: customer_phone = (java.lang.CharSequence)value$; break;
    case 31: logo = (java.lang.CharSequence)value$; break;
    case 32: business_name = (java.lang.CharSequence)value$; break;
    case 33: concat_identity = (java.lang.CharSequence)value$; break;
    case 34: merchant_type = (java.lang.Integer)value$; break;
    case 35: type = (java.lang.Integer)value$; break;
    case 36: photo = (java.lang.Boolean)value$; break;
    case 37: number = (java.lang.CharSequence)value$; break;
    case 38: license_name = (java.lang.CharSequence)value$; break;
    case 39: validity = (java.lang.CharSequence)value$; break;
    case 40: address = (java.lang.CharSequence)value$; break;
    case 41: registered_legal_person_name = (java.lang.CharSequence)value$; break;
    case 42: letter_of_authorization = (java.lang.Boolean)value$; break;
    case 43: trade_license = (java.lang.Boolean)value$; break;
    case 44: legal_person_name = (java.lang.CharSequence)value$; break;
    case 45: legal_person_id_type = (java.lang.Integer)value$; break;
    case 46: legal_person_id_number = (java.lang.CharSequence)value$; break;
    case 47: legal_person_id_card_front_photo = (java.lang.Boolean)value$; break;
    case 48: legal_person_id_card_back_photo = (java.lang.Boolean)value$; break;
    case 49: legal_person_id_card_address = (java.lang.CharSequence)value$; break;
    case 50: legal_person_id_card_issuing_authority = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public java.lang.Long getMtime() {
    return mtime;
  }

  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(java.lang.Long value) {
    this.mtime = value;
  }

  /**
   * Gets the value of the 'operator' field.
   * @return The value of the 'operator' field.
   */
  public java.lang.CharSequence getOperator() {
    return operator;
  }

  /**
   * Sets the value of the 'operator' field.
   * @param value the value to set.
   */
  public void setOperator(java.lang.CharSequence value) {
    this.operator = value;
  }

  /**
   * Gets the value of the 'platform' field.
   * @return The value of the 'platform' field.
   */
  public java.lang.CharSequence getPlatform() {
    return platform;
  }

  /**
   * Sets the value of the 'platform' field.
   * @param value the value to set.
   */
  public void setPlatform(java.lang.CharSequence value) {
    this.platform = value;
  }

  /**
   * Gets the value of the 'name' field.
   * @return The value of the 'name' field.
   */
  public java.lang.CharSequence getName() {
    return name;
  }

  /**
   * Sets the value of the 'name' field.
   * @param value the value to set.
   */
  public void setName(java.lang.CharSequence value) {
    this.name = value;
  }

  /**
   * Gets the value of the 'alias' field.
   * @return The value of the 'alias' field.
   */
  public java.lang.CharSequence getAlias() {
    return alias;
  }

  /**
   * Sets the value of the 'alias' field.
   * @param value the value to set.
   */
  public void setAlias(java.lang.CharSequence value) {
    this.alias = value;
  }

  /**
   * Gets the value of the 'industry' field.
   * @return The value of the 'industry' field.
   */
  public java.lang.CharSequence getIndustry() {
    return industry;
  }

  /**
   * Sets the value of the 'industry' field.
   * @param value the value to set.
   */
  public void setIndustry(java.lang.CharSequence value) {
    this.industry = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'rank' field.
   * @return The value of the 'rank' field.
   */
  public java.lang.Integer getRank() {
    return rank;
  }

  /**
   * Sets the value of the 'rank' field.
   * @param value the value to set.
   */
  public void setRank(java.lang.Integer value) {
    this.rank = value;
  }

  /**
   * Gets the value of the 'withdraw_mode' field.
   * @return The value of the 'withdraw_mode' field.
   */
  public java.lang.Integer getWithdrawMode() {
    return withdraw_mode;
  }

  /**
   * Sets the value of the 'withdraw_mode' field.
   * @param value the value to set.
   */
  public void setWithdrawMode(java.lang.Integer value) {
    this.withdraw_mode = value;
  }

  /**
   * Gets the value of the 'longitude' field.
   * @return The value of the 'longitude' field.
   */
  public java.lang.CharSequence getLongitude() {
    return longitude;
  }

  /**
   * Sets the value of the 'longitude' field.
   * @param value the value to set.
   */
  public void setLongitude(java.lang.CharSequence value) {
    this.longitude = value;
  }

  /**
   * Gets the value of the 'latitude' field.
   * @return The value of the 'latitude' field.
   */
  public java.lang.CharSequence getLatitude() {
    return latitude;
  }

  /**
   * Sets the value of the 'latitude' field.
   * @param value the value to set.
   */
  public void setLatitude(java.lang.CharSequence value) {
    this.latitude = value;
  }

  /**
   * Gets the value of the 'country' field.
   * @return The value of the 'country' field.
   */
  public java.lang.CharSequence getCountry() {
    return country;
  }

  /**
   * Sets the value of the 'country' field.
   * @param value the value to set.
   */
  public void setCountry(java.lang.CharSequence value) {
    this.country = value;
  }

  /**
   * Gets the value of the 'province' field.
   * @return The value of the 'province' field.
   */
  public java.lang.CharSequence getProvince() {
    return province;
  }

  /**
   * Sets the value of the 'province' field.
   * @param value the value to set.
   */
  public void setProvince(java.lang.CharSequence value) {
    this.province = value;
  }

  /**
   * Gets the value of the 'city' field.
   * @return The value of the 'city' field.
   */
  public java.lang.CharSequence getCity() {
    return city;
  }

  /**
   * Sets the value of the 'city' field.
   * @param value the value to set.
   */
  public void setCity(java.lang.CharSequence value) {
    this.city = value;
  }

  /**
   * Gets the value of the 'district' field.
   * @return The value of the 'district' field.
   */
  public java.lang.CharSequence getDistrict() {
    return district;
  }

  /**
   * Sets the value of the 'district' field.
   * @param value the value to set.
   */
  public void setDistrict(java.lang.CharSequence value) {
    this.district = value;
  }

  /**
   * Gets the value of the 'street_address' field.
   * @return The value of the 'street_address' field.
   */
  public java.lang.CharSequence getStreetAddress() {
    return street_address;
  }

  /**
   * Sets the value of the 'street_address' field.
   * @param value the value to set.
   */
  public void setStreetAddress(java.lang.CharSequence value) {
    this.street_address = value;
  }

  /**
   * Gets the value of the 'contact_name' field.
   * @return The value of the 'contact_name' field.
   */
  public java.lang.CharSequence getContactName() {
    return contact_name;
  }

  /**
   * Sets the value of the 'contact_name' field.
   * @param value the value to set.
   */
  public void setContactName(java.lang.CharSequence value) {
    this.contact_name = value;
  }

  /**
   * Gets the value of the 'contract_cellphone' field.
   * @return The value of the 'contract_cellphone' field.
   */
  public java.lang.CharSequence getContractCellphone() {
    return contract_cellphone;
  }

  /**
   * Sets the value of the 'contract_cellphone' field.
   * @param value the value to set.
   */
  public void setContractCellphone(java.lang.CharSequence value) {
    this.contract_cellphone = value;
  }

  /**
   * Gets the value of the 'contact_phone' field.
   * @return The value of the 'contact_phone' field.
   */
  public java.lang.CharSequence getContactPhone() {
    return contact_phone;
  }

  /**
   * Sets the value of the 'contact_phone' field.
   * @param value the value to set.
   */
  public void setContactPhone(java.lang.CharSequence value) {
    this.contact_phone = value;
  }

  /**
   * Gets the value of the 'contact_email' field.
   * @return The value of the 'contact_email' field.
   */
  public java.lang.CharSequence getContactEmail() {
    return contact_email;
  }

  /**
   * Sets the value of the 'contact_email' field.
   * @param value the value to set.
   */
  public void setContactEmail(java.lang.CharSequence value) {
    this.contact_email = value;
  }

  /**
   * Gets the value of the 'concat_id_card_front_photo' field.
   * @return The value of the 'concat_id_card_front_photo' field.
   */
  public java.lang.Boolean getConcatIdCardFrontPhoto() {
    return concat_id_card_front_photo;
  }

  /**
   * Sets the value of the 'concat_id_card_front_photo' field.
   * @param value the value to set.
   */
  public void setConcatIdCardFrontPhoto(java.lang.Boolean value) {
    this.concat_id_card_front_photo = value;
  }

  /**
   * Gets the value of the 'legal_person_type' field.
   * @return The value of the 'legal_person_type' field.
   */
  public java.lang.Integer getLegalPersonType() {
    return legal_person_type;
  }

  /**
   * Sets the value of the 'legal_person_type' field.
   * @param value the value to set.
   */
  public void setLegalPersonType(java.lang.Integer value) {
    this.legal_person_type = value;
  }

  /**
   * Gets the value of the 'legal_person_register_no' field.
   * @return The value of the 'legal_person_register_no' field.
   */
  public java.lang.CharSequence getLegalPersonRegisterNo() {
    return legal_person_register_no;
  }

  /**
   * Sets the value of the 'legal_person_register_no' field.
   * @param value the value to set.
   */
  public void setLegalPersonRegisterNo(java.lang.CharSequence value) {
    this.legal_person_register_no = value;
  }

  /**
   * Gets the value of the 'business_license_photo' field.
   * @return The value of the 'business_license_photo' field.
   */
  public java.lang.Boolean getBusinessLicensePhoto() {
    return business_license_photo;
  }

  /**
   * Sets the value of the 'business_license_photo' field.
   * @param value the value to set.
   */
  public void setBusinessLicensePhoto(java.lang.Boolean value) {
    this.business_license_photo = value;
  }

  /**
   * Gets the value of the 'business' field.
   * @return The value of the 'business' field.
   */
  public java.lang.CharSequence getBusiness() {
    return business;
  }

  /**
   * Sets the value of the 'business' field.
   * @param value the value to set.
   */
  public void setBusiness(java.lang.CharSequence value) {
    this.business = value;
  }

  /**
   * Gets the value of the 'currency' field.
   * @return The value of the 'currency' field.
   */
  public java.lang.CharSequence getCurrency() {
    return currency;
  }

  /**
   * Sets the value of the 'currency' field.
   * @param value the value to set.
   */
  public void setCurrency(java.lang.CharSequence value) {
    this.currency = value;
  }

  /**
   * Gets the value of the 'owner_name' field.
   * @return The value of the 'owner_name' field.
   */
  public java.lang.CharSequence getOwnerName() {
    return owner_name;
  }

  /**
   * Sets the value of the 'owner_name' field.
   * @param value the value to set.
   */
  public void setOwnerName(java.lang.CharSequence value) {
    this.owner_name = value;
  }

  /**
   * Gets the value of the 'owner_cellphone' field.
   * @return The value of the 'owner_cellphone' field.
   */
  public java.lang.CharSequence getOwnerCellphone() {
    return owner_cellphone;
  }

  /**
   * Sets the value of the 'owner_cellphone' field.
   * @param value the value to set.
   */
  public void setOwnerCellphone(java.lang.CharSequence value) {
    this.owner_cellphone = value;
  }

  /**
   * Gets the value of the 'customer_phone' field.
   * @return The value of the 'customer_phone' field.
   */
  public java.lang.CharSequence getCustomerPhone() {
    return customer_phone;
  }

  /**
   * Sets the value of the 'customer_phone' field.
   * @param value the value to set.
   */
  public void setCustomerPhone(java.lang.CharSequence value) {
    this.customer_phone = value;
  }

  /**
   * Gets the value of the 'logo' field.
   * @return The value of the 'logo' field.
   */
  public java.lang.CharSequence getLogo() {
    return logo;
  }

  /**
   * Sets the value of the 'logo' field.
   * @param value the value to set.
   */
  public void setLogo(java.lang.CharSequence value) {
    this.logo = value;
  }

  /**
   * Gets the value of the 'business_name' field.
   * @return The value of the 'business_name' field.
   */
  public java.lang.CharSequence getBusinessName() {
    return business_name;
  }

  /**
   * Sets the value of the 'business_name' field.
   * @param value the value to set.
   */
  public void setBusinessName(java.lang.CharSequence value) {
    this.business_name = value;
  }

  /**
   * Gets the value of the 'concat_identity' field.
   * @return The value of the 'concat_identity' field.
   */
  public java.lang.CharSequence getConcatIdentity() {
    return concat_identity;
  }

  /**
   * Sets the value of the 'concat_identity' field.
   * @param value the value to set.
   */
  public void setConcatIdentity(java.lang.CharSequence value) {
    this.concat_identity = value;
  }

  /**
   * Gets the value of the 'merchant_type' field.
   * @return The value of the 'merchant_type' field.
   */
  public java.lang.Integer getMerchantType() {
    return merchant_type;
  }

  /**
   * Sets the value of the 'merchant_type' field.
   * @param value the value to set.
   */
  public void setMerchantType(java.lang.Integer value) {
    this.merchant_type = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return The value of the 'type' field.
   */
  public java.lang.Integer getType() {
    return type;
  }

  /**
   * Sets the value of the 'type' field.
   * @param value the value to set.
   */
  public void setType(java.lang.Integer value) {
    this.type = value;
  }

  /**
   * Gets the value of the 'photo' field.
   * @return The value of the 'photo' field.
   */
  public java.lang.Boolean getPhoto() {
    return photo;
  }

  /**
   * Sets the value of the 'photo' field.
   * @param value the value to set.
   */
  public void setPhoto(java.lang.Boolean value) {
    this.photo = value;
  }

  /**
   * Gets the value of the 'number' field.
   * @return The value of the 'number' field.
   */
  public java.lang.CharSequence getNumber() {
    return number;
  }

  /**
   * Sets the value of the 'number' field.
   * @param value the value to set.
   */
  public void setNumber(java.lang.CharSequence value) {
    this.number = value;
  }

  /**
   * Gets the value of the 'license_name' field.
   * @return The value of the 'license_name' field.
   */
  public java.lang.CharSequence getLicenseName() {
    return license_name;
  }

  /**
   * Sets the value of the 'license_name' field.
   * @param value the value to set.
   */
  public void setLicenseName(java.lang.CharSequence value) {
    this.license_name = value;
  }

  /**
   * Gets the value of the 'validity' field.
   * @return The value of the 'validity' field.
   */
  public java.lang.CharSequence getValidity() {
    return validity;
  }

  /**
   * Sets the value of the 'validity' field.
   * @param value the value to set.
   */
  public void setValidity(java.lang.CharSequence value) {
    this.validity = value;
  }

  /**
   * Gets the value of the 'address' field.
   * @return The value of the 'address' field.
   */
  public java.lang.CharSequence getAddress() {
    return address;
  }

  /**
   * Sets the value of the 'address' field.
   * @param value the value to set.
   */
  public void setAddress(java.lang.CharSequence value) {
    this.address = value;
  }

  /**
   * Gets the value of the 'registered_legal_person_name' field.
   * @return The value of the 'registered_legal_person_name' field.
   */
  public java.lang.CharSequence getRegisteredLegalPersonName() {
    return registered_legal_person_name;
  }

  /**
   * Sets the value of the 'registered_legal_person_name' field.
   * @param value the value to set.
   */
  public void setRegisteredLegalPersonName(java.lang.CharSequence value) {
    this.registered_legal_person_name = value;
  }

  /**
   * Gets the value of the 'letter_of_authorization' field.
   * @return The value of the 'letter_of_authorization' field.
   */
  public java.lang.Boolean getLetterOfAuthorization() {
    return letter_of_authorization;
  }

  /**
   * Sets the value of the 'letter_of_authorization' field.
   * @param value the value to set.
   */
  public void setLetterOfAuthorization(java.lang.Boolean value) {
    this.letter_of_authorization = value;
  }

  /**
   * Gets the value of the 'trade_license' field.
   * @return The value of the 'trade_license' field.
   */
  public java.lang.Boolean getTradeLicense() {
    return trade_license;
  }

  /**
   * Sets the value of the 'trade_license' field.
   * @param value the value to set.
   */
  public void setTradeLicense(java.lang.Boolean value) {
    this.trade_license = value;
  }

  /**
   * Gets the value of the 'legal_person_name' field.
   * @return The value of the 'legal_person_name' field.
   */
  public java.lang.CharSequence getLegalPersonName() {
    return legal_person_name;
  }

  /**
   * Sets the value of the 'legal_person_name' field.
   * @param value the value to set.
   */
  public void setLegalPersonName(java.lang.CharSequence value) {
    this.legal_person_name = value;
  }

  /**
   * Gets the value of the 'legal_person_id_type' field.
   * @return The value of the 'legal_person_id_type' field.
   */
  public java.lang.Integer getLegalPersonIdType() {
    return legal_person_id_type;
  }

  /**
   * Sets the value of the 'legal_person_id_type' field.
   * @param value the value to set.
   */
  public void setLegalPersonIdType(java.lang.Integer value) {
    this.legal_person_id_type = value;
  }

  /**
   * Gets the value of the 'legal_person_id_number' field.
   * @return The value of the 'legal_person_id_number' field.
   */
  public java.lang.CharSequence getLegalPersonIdNumber() {
    return legal_person_id_number;
  }

  /**
   * Sets the value of the 'legal_person_id_number' field.
   * @param value the value to set.
   */
  public void setLegalPersonIdNumber(java.lang.CharSequence value) {
    this.legal_person_id_number = value;
  }

  /**
   * Gets the value of the 'legal_person_id_card_front_photo' field.
   * @return The value of the 'legal_person_id_card_front_photo' field.
   */
  public java.lang.Boolean getLegalPersonIdCardFrontPhoto() {
    return legal_person_id_card_front_photo;
  }

  /**
   * Sets the value of the 'legal_person_id_card_front_photo' field.
   * @param value the value to set.
   */
  public void setLegalPersonIdCardFrontPhoto(java.lang.Boolean value) {
    this.legal_person_id_card_front_photo = value;
  }

  /**
   * Gets the value of the 'legal_person_id_card_back_photo' field.
   * @return The value of the 'legal_person_id_card_back_photo' field.
   */
  public java.lang.Boolean getLegalPersonIdCardBackPhoto() {
    return legal_person_id_card_back_photo;
  }

  /**
   * Sets the value of the 'legal_person_id_card_back_photo' field.
   * @param value the value to set.
   */
  public void setLegalPersonIdCardBackPhoto(java.lang.Boolean value) {
    this.legal_person_id_card_back_photo = value;
  }

  /**
   * Gets the value of the 'legal_person_id_card_address' field.
   * @return The value of the 'legal_person_id_card_address' field.
   */
  public java.lang.CharSequence getLegalPersonIdCardAddress() {
    return legal_person_id_card_address;
  }

  /**
   * Sets the value of the 'legal_person_id_card_address' field.
   * @param value the value to set.
   */
  public void setLegalPersonIdCardAddress(java.lang.CharSequence value) {
    this.legal_person_id_card_address = value;
  }

  /**
   * Gets the value of the 'legal_person_id_card_issuing_authority' field.
   * @return The value of the 'legal_person_id_card_issuing_authority' field.
   */
  public java.lang.CharSequence getLegalPersonIdCardIssuingAuthority() {
    return legal_person_id_card_issuing_authority;
  }

  /**
   * Sets the value of the 'legal_person_id_card_issuing_authority' field.
   * @param value the value to set.
   */
  public void setLegalPersonIdCardIssuingAuthority(java.lang.CharSequence value) {
    this.legal_person_id_card_issuing_authority = value;
  }

  /**
   * Creates a new ChangeMerchantSensor RecordBuilder.
   * @return A new ChangeMerchantSensor RecordBuilder
   */
  public static com.wosai.mc.avro.ChangeMerchantSensor.Builder newBuilder() {
    return new com.wosai.mc.avro.ChangeMerchantSensor.Builder();
  }

  /**
   * Creates a new ChangeMerchantSensor RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new ChangeMerchantSensor RecordBuilder
   */
  public static com.wosai.mc.avro.ChangeMerchantSensor.Builder newBuilder(com.wosai.mc.avro.ChangeMerchantSensor.Builder other) {
    return new com.wosai.mc.avro.ChangeMerchantSensor.Builder(other);
  }

  /**
   * Creates a new ChangeMerchantSensor RecordBuilder by copying an existing ChangeMerchantSensor instance.
   * @param other The existing instance to copy.
   * @return A new ChangeMerchantSensor RecordBuilder
   */
  public static com.wosai.mc.avro.ChangeMerchantSensor.Builder newBuilder(com.wosai.mc.avro.ChangeMerchantSensor other) {
    return new com.wosai.mc.avro.ChangeMerchantSensor.Builder(other);
  }

  /**
   * RecordBuilder for ChangeMerchantSensor instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<ChangeMerchantSensor>
    implements org.apache.avro.data.RecordBuilder<ChangeMerchantSensor> {

    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence merchant_sn;
    private java.lang.Long mtime;
    private java.lang.CharSequence operator;
    private java.lang.CharSequence platform;
    private java.lang.CharSequence name;
    private java.lang.CharSequence alias;
    private java.lang.CharSequence industry;
    private java.lang.Integer status;
    private java.lang.Integer rank;
    private java.lang.Integer withdraw_mode;
    private java.lang.CharSequence longitude;
    private java.lang.CharSequence latitude;
    private java.lang.CharSequence country;
    private java.lang.CharSequence province;
    private java.lang.CharSequence city;
    private java.lang.CharSequence district;
    private java.lang.CharSequence street_address;
    private java.lang.CharSequence contact_name;
    private java.lang.CharSequence contract_cellphone;
    private java.lang.CharSequence contact_phone;
    private java.lang.CharSequence contact_email;
    private java.lang.Boolean concat_id_card_front_photo;
    private java.lang.Integer legal_person_type;
    private java.lang.CharSequence legal_person_register_no;
    private java.lang.Boolean business_license_photo;
    private java.lang.CharSequence business;
    private java.lang.CharSequence currency;
    private java.lang.CharSequence owner_name;
    private java.lang.CharSequence owner_cellphone;
    private java.lang.CharSequence customer_phone;
    private java.lang.CharSequence logo;
    private java.lang.CharSequence business_name;
    private java.lang.CharSequence concat_identity;
    private java.lang.Integer merchant_type;
    private java.lang.Integer type;
    private java.lang.Boolean photo;
    private java.lang.CharSequence number;
    private java.lang.CharSequence license_name;
    private java.lang.CharSequence validity;
    private java.lang.CharSequence address;
    private java.lang.CharSequence registered_legal_person_name;
    private java.lang.Boolean letter_of_authorization;
    private java.lang.Boolean trade_license;
    private java.lang.CharSequence legal_person_name;
    private java.lang.Integer legal_person_id_type;
    private java.lang.CharSequence legal_person_id_number;
    private java.lang.Boolean legal_person_id_card_front_photo;
    private java.lang.Boolean legal_person_id_card_back_photo;
    private java.lang.CharSequence legal_person_id_card_address;
    private java.lang.CharSequence legal_person_id_card_issuing_authority;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.mc.avro.ChangeMerchantSensor.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.mtime)) {
        this.mtime = data().deepCopy(fields()[2].schema(), other.mtime);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.operator)) {
        this.operator = data().deepCopy(fields()[3].schema(), other.operator);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.platform)) {
        this.platform = data().deepCopy(fields()[4].schema(), other.platform);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.name)) {
        this.name = data().deepCopy(fields()[5].schema(), other.name);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.alias)) {
        this.alias = data().deepCopy(fields()[6].schema(), other.alias);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.industry)) {
        this.industry = data().deepCopy(fields()[7].schema(), other.industry);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.status)) {
        this.status = data().deepCopy(fields()[8].schema(), other.status);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.rank)) {
        this.rank = data().deepCopy(fields()[9].schema(), other.rank);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.withdraw_mode)) {
        this.withdraw_mode = data().deepCopy(fields()[10].schema(), other.withdraw_mode);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.longitude)) {
        this.longitude = data().deepCopy(fields()[11].schema(), other.longitude);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.latitude)) {
        this.latitude = data().deepCopy(fields()[12].schema(), other.latitude);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.country)) {
        this.country = data().deepCopy(fields()[13].schema(), other.country);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.province)) {
        this.province = data().deepCopy(fields()[14].schema(), other.province);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.city)) {
        this.city = data().deepCopy(fields()[15].schema(), other.city);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.district)) {
        this.district = data().deepCopy(fields()[16].schema(), other.district);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.street_address)) {
        this.street_address = data().deepCopy(fields()[17].schema(), other.street_address);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.contact_name)) {
        this.contact_name = data().deepCopy(fields()[18].schema(), other.contact_name);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.contract_cellphone)) {
        this.contract_cellphone = data().deepCopy(fields()[19].schema(), other.contract_cellphone);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.contact_phone)) {
        this.contact_phone = data().deepCopy(fields()[20].schema(), other.contact_phone);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.contact_email)) {
        this.contact_email = data().deepCopy(fields()[21].schema(), other.contact_email);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.concat_id_card_front_photo)) {
        this.concat_id_card_front_photo = data().deepCopy(fields()[22].schema(), other.concat_id_card_front_photo);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.legal_person_type)) {
        this.legal_person_type = data().deepCopy(fields()[23].schema(), other.legal_person_type);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.legal_person_register_no)) {
        this.legal_person_register_no = data().deepCopy(fields()[24].schema(), other.legal_person_register_no);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.business_license_photo)) {
        this.business_license_photo = data().deepCopy(fields()[25].schema(), other.business_license_photo);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.business)) {
        this.business = data().deepCopy(fields()[26].schema(), other.business);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.currency)) {
        this.currency = data().deepCopy(fields()[27].schema(), other.currency);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.owner_name)) {
        this.owner_name = data().deepCopy(fields()[28].schema(), other.owner_name);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.owner_cellphone)) {
        this.owner_cellphone = data().deepCopy(fields()[29].schema(), other.owner_cellphone);
        fieldSetFlags()[29] = true;
      }
      if (isValidValue(fields()[30], other.customer_phone)) {
        this.customer_phone = data().deepCopy(fields()[30].schema(), other.customer_phone);
        fieldSetFlags()[30] = true;
      }
      if (isValidValue(fields()[31], other.logo)) {
        this.logo = data().deepCopy(fields()[31].schema(), other.logo);
        fieldSetFlags()[31] = true;
      }
      if (isValidValue(fields()[32], other.business_name)) {
        this.business_name = data().deepCopy(fields()[32].schema(), other.business_name);
        fieldSetFlags()[32] = true;
      }
      if (isValidValue(fields()[33], other.concat_identity)) {
        this.concat_identity = data().deepCopy(fields()[33].schema(), other.concat_identity);
        fieldSetFlags()[33] = true;
      }
      if (isValidValue(fields()[34], other.merchant_type)) {
        this.merchant_type = data().deepCopy(fields()[34].schema(), other.merchant_type);
        fieldSetFlags()[34] = true;
      }
      if (isValidValue(fields()[35], other.type)) {
        this.type = data().deepCopy(fields()[35].schema(), other.type);
        fieldSetFlags()[35] = true;
      }
      if (isValidValue(fields()[36], other.photo)) {
        this.photo = data().deepCopy(fields()[36].schema(), other.photo);
        fieldSetFlags()[36] = true;
      }
      if (isValidValue(fields()[37], other.number)) {
        this.number = data().deepCopy(fields()[37].schema(), other.number);
        fieldSetFlags()[37] = true;
      }
      if (isValidValue(fields()[38], other.license_name)) {
        this.license_name = data().deepCopy(fields()[38].schema(), other.license_name);
        fieldSetFlags()[38] = true;
      }
      if (isValidValue(fields()[39], other.validity)) {
        this.validity = data().deepCopy(fields()[39].schema(), other.validity);
        fieldSetFlags()[39] = true;
      }
      if (isValidValue(fields()[40], other.address)) {
        this.address = data().deepCopy(fields()[40].schema(), other.address);
        fieldSetFlags()[40] = true;
      }
      if (isValidValue(fields()[41], other.registered_legal_person_name)) {
        this.registered_legal_person_name = data().deepCopy(fields()[41].schema(), other.registered_legal_person_name);
        fieldSetFlags()[41] = true;
      }
      if (isValidValue(fields()[42], other.letter_of_authorization)) {
        this.letter_of_authorization = data().deepCopy(fields()[42].schema(), other.letter_of_authorization);
        fieldSetFlags()[42] = true;
      }
      if (isValidValue(fields()[43], other.trade_license)) {
        this.trade_license = data().deepCopy(fields()[43].schema(), other.trade_license);
        fieldSetFlags()[43] = true;
      }
      if (isValidValue(fields()[44], other.legal_person_name)) {
        this.legal_person_name = data().deepCopy(fields()[44].schema(), other.legal_person_name);
        fieldSetFlags()[44] = true;
      }
      if (isValidValue(fields()[45], other.legal_person_id_type)) {
        this.legal_person_id_type = data().deepCopy(fields()[45].schema(), other.legal_person_id_type);
        fieldSetFlags()[45] = true;
      }
      if (isValidValue(fields()[46], other.legal_person_id_number)) {
        this.legal_person_id_number = data().deepCopy(fields()[46].schema(), other.legal_person_id_number);
        fieldSetFlags()[46] = true;
      }
      if (isValidValue(fields()[47], other.legal_person_id_card_front_photo)) {
        this.legal_person_id_card_front_photo = data().deepCopy(fields()[47].schema(), other.legal_person_id_card_front_photo);
        fieldSetFlags()[47] = true;
      }
      if (isValidValue(fields()[48], other.legal_person_id_card_back_photo)) {
        this.legal_person_id_card_back_photo = data().deepCopy(fields()[48].schema(), other.legal_person_id_card_back_photo);
        fieldSetFlags()[48] = true;
      }
      if (isValidValue(fields()[49], other.legal_person_id_card_address)) {
        this.legal_person_id_card_address = data().deepCopy(fields()[49].schema(), other.legal_person_id_card_address);
        fieldSetFlags()[49] = true;
      }
      if (isValidValue(fields()[50], other.legal_person_id_card_issuing_authority)) {
        this.legal_person_id_card_issuing_authority = data().deepCopy(fields()[50].schema(), other.legal_person_id_card_issuing_authority);
        fieldSetFlags()[50] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing ChangeMerchantSensor instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.mc.avro.ChangeMerchantSensor other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.mtime)) {
        this.mtime = data().deepCopy(fields()[2].schema(), other.mtime);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.operator)) {
        this.operator = data().deepCopy(fields()[3].schema(), other.operator);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.platform)) {
        this.platform = data().deepCopy(fields()[4].schema(), other.platform);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.name)) {
        this.name = data().deepCopy(fields()[5].schema(), other.name);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.alias)) {
        this.alias = data().deepCopy(fields()[6].schema(), other.alias);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.industry)) {
        this.industry = data().deepCopy(fields()[7].schema(), other.industry);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.status)) {
        this.status = data().deepCopy(fields()[8].schema(), other.status);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.rank)) {
        this.rank = data().deepCopy(fields()[9].schema(), other.rank);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.withdraw_mode)) {
        this.withdraw_mode = data().deepCopy(fields()[10].schema(), other.withdraw_mode);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.longitude)) {
        this.longitude = data().deepCopy(fields()[11].schema(), other.longitude);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.latitude)) {
        this.latitude = data().deepCopy(fields()[12].schema(), other.latitude);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.country)) {
        this.country = data().deepCopy(fields()[13].schema(), other.country);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.province)) {
        this.province = data().deepCopy(fields()[14].schema(), other.province);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.city)) {
        this.city = data().deepCopy(fields()[15].schema(), other.city);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.district)) {
        this.district = data().deepCopy(fields()[16].schema(), other.district);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.street_address)) {
        this.street_address = data().deepCopy(fields()[17].schema(), other.street_address);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.contact_name)) {
        this.contact_name = data().deepCopy(fields()[18].schema(), other.contact_name);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.contract_cellphone)) {
        this.contract_cellphone = data().deepCopy(fields()[19].schema(), other.contract_cellphone);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.contact_phone)) {
        this.contact_phone = data().deepCopy(fields()[20].schema(), other.contact_phone);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.contact_email)) {
        this.contact_email = data().deepCopy(fields()[21].schema(), other.contact_email);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.concat_id_card_front_photo)) {
        this.concat_id_card_front_photo = data().deepCopy(fields()[22].schema(), other.concat_id_card_front_photo);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.legal_person_type)) {
        this.legal_person_type = data().deepCopy(fields()[23].schema(), other.legal_person_type);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.legal_person_register_no)) {
        this.legal_person_register_no = data().deepCopy(fields()[24].schema(), other.legal_person_register_no);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.business_license_photo)) {
        this.business_license_photo = data().deepCopy(fields()[25].schema(), other.business_license_photo);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.business)) {
        this.business = data().deepCopy(fields()[26].schema(), other.business);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.currency)) {
        this.currency = data().deepCopy(fields()[27].schema(), other.currency);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.owner_name)) {
        this.owner_name = data().deepCopy(fields()[28].schema(), other.owner_name);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.owner_cellphone)) {
        this.owner_cellphone = data().deepCopy(fields()[29].schema(), other.owner_cellphone);
        fieldSetFlags()[29] = true;
      }
      if (isValidValue(fields()[30], other.customer_phone)) {
        this.customer_phone = data().deepCopy(fields()[30].schema(), other.customer_phone);
        fieldSetFlags()[30] = true;
      }
      if (isValidValue(fields()[31], other.logo)) {
        this.logo = data().deepCopy(fields()[31].schema(), other.logo);
        fieldSetFlags()[31] = true;
      }
      if (isValidValue(fields()[32], other.business_name)) {
        this.business_name = data().deepCopy(fields()[32].schema(), other.business_name);
        fieldSetFlags()[32] = true;
      }
      if (isValidValue(fields()[33], other.concat_identity)) {
        this.concat_identity = data().deepCopy(fields()[33].schema(), other.concat_identity);
        fieldSetFlags()[33] = true;
      }
      if (isValidValue(fields()[34], other.merchant_type)) {
        this.merchant_type = data().deepCopy(fields()[34].schema(), other.merchant_type);
        fieldSetFlags()[34] = true;
      }
      if (isValidValue(fields()[35], other.type)) {
        this.type = data().deepCopy(fields()[35].schema(), other.type);
        fieldSetFlags()[35] = true;
      }
      if (isValidValue(fields()[36], other.photo)) {
        this.photo = data().deepCopy(fields()[36].schema(), other.photo);
        fieldSetFlags()[36] = true;
      }
      if (isValidValue(fields()[37], other.number)) {
        this.number = data().deepCopy(fields()[37].schema(), other.number);
        fieldSetFlags()[37] = true;
      }
      if (isValidValue(fields()[38], other.license_name)) {
        this.license_name = data().deepCopy(fields()[38].schema(), other.license_name);
        fieldSetFlags()[38] = true;
      }
      if (isValidValue(fields()[39], other.validity)) {
        this.validity = data().deepCopy(fields()[39].schema(), other.validity);
        fieldSetFlags()[39] = true;
      }
      if (isValidValue(fields()[40], other.address)) {
        this.address = data().deepCopy(fields()[40].schema(), other.address);
        fieldSetFlags()[40] = true;
      }
      if (isValidValue(fields()[41], other.registered_legal_person_name)) {
        this.registered_legal_person_name = data().deepCopy(fields()[41].schema(), other.registered_legal_person_name);
        fieldSetFlags()[41] = true;
      }
      if (isValidValue(fields()[42], other.letter_of_authorization)) {
        this.letter_of_authorization = data().deepCopy(fields()[42].schema(), other.letter_of_authorization);
        fieldSetFlags()[42] = true;
      }
      if (isValidValue(fields()[43], other.trade_license)) {
        this.trade_license = data().deepCopy(fields()[43].schema(), other.trade_license);
        fieldSetFlags()[43] = true;
      }
      if (isValidValue(fields()[44], other.legal_person_name)) {
        this.legal_person_name = data().deepCopy(fields()[44].schema(), other.legal_person_name);
        fieldSetFlags()[44] = true;
      }
      if (isValidValue(fields()[45], other.legal_person_id_type)) {
        this.legal_person_id_type = data().deepCopy(fields()[45].schema(), other.legal_person_id_type);
        fieldSetFlags()[45] = true;
      }
      if (isValidValue(fields()[46], other.legal_person_id_number)) {
        this.legal_person_id_number = data().deepCopy(fields()[46].schema(), other.legal_person_id_number);
        fieldSetFlags()[46] = true;
      }
      if (isValidValue(fields()[47], other.legal_person_id_card_front_photo)) {
        this.legal_person_id_card_front_photo = data().deepCopy(fields()[47].schema(), other.legal_person_id_card_front_photo);
        fieldSetFlags()[47] = true;
      }
      if (isValidValue(fields()[48], other.legal_person_id_card_back_photo)) {
        this.legal_person_id_card_back_photo = data().deepCopy(fields()[48].schema(), other.legal_person_id_card_back_photo);
        fieldSetFlags()[48] = true;
      }
      if (isValidValue(fields()[49], other.legal_person_id_card_address)) {
        this.legal_person_id_card_address = data().deepCopy(fields()[49].schema(), other.legal_person_id_card_address);
        fieldSetFlags()[49] = true;
      }
      if (isValidValue(fields()[50], other.legal_person_id_card_issuing_authority)) {
        this.legal_person_id_card_issuing_authority = data().deepCopy(fields()[50].schema(), other.legal_person_id_card_issuing_authority);
        fieldSetFlags()[50] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public java.lang.Long getMtime() {
      return mtime;
    }

    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setMtime(java.lang.Long value) {
      validate(fields()[2], value);
      this.mtime = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearMtime() {
      mtime = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'operator' field.
      * @return The value.
      */
    public java.lang.CharSequence getOperator() {
      return operator;
    }

    /**
      * Sets the value of the 'operator' field.
      * @param value The value of 'operator'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setOperator(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.operator = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'operator' field has been set.
      * @return True if the 'operator' field has been set, false otherwise.
      */
    public boolean hasOperator() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'operator' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearOperator() {
      operator = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'platform' field.
      * @return The value.
      */
    public java.lang.CharSequence getPlatform() {
      return platform;
    }

    /**
      * Sets the value of the 'platform' field.
      * @param value The value of 'platform'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setPlatform(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.platform = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'platform' field has been set.
      * @return True if the 'platform' field has been set, false otherwise.
      */
    public boolean hasPlatform() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'platform' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearPlatform() {
      platform = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'name' field.
      * @return The value.
      */
    public java.lang.CharSequence getName() {
      return name;
    }

    /**
      * Sets the value of the 'name' field.
      * @param value The value of 'name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setName(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.name = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'name' field has been set.
      * @return True if the 'name' field has been set, false otherwise.
      */
    public boolean hasName() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearName() {
      name = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'alias' field.
      * @return The value.
      */
    public java.lang.CharSequence getAlias() {
      return alias;
    }

    /**
      * Sets the value of the 'alias' field.
      * @param value The value of 'alias'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setAlias(java.lang.CharSequence value) {
      validate(fields()[6], value);
      this.alias = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'alias' field has been set.
      * @return True if the 'alias' field has been set, false otherwise.
      */
    public boolean hasAlias() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'alias' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearAlias() {
      alias = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'industry' field.
      * @return The value.
      */
    public java.lang.CharSequence getIndustry() {
      return industry;
    }

    /**
      * Sets the value of the 'industry' field.
      * @param value The value of 'industry'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setIndustry(java.lang.CharSequence value) {
      validate(fields()[7], value);
      this.industry = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'industry' field has been set.
      * @return True if the 'industry' field has been set, false otherwise.
      */
    public boolean hasIndustry() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'industry' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearIndustry() {
      industry = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setStatus(java.lang.Integer value) {
      validate(fields()[8], value);
      this.status = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearStatus() {
      status = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'rank' field.
      * @return The value.
      */
    public java.lang.Integer getRank() {
      return rank;
    }

    /**
      * Sets the value of the 'rank' field.
      * @param value The value of 'rank'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setRank(java.lang.Integer value) {
      validate(fields()[9], value);
      this.rank = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'rank' field has been set.
      * @return True if the 'rank' field has been set, false otherwise.
      */
    public boolean hasRank() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'rank' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearRank() {
      rank = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'withdraw_mode' field.
      * @return The value.
      */
    public java.lang.Integer getWithdrawMode() {
      return withdraw_mode;
    }

    /**
      * Sets the value of the 'withdraw_mode' field.
      * @param value The value of 'withdraw_mode'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setWithdrawMode(java.lang.Integer value) {
      validate(fields()[10], value);
      this.withdraw_mode = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'withdraw_mode' field has been set.
      * @return True if the 'withdraw_mode' field has been set, false otherwise.
      */
    public boolean hasWithdrawMode() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'withdraw_mode' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearWithdrawMode() {
      withdraw_mode = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'longitude' field.
      * @return The value.
      */
    public java.lang.CharSequence getLongitude() {
      return longitude;
    }

    /**
      * Sets the value of the 'longitude' field.
      * @param value The value of 'longitude'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLongitude(java.lang.CharSequence value) {
      validate(fields()[11], value);
      this.longitude = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'longitude' field has been set.
      * @return True if the 'longitude' field has been set, false otherwise.
      */
    public boolean hasLongitude() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'longitude' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLongitude() {
      longitude = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'latitude' field.
      * @return The value.
      */
    public java.lang.CharSequence getLatitude() {
      return latitude;
    }

    /**
      * Sets the value of the 'latitude' field.
      * @param value The value of 'latitude'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLatitude(java.lang.CharSequence value) {
      validate(fields()[12], value);
      this.latitude = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'latitude' field has been set.
      * @return True if the 'latitude' field has been set, false otherwise.
      */
    public boolean hasLatitude() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'latitude' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLatitude() {
      latitude = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'country' field.
      * @return The value.
      */
    public java.lang.CharSequence getCountry() {
      return country;
    }

    /**
      * Sets the value of the 'country' field.
      * @param value The value of 'country'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setCountry(java.lang.CharSequence value) {
      validate(fields()[13], value);
      this.country = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'country' field has been set.
      * @return True if the 'country' field has been set, false otherwise.
      */
    public boolean hasCountry() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'country' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearCountry() {
      country = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'province' field.
      * @return The value.
      */
    public java.lang.CharSequence getProvince() {
      return province;
    }

    /**
      * Sets the value of the 'province' field.
      * @param value The value of 'province'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setProvince(java.lang.CharSequence value) {
      validate(fields()[14], value);
      this.province = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'province' field has been set.
      * @return True if the 'province' field has been set, false otherwise.
      */
    public boolean hasProvince() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'province' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearProvince() {
      province = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'city' field.
      * @return The value.
      */
    public java.lang.CharSequence getCity() {
      return city;
    }

    /**
      * Sets the value of the 'city' field.
      * @param value The value of 'city'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setCity(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.city = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'city' field has been set.
      * @return True if the 'city' field has been set, false otherwise.
      */
    public boolean hasCity() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'city' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearCity() {
      city = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'district' field.
      * @return The value.
      */
    public java.lang.CharSequence getDistrict() {
      return district;
    }

    /**
      * Sets the value of the 'district' field.
      * @param value The value of 'district'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setDistrict(java.lang.CharSequence value) {
      validate(fields()[16], value);
      this.district = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'district' field has been set.
      * @return True if the 'district' field has been set, false otherwise.
      */
    public boolean hasDistrict() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'district' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearDistrict() {
      district = null;
      fieldSetFlags()[16] = false;
      return this;
    }

    /**
      * Gets the value of the 'street_address' field.
      * @return The value.
      */
    public java.lang.CharSequence getStreetAddress() {
      return street_address;
    }

    /**
      * Sets the value of the 'street_address' field.
      * @param value The value of 'street_address'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setStreetAddress(java.lang.CharSequence value) {
      validate(fields()[17], value);
      this.street_address = value;
      fieldSetFlags()[17] = true;
      return this;
    }

    /**
      * Checks whether the 'street_address' field has been set.
      * @return True if the 'street_address' field has been set, false otherwise.
      */
    public boolean hasStreetAddress() {
      return fieldSetFlags()[17];
    }


    /**
      * Clears the value of the 'street_address' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearStreetAddress() {
      street_address = null;
      fieldSetFlags()[17] = false;
      return this;
    }

    /**
      * Gets the value of the 'contact_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getContactName() {
      return contact_name;
    }

    /**
      * Sets the value of the 'contact_name' field.
      * @param value The value of 'contact_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setContactName(java.lang.CharSequence value) {
      validate(fields()[18], value);
      this.contact_name = value;
      fieldSetFlags()[18] = true;
      return this;
    }

    /**
      * Checks whether the 'contact_name' field has been set.
      * @return True if the 'contact_name' field has been set, false otherwise.
      */
    public boolean hasContactName() {
      return fieldSetFlags()[18];
    }


    /**
      * Clears the value of the 'contact_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearContactName() {
      contact_name = null;
      fieldSetFlags()[18] = false;
      return this;
    }

    /**
      * Gets the value of the 'contract_cellphone' field.
      * @return The value.
      */
    public java.lang.CharSequence getContractCellphone() {
      return contract_cellphone;
    }

    /**
      * Sets the value of the 'contract_cellphone' field.
      * @param value The value of 'contract_cellphone'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setContractCellphone(java.lang.CharSequence value) {
      validate(fields()[19], value);
      this.contract_cellphone = value;
      fieldSetFlags()[19] = true;
      return this;
    }

    /**
      * Checks whether the 'contract_cellphone' field has been set.
      * @return True if the 'contract_cellphone' field has been set, false otherwise.
      */
    public boolean hasContractCellphone() {
      return fieldSetFlags()[19];
    }


    /**
      * Clears the value of the 'contract_cellphone' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearContractCellphone() {
      contract_cellphone = null;
      fieldSetFlags()[19] = false;
      return this;
    }

    /**
      * Gets the value of the 'contact_phone' field.
      * @return The value.
      */
    public java.lang.CharSequence getContactPhone() {
      return contact_phone;
    }

    /**
      * Sets the value of the 'contact_phone' field.
      * @param value The value of 'contact_phone'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setContactPhone(java.lang.CharSequence value) {
      validate(fields()[20], value);
      this.contact_phone = value;
      fieldSetFlags()[20] = true;
      return this;
    }

    /**
      * Checks whether the 'contact_phone' field has been set.
      * @return True if the 'contact_phone' field has been set, false otherwise.
      */
    public boolean hasContactPhone() {
      return fieldSetFlags()[20];
    }


    /**
      * Clears the value of the 'contact_phone' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearContactPhone() {
      contact_phone = null;
      fieldSetFlags()[20] = false;
      return this;
    }

    /**
      * Gets the value of the 'contact_email' field.
      * @return The value.
      */
    public java.lang.CharSequence getContactEmail() {
      return contact_email;
    }

    /**
      * Sets the value of the 'contact_email' field.
      * @param value The value of 'contact_email'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setContactEmail(java.lang.CharSequence value) {
      validate(fields()[21], value);
      this.contact_email = value;
      fieldSetFlags()[21] = true;
      return this;
    }

    /**
      * Checks whether the 'contact_email' field has been set.
      * @return True if the 'contact_email' field has been set, false otherwise.
      */
    public boolean hasContactEmail() {
      return fieldSetFlags()[21];
    }


    /**
      * Clears the value of the 'contact_email' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearContactEmail() {
      contact_email = null;
      fieldSetFlags()[21] = false;
      return this;
    }

    /**
      * Gets the value of the 'concat_id_card_front_photo' field.
      * @return The value.
      */
    public java.lang.Boolean getConcatIdCardFrontPhoto() {
      return concat_id_card_front_photo;
    }

    /**
      * Sets the value of the 'concat_id_card_front_photo' field.
      * @param value The value of 'concat_id_card_front_photo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setConcatIdCardFrontPhoto(java.lang.Boolean value) {
      validate(fields()[22], value);
      this.concat_id_card_front_photo = value;
      fieldSetFlags()[22] = true;
      return this;
    }

    /**
      * Checks whether the 'concat_id_card_front_photo' field has been set.
      * @return True if the 'concat_id_card_front_photo' field has been set, false otherwise.
      */
    public boolean hasConcatIdCardFrontPhoto() {
      return fieldSetFlags()[22];
    }


    /**
      * Clears the value of the 'concat_id_card_front_photo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearConcatIdCardFrontPhoto() {
      concat_id_card_front_photo = null;
      fieldSetFlags()[22] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_type' field.
      * @return The value.
      */
    public java.lang.Integer getLegalPersonType() {
      return legal_person_type;
    }

    /**
      * Sets the value of the 'legal_person_type' field.
      * @param value The value of 'legal_person_type'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonType(java.lang.Integer value) {
      validate(fields()[23], value);
      this.legal_person_type = value;
      fieldSetFlags()[23] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_type' field has been set.
      * @return True if the 'legal_person_type' field has been set, false otherwise.
      */
    public boolean hasLegalPersonType() {
      return fieldSetFlags()[23];
    }


    /**
      * Clears the value of the 'legal_person_type' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonType() {
      legal_person_type = null;
      fieldSetFlags()[23] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_register_no' field.
      * @return The value.
      */
    public java.lang.CharSequence getLegalPersonRegisterNo() {
      return legal_person_register_no;
    }

    /**
      * Sets the value of the 'legal_person_register_no' field.
      * @param value The value of 'legal_person_register_no'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonRegisterNo(java.lang.CharSequence value) {
      validate(fields()[24], value);
      this.legal_person_register_no = value;
      fieldSetFlags()[24] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_register_no' field has been set.
      * @return True if the 'legal_person_register_no' field has been set, false otherwise.
      */
    public boolean hasLegalPersonRegisterNo() {
      return fieldSetFlags()[24];
    }


    /**
      * Clears the value of the 'legal_person_register_no' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonRegisterNo() {
      legal_person_register_no = null;
      fieldSetFlags()[24] = false;
      return this;
    }

    /**
      * Gets the value of the 'business_license_photo' field.
      * @return The value.
      */
    public java.lang.Boolean getBusinessLicensePhoto() {
      return business_license_photo;
    }

    /**
      * Sets the value of the 'business_license_photo' field.
      * @param value The value of 'business_license_photo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setBusinessLicensePhoto(java.lang.Boolean value) {
      validate(fields()[25], value);
      this.business_license_photo = value;
      fieldSetFlags()[25] = true;
      return this;
    }

    /**
      * Checks whether the 'business_license_photo' field has been set.
      * @return True if the 'business_license_photo' field has been set, false otherwise.
      */
    public boolean hasBusinessLicensePhoto() {
      return fieldSetFlags()[25];
    }


    /**
      * Clears the value of the 'business_license_photo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearBusinessLicensePhoto() {
      business_license_photo = null;
      fieldSetFlags()[25] = false;
      return this;
    }

    /**
      * Gets the value of the 'business' field.
      * @return The value.
      */
    public java.lang.CharSequence getBusiness() {
      return business;
    }

    /**
      * Sets the value of the 'business' field.
      * @param value The value of 'business'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setBusiness(java.lang.CharSequence value) {
      validate(fields()[26], value);
      this.business = value;
      fieldSetFlags()[26] = true;
      return this;
    }

    /**
      * Checks whether the 'business' field has been set.
      * @return True if the 'business' field has been set, false otherwise.
      */
    public boolean hasBusiness() {
      return fieldSetFlags()[26];
    }


    /**
      * Clears the value of the 'business' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearBusiness() {
      business = null;
      fieldSetFlags()[26] = false;
      return this;
    }

    /**
      * Gets the value of the 'currency' field.
      * @return The value.
      */
    public java.lang.CharSequence getCurrency() {
      return currency;
    }

    /**
      * Sets the value of the 'currency' field.
      * @param value The value of 'currency'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setCurrency(java.lang.CharSequence value) {
      validate(fields()[27], value);
      this.currency = value;
      fieldSetFlags()[27] = true;
      return this;
    }

    /**
      * Checks whether the 'currency' field has been set.
      * @return True if the 'currency' field has been set, false otherwise.
      */
    public boolean hasCurrency() {
      return fieldSetFlags()[27];
    }


    /**
      * Clears the value of the 'currency' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearCurrency() {
      currency = null;
      fieldSetFlags()[27] = false;
      return this;
    }

    /**
      * Gets the value of the 'owner_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getOwnerName() {
      return owner_name;
    }

    /**
      * Sets the value of the 'owner_name' field.
      * @param value The value of 'owner_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setOwnerName(java.lang.CharSequence value) {
      validate(fields()[28], value);
      this.owner_name = value;
      fieldSetFlags()[28] = true;
      return this;
    }

    /**
      * Checks whether the 'owner_name' field has been set.
      * @return True if the 'owner_name' field has been set, false otherwise.
      */
    public boolean hasOwnerName() {
      return fieldSetFlags()[28];
    }


    /**
      * Clears the value of the 'owner_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearOwnerName() {
      owner_name = null;
      fieldSetFlags()[28] = false;
      return this;
    }

    /**
      * Gets the value of the 'owner_cellphone' field.
      * @return The value.
      */
    public java.lang.CharSequence getOwnerCellphone() {
      return owner_cellphone;
    }

    /**
      * Sets the value of the 'owner_cellphone' field.
      * @param value The value of 'owner_cellphone'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setOwnerCellphone(java.lang.CharSequence value) {
      validate(fields()[29], value);
      this.owner_cellphone = value;
      fieldSetFlags()[29] = true;
      return this;
    }

    /**
      * Checks whether the 'owner_cellphone' field has been set.
      * @return True if the 'owner_cellphone' field has been set, false otherwise.
      */
    public boolean hasOwnerCellphone() {
      return fieldSetFlags()[29];
    }


    /**
      * Clears the value of the 'owner_cellphone' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearOwnerCellphone() {
      owner_cellphone = null;
      fieldSetFlags()[29] = false;
      return this;
    }

    /**
      * Gets the value of the 'customer_phone' field.
      * @return The value.
      */
    public java.lang.CharSequence getCustomerPhone() {
      return customer_phone;
    }

    /**
      * Sets the value of the 'customer_phone' field.
      * @param value The value of 'customer_phone'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setCustomerPhone(java.lang.CharSequence value) {
      validate(fields()[30], value);
      this.customer_phone = value;
      fieldSetFlags()[30] = true;
      return this;
    }

    /**
      * Checks whether the 'customer_phone' field has been set.
      * @return True if the 'customer_phone' field has been set, false otherwise.
      */
    public boolean hasCustomerPhone() {
      return fieldSetFlags()[30];
    }


    /**
      * Clears the value of the 'customer_phone' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearCustomerPhone() {
      customer_phone = null;
      fieldSetFlags()[30] = false;
      return this;
    }

    /**
      * Gets the value of the 'logo' field.
      * @return The value.
      */
    public java.lang.CharSequence getLogo() {
      return logo;
    }

    /**
      * Sets the value of the 'logo' field.
      * @param value The value of 'logo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLogo(java.lang.CharSequence value) {
      validate(fields()[31], value);
      this.logo = value;
      fieldSetFlags()[31] = true;
      return this;
    }

    /**
      * Checks whether the 'logo' field has been set.
      * @return True if the 'logo' field has been set, false otherwise.
      */
    public boolean hasLogo() {
      return fieldSetFlags()[31];
    }


    /**
      * Clears the value of the 'logo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLogo() {
      logo = null;
      fieldSetFlags()[31] = false;
      return this;
    }

    /**
      * Gets the value of the 'business_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getBusinessName() {
      return business_name;
    }

    /**
      * Sets the value of the 'business_name' field.
      * @param value The value of 'business_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setBusinessName(java.lang.CharSequence value) {
      validate(fields()[32], value);
      this.business_name = value;
      fieldSetFlags()[32] = true;
      return this;
    }

    /**
      * Checks whether the 'business_name' field has been set.
      * @return True if the 'business_name' field has been set, false otherwise.
      */
    public boolean hasBusinessName() {
      return fieldSetFlags()[32];
    }


    /**
      * Clears the value of the 'business_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearBusinessName() {
      business_name = null;
      fieldSetFlags()[32] = false;
      return this;
    }

    /**
      * Gets the value of the 'concat_identity' field.
      * @return The value.
      */
    public java.lang.CharSequence getConcatIdentity() {
      return concat_identity;
    }

    /**
      * Sets the value of the 'concat_identity' field.
      * @param value The value of 'concat_identity'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setConcatIdentity(java.lang.CharSequence value) {
      validate(fields()[33], value);
      this.concat_identity = value;
      fieldSetFlags()[33] = true;
      return this;
    }

    /**
      * Checks whether the 'concat_identity' field has been set.
      * @return True if the 'concat_identity' field has been set, false otherwise.
      */
    public boolean hasConcatIdentity() {
      return fieldSetFlags()[33];
    }


    /**
      * Clears the value of the 'concat_identity' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearConcatIdentity() {
      concat_identity = null;
      fieldSetFlags()[33] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_type' field.
      * @return The value.
      */
    public java.lang.Integer getMerchantType() {
      return merchant_type;
    }

    /**
      * Sets the value of the 'merchant_type' field.
      * @param value The value of 'merchant_type'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setMerchantType(java.lang.Integer value) {
      validate(fields()[34], value);
      this.merchant_type = value;
      fieldSetFlags()[34] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_type' field has been set.
      * @return True if the 'merchant_type' field has been set, false otherwise.
      */
    public boolean hasMerchantType() {
      return fieldSetFlags()[34];
    }


    /**
      * Clears the value of the 'merchant_type' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearMerchantType() {
      merchant_type = null;
      fieldSetFlags()[34] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * @return The value.
      */
    public java.lang.Integer getType() {
      return type;
    }

    /**
      * Sets the value of the 'type' field.
      * @param value The value of 'type'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setType(java.lang.Integer value) {
      validate(fields()[35], value);
      this.type = value;
      fieldSetFlags()[35] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[35];
    }


    /**
      * Clears the value of the 'type' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearType() {
      type = null;
      fieldSetFlags()[35] = false;
      return this;
    }

    /**
      * Gets the value of the 'photo' field.
      * @return The value.
      */
    public java.lang.Boolean getPhoto() {
      return photo;
    }

    /**
      * Sets the value of the 'photo' field.
      * @param value The value of 'photo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setPhoto(java.lang.Boolean value) {
      validate(fields()[36], value);
      this.photo = value;
      fieldSetFlags()[36] = true;
      return this;
    }

    /**
      * Checks whether the 'photo' field has been set.
      * @return True if the 'photo' field has been set, false otherwise.
      */
    public boolean hasPhoto() {
      return fieldSetFlags()[36];
    }


    /**
      * Clears the value of the 'photo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearPhoto() {
      photo = null;
      fieldSetFlags()[36] = false;
      return this;
    }

    /**
      * Gets the value of the 'number' field.
      * @return The value.
      */
    public java.lang.CharSequence getNumber() {
      return number;
    }

    /**
      * Sets the value of the 'number' field.
      * @param value The value of 'number'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setNumber(java.lang.CharSequence value) {
      validate(fields()[37], value);
      this.number = value;
      fieldSetFlags()[37] = true;
      return this;
    }

    /**
      * Checks whether the 'number' field has been set.
      * @return True if the 'number' field has been set, false otherwise.
      */
    public boolean hasNumber() {
      return fieldSetFlags()[37];
    }


    /**
      * Clears the value of the 'number' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearNumber() {
      number = null;
      fieldSetFlags()[37] = false;
      return this;
    }

    /**
      * Gets the value of the 'license_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getLicenseName() {
      return license_name;
    }

    /**
      * Sets the value of the 'license_name' field.
      * @param value The value of 'license_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLicenseName(java.lang.CharSequence value) {
      validate(fields()[38], value);
      this.license_name = value;
      fieldSetFlags()[38] = true;
      return this;
    }

    /**
      * Checks whether the 'license_name' field has been set.
      * @return True if the 'license_name' field has been set, false otherwise.
      */
    public boolean hasLicenseName() {
      return fieldSetFlags()[38];
    }


    /**
      * Clears the value of the 'license_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLicenseName() {
      license_name = null;
      fieldSetFlags()[38] = false;
      return this;
    }

    /**
      * Gets the value of the 'validity' field.
      * @return The value.
      */
    public java.lang.CharSequence getValidity() {
      return validity;
    }

    /**
      * Sets the value of the 'validity' field.
      * @param value The value of 'validity'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setValidity(java.lang.CharSequence value) {
      validate(fields()[39], value);
      this.validity = value;
      fieldSetFlags()[39] = true;
      return this;
    }

    /**
      * Checks whether the 'validity' field has been set.
      * @return True if the 'validity' field has been set, false otherwise.
      */
    public boolean hasValidity() {
      return fieldSetFlags()[39];
    }


    /**
      * Clears the value of the 'validity' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearValidity() {
      validity = null;
      fieldSetFlags()[39] = false;
      return this;
    }

    /**
      * Gets the value of the 'address' field.
      * @return The value.
      */
    public java.lang.CharSequence getAddress() {
      return address;
    }

    /**
      * Sets the value of the 'address' field.
      * @param value The value of 'address'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setAddress(java.lang.CharSequence value) {
      validate(fields()[40], value);
      this.address = value;
      fieldSetFlags()[40] = true;
      return this;
    }

    /**
      * Checks whether the 'address' field has been set.
      * @return True if the 'address' field has been set, false otherwise.
      */
    public boolean hasAddress() {
      return fieldSetFlags()[40];
    }


    /**
      * Clears the value of the 'address' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearAddress() {
      address = null;
      fieldSetFlags()[40] = false;
      return this;
    }

    /**
      * Gets the value of the 'registered_legal_person_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getRegisteredLegalPersonName() {
      return registered_legal_person_name;
    }

    /**
      * Sets the value of the 'registered_legal_person_name' field.
      * @param value The value of 'registered_legal_person_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setRegisteredLegalPersonName(java.lang.CharSequence value) {
      validate(fields()[41], value);
      this.registered_legal_person_name = value;
      fieldSetFlags()[41] = true;
      return this;
    }

    /**
      * Checks whether the 'registered_legal_person_name' field has been set.
      * @return True if the 'registered_legal_person_name' field has been set, false otherwise.
      */
    public boolean hasRegisteredLegalPersonName() {
      return fieldSetFlags()[41];
    }


    /**
      * Clears the value of the 'registered_legal_person_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearRegisteredLegalPersonName() {
      registered_legal_person_name = null;
      fieldSetFlags()[41] = false;
      return this;
    }

    /**
      * Gets the value of the 'letter_of_authorization' field.
      * @return The value.
      */
    public java.lang.Boolean getLetterOfAuthorization() {
      return letter_of_authorization;
    }

    /**
      * Sets the value of the 'letter_of_authorization' field.
      * @param value The value of 'letter_of_authorization'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLetterOfAuthorization(java.lang.Boolean value) {
      validate(fields()[42], value);
      this.letter_of_authorization = value;
      fieldSetFlags()[42] = true;
      return this;
    }

    /**
      * Checks whether the 'letter_of_authorization' field has been set.
      * @return True if the 'letter_of_authorization' field has been set, false otherwise.
      */
    public boolean hasLetterOfAuthorization() {
      return fieldSetFlags()[42];
    }


    /**
      * Clears the value of the 'letter_of_authorization' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLetterOfAuthorization() {
      letter_of_authorization = null;
      fieldSetFlags()[42] = false;
      return this;
    }

    /**
      * Gets the value of the 'trade_license' field.
      * @return The value.
      */
    public java.lang.Boolean getTradeLicense() {
      return trade_license;
    }

    /**
      * Sets the value of the 'trade_license' field.
      * @param value The value of 'trade_license'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setTradeLicense(java.lang.Boolean value) {
      validate(fields()[43], value);
      this.trade_license = value;
      fieldSetFlags()[43] = true;
      return this;
    }

    /**
      * Checks whether the 'trade_license' field has been set.
      * @return True if the 'trade_license' field has been set, false otherwise.
      */
    public boolean hasTradeLicense() {
      return fieldSetFlags()[43];
    }


    /**
      * Clears the value of the 'trade_license' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearTradeLicense() {
      trade_license = null;
      fieldSetFlags()[43] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getLegalPersonName() {
      return legal_person_name;
    }

    /**
      * Sets the value of the 'legal_person_name' field.
      * @param value The value of 'legal_person_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonName(java.lang.CharSequence value) {
      validate(fields()[44], value);
      this.legal_person_name = value;
      fieldSetFlags()[44] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_name' field has been set.
      * @return True if the 'legal_person_name' field has been set, false otherwise.
      */
    public boolean hasLegalPersonName() {
      return fieldSetFlags()[44];
    }


    /**
      * Clears the value of the 'legal_person_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonName() {
      legal_person_name = null;
      fieldSetFlags()[44] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_id_type' field.
      * @return The value.
      */
    public java.lang.Integer getLegalPersonIdType() {
      return legal_person_id_type;
    }

    /**
      * Sets the value of the 'legal_person_id_type' field.
      * @param value The value of 'legal_person_id_type'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonIdType(java.lang.Integer value) {
      validate(fields()[45], value);
      this.legal_person_id_type = value;
      fieldSetFlags()[45] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_id_type' field has been set.
      * @return True if the 'legal_person_id_type' field has been set, false otherwise.
      */
    public boolean hasLegalPersonIdType() {
      return fieldSetFlags()[45];
    }


    /**
      * Clears the value of the 'legal_person_id_type' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonIdType() {
      legal_person_id_type = null;
      fieldSetFlags()[45] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_id_number' field.
      * @return The value.
      */
    public java.lang.CharSequence getLegalPersonIdNumber() {
      return legal_person_id_number;
    }

    /**
      * Sets the value of the 'legal_person_id_number' field.
      * @param value The value of 'legal_person_id_number'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonIdNumber(java.lang.CharSequence value) {
      validate(fields()[46], value);
      this.legal_person_id_number = value;
      fieldSetFlags()[46] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_id_number' field has been set.
      * @return True if the 'legal_person_id_number' field has been set, false otherwise.
      */
    public boolean hasLegalPersonIdNumber() {
      return fieldSetFlags()[46];
    }


    /**
      * Clears the value of the 'legal_person_id_number' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonIdNumber() {
      legal_person_id_number = null;
      fieldSetFlags()[46] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_id_card_front_photo' field.
      * @return The value.
      */
    public java.lang.Boolean getLegalPersonIdCardFrontPhoto() {
      return legal_person_id_card_front_photo;
    }

    /**
      * Sets the value of the 'legal_person_id_card_front_photo' field.
      * @param value The value of 'legal_person_id_card_front_photo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonIdCardFrontPhoto(java.lang.Boolean value) {
      validate(fields()[47], value);
      this.legal_person_id_card_front_photo = value;
      fieldSetFlags()[47] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_id_card_front_photo' field has been set.
      * @return True if the 'legal_person_id_card_front_photo' field has been set, false otherwise.
      */
    public boolean hasLegalPersonIdCardFrontPhoto() {
      return fieldSetFlags()[47];
    }


    /**
      * Clears the value of the 'legal_person_id_card_front_photo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonIdCardFrontPhoto() {
      legal_person_id_card_front_photo = null;
      fieldSetFlags()[47] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_id_card_back_photo' field.
      * @return The value.
      */
    public java.lang.Boolean getLegalPersonIdCardBackPhoto() {
      return legal_person_id_card_back_photo;
    }

    /**
      * Sets the value of the 'legal_person_id_card_back_photo' field.
      * @param value The value of 'legal_person_id_card_back_photo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonIdCardBackPhoto(java.lang.Boolean value) {
      validate(fields()[48], value);
      this.legal_person_id_card_back_photo = value;
      fieldSetFlags()[48] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_id_card_back_photo' field has been set.
      * @return True if the 'legal_person_id_card_back_photo' field has been set, false otherwise.
      */
    public boolean hasLegalPersonIdCardBackPhoto() {
      return fieldSetFlags()[48];
    }


    /**
      * Clears the value of the 'legal_person_id_card_back_photo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonIdCardBackPhoto() {
      legal_person_id_card_back_photo = null;
      fieldSetFlags()[48] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_id_card_address' field.
      * @return The value.
      */
    public java.lang.CharSequence getLegalPersonIdCardAddress() {
      return legal_person_id_card_address;
    }

    /**
      * Sets the value of the 'legal_person_id_card_address' field.
      * @param value The value of 'legal_person_id_card_address'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonIdCardAddress(java.lang.CharSequence value) {
      validate(fields()[49], value);
      this.legal_person_id_card_address = value;
      fieldSetFlags()[49] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_id_card_address' field has been set.
      * @return True if the 'legal_person_id_card_address' field has been set, false otherwise.
      */
    public boolean hasLegalPersonIdCardAddress() {
      return fieldSetFlags()[49];
    }


    /**
      * Clears the value of the 'legal_person_id_card_address' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonIdCardAddress() {
      legal_person_id_card_address = null;
      fieldSetFlags()[49] = false;
      return this;
    }

    /**
      * Gets the value of the 'legal_person_id_card_issuing_authority' field.
      * @return The value.
      */
    public java.lang.CharSequence getLegalPersonIdCardIssuingAuthority() {
      return legal_person_id_card_issuing_authority;
    }

    /**
      * Sets the value of the 'legal_person_id_card_issuing_authority' field.
      * @param value The value of 'legal_person_id_card_issuing_authority'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder setLegalPersonIdCardIssuingAuthority(java.lang.CharSequence value) {
      validate(fields()[50], value);
      this.legal_person_id_card_issuing_authority = value;
      fieldSetFlags()[50] = true;
      return this;
    }

    /**
      * Checks whether the 'legal_person_id_card_issuing_authority' field has been set.
      * @return True if the 'legal_person_id_card_issuing_authority' field has been set, false otherwise.
      */
    public boolean hasLegalPersonIdCardIssuingAuthority() {
      return fieldSetFlags()[50];
    }


    /**
      * Clears the value of the 'legal_person_id_card_issuing_authority' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeMerchantSensor.Builder clearLegalPersonIdCardIssuingAuthority() {
      legal_person_id_card_issuing_authority = null;
      fieldSetFlags()[50] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ChangeMerchantSensor build() {
      try {
        ChangeMerchantSensor record = new ChangeMerchantSensor();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_sn = fieldSetFlags()[1] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.mtime = fieldSetFlags()[2] ? this.mtime : (java.lang.Long) defaultValue(fields()[2]);
        record.operator = fieldSetFlags()[3] ? this.operator : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.platform = fieldSetFlags()[4] ? this.platform : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.name = fieldSetFlags()[5] ? this.name : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.alias = fieldSetFlags()[6] ? this.alias : (java.lang.CharSequence) defaultValue(fields()[6]);
        record.industry = fieldSetFlags()[7] ? this.industry : (java.lang.CharSequence) defaultValue(fields()[7]);
        record.status = fieldSetFlags()[8] ? this.status : (java.lang.Integer) defaultValue(fields()[8]);
        record.rank = fieldSetFlags()[9] ? this.rank : (java.lang.Integer) defaultValue(fields()[9]);
        record.withdraw_mode = fieldSetFlags()[10] ? this.withdraw_mode : (java.lang.Integer) defaultValue(fields()[10]);
        record.longitude = fieldSetFlags()[11] ? this.longitude : (java.lang.CharSequence) defaultValue(fields()[11]);
        record.latitude = fieldSetFlags()[12] ? this.latitude : (java.lang.CharSequence) defaultValue(fields()[12]);
        record.country = fieldSetFlags()[13] ? this.country : (java.lang.CharSequence) defaultValue(fields()[13]);
        record.province = fieldSetFlags()[14] ? this.province : (java.lang.CharSequence) defaultValue(fields()[14]);
        record.city = fieldSetFlags()[15] ? this.city : (java.lang.CharSequence) defaultValue(fields()[15]);
        record.district = fieldSetFlags()[16] ? this.district : (java.lang.CharSequence) defaultValue(fields()[16]);
        record.street_address = fieldSetFlags()[17] ? this.street_address : (java.lang.CharSequence) defaultValue(fields()[17]);
        record.contact_name = fieldSetFlags()[18] ? this.contact_name : (java.lang.CharSequence) defaultValue(fields()[18]);
        record.contract_cellphone = fieldSetFlags()[19] ? this.contract_cellphone : (java.lang.CharSequence) defaultValue(fields()[19]);
        record.contact_phone = fieldSetFlags()[20] ? this.contact_phone : (java.lang.CharSequence) defaultValue(fields()[20]);
        record.contact_email = fieldSetFlags()[21] ? this.contact_email : (java.lang.CharSequence) defaultValue(fields()[21]);
        record.concat_id_card_front_photo = fieldSetFlags()[22] ? this.concat_id_card_front_photo : (java.lang.Boolean) defaultValue(fields()[22]);
        record.legal_person_type = fieldSetFlags()[23] ? this.legal_person_type : (java.lang.Integer) defaultValue(fields()[23]);
        record.legal_person_register_no = fieldSetFlags()[24] ? this.legal_person_register_no : (java.lang.CharSequence) defaultValue(fields()[24]);
        record.business_license_photo = fieldSetFlags()[25] ? this.business_license_photo : (java.lang.Boolean) defaultValue(fields()[25]);
        record.business = fieldSetFlags()[26] ? this.business : (java.lang.CharSequence) defaultValue(fields()[26]);
        record.currency = fieldSetFlags()[27] ? this.currency : (java.lang.CharSequence) defaultValue(fields()[27]);
        record.owner_name = fieldSetFlags()[28] ? this.owner_name : (java.lang.CharSequence) defaultValue(fields()[28]);
        record.owner_cellphone = fieldSetFlags()[29] ? this.owner_cellphone : (java.lang.CharSequence) defaultValue(fields()[29]);
        record.customer_phone = fieldSetFlags()[30] ? this.customer_phone : (java.lang.CharSequence) defaultValue(fields()[30]);
        record.logo = fieldSetFlags()[31] ? this.logo : (java.lang.CharSequence) defaultValue(fields()[31]);
        record.business_name = fieldSetFlags()[32] ? this.business_name : (java.lang.CharSequence) defaultValue(fields()[32]);
        record.concat_identity = fieldSetFlags()[33] ? this.concat_identity : (java.lang.CharSequence) defaultValue(fields()[33]);
        record.merchant_type = fieldSetFlags()[34] ? this.merchant_type : (java.lang.Integer) defaultValue(fields()[34]);
        record.type = fieldSetFlags()[35] ? this.type : (java.lang.Integer) defaultValue(fields()[35]);
        record.photo = fieldSetFlags()[36] ? this.photo : (java.lang.Boolean) defaultValue(fields()[36]);
        record.number = fieldSetFlags()[37] ? this.number : (java.lang.CharSequence) defaultValue(fields()[37]);
        record.license_name = fieldSetFlags()[38] ? this.license_name : (java.lang.CharSequence) defaultValue(fields()[38]);
        record.validity = fieldSetFlags()[39] ? this.validity : (java.lang.CharSequence) defaultValue(fields()[39]);
        record.address = fieldSetFlags()[40] ? this.address : (java.lang.CharSequence) defaultValue(fields()[40]);
        record.registered_legal_person_name = fieldSetFlags()[41] ? this.registered_legal_person_name : (java.lang.CharSequence) defaultValue(fields()[41]);
        record.letter_of_authorization = fieldSetFlags()[42] ? this.letter_of_authorization : (java.lang.Boolean) defaultValue(fields()[42]);
        record.trade_license = fieldSetFlags()[43] ? this.trade_license : (java.lang.Boolean) defaultValue(fields()[43]);
        record.legal_person_name = fieldSetFlags()[44] ? this.legal_person_name : (java.lang.CharSequence) defaultValue(fields()[44]);
        record.legal_person_id_type = fieldSetFlags()[45] ? this.legal_person_id_type : (java.lang.Integer) defaultValue(fields()[45]);
        record.legal_person_id_number = fieldSetFlags()[46] ? this.legal_person_id_number : (java.lang.CharSequence) defaultValue(fields()[46]);
        record.legal_person_id_card_front_photo = fieldSetFlags()[47] ? this.legal_person_id_card_front_photo : (java.lang.Boolean) defaultValue(fields()[47]);
        record.legal_person_id_card_back_photo = fieldSetFlags()[48] ? this.legal_person_id_card_back_photo : (java.lang.Boolean) defaultValue(fields()[48]);
        record.legal_person_id_card_address = fieldSetFlags()[49] ? this.legal_person_id_card_address : (java.lang.CharSequence) defaultValue(fields()[49]);
        record.legal_person_id_card_issuing_authority = fieldSetFlags()[50] ? this.legal_person_id_card_issuing_authority : (java.lang.CharSequence) defaultValue(fields()[50]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<ChangeMerchantSensor>
    WRITER$ = (org.apache.avro.io.DatumWriter<ChangeMerchantSensor>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<ChangeMerchantSensor>
    READER$ = (org.apache.avro.io.DatumReader<ChangeMerchantSensor>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
