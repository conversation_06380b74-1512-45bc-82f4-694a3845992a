/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.mc.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class ChangeStoreSensor extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -3199848849202857091L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"ChangeStoreSensor\",\"namespace\":\"com.wosai.mc.avro\",\"fields\":[{\"name\":\"merchant_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_sn\",\"type\":[\"string\",\"null\"]},{\"name\":\"store_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"store_sn\",\"type\":[\"string\",\"null\"]},{\"name\":\"mtime\",\"type\":[\"long\",\"null\"]},{\"name\":\"operator\",\"type\":[\"string\",\"null\"]},{\"name\":\"platform\",\"type\":[\"string\",\"null\"]},{\"name\":\"name\",\"type\":[\"string\",\"null\"]},{\"name\":\"industry\",\"type\":[\"string\",\"null\"]},{\"name\":\"status\",\"type\":[\"int\",\"null\"]},{\"name\":\"rank\",\"type\":[\"int\",\"null\"]},{\"name\":\"longitude\",\"type\":[\"string\",\"null\"]},{\"name\":\"latitude\",\"type\":[\"string\",\"null\"]},{\"name\":\"province\",\"type\":[\"string\",\"null\"]},{\"name\":\"city\",\"type\":[\"string\",\"null\"]},{\"name\":\"district\",\"type\":[\"string\",\"null\"]},{\"name\":\"street_address\",\"type\":[\"string\",\"null\"]},{\"name\":\"contact_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"contract_cellphone\",\"type\":[\"string\",\"null\"]},{\"name\":\"contact_phone\",\"type\":[\"string\",\"null\"]},{\"name\":\"contact_email\",\"type\":[\"string\",\"null\"]},{\"name\":\"operation_contents\",\"type\":[\"string\",\"null\"]},{\"name\":\"type\",\"type\":[\"int\",\"null\"]},{\"name\":\"open_account_way\",\"type\":[\"int\",\"null\"]},{\"name\":\"brand_photo\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"indoor_material\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"outdoor_material\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"product_price\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"audit_picture\",\"type\":[\"boolean\",\"null\"]},{\"name\":\"video\",\"type\":[\"string\",\"null\"]},{\"name\":\"business_hour\",\"type\":[\"string\",\"null\"]},{\"name\":\"store_area\",\"type\":[\"string\",\"null\"]},{\"name\":\"room_count\",\"type\":[\"string\",\"null\"]},{\"name\":\"table_count\",\"type\":[\"string\",\"null\"]},{\"name\":\"average_consumption_time\",\"type\":[\"string\",\"null\"]},{\"name\":\"around_type\",\"type\":[\"string\",\"null\"]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<ChangeStoreSensor> ENCODER =
      new BinaryMessageEncoder<ChangeStoreSensor>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<ChangeStoreSensor> DECODER =
      new BinaryMessageDecoder<ChangeStoreSensor>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<ChangeStoreSensor> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<ChangeStoreSensor> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<ChangeStoreSensor>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this ChangeStoreSensor to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a ChangeStoreSensor from a ByteBuffer. */
  public static ChangeStoreSensor fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence store_id;
  @Deprecated public java.lang.CharSequence store_sn;
  @Deprecated public java.lang.Long mtime;
  @Deprecated public java.lang.CharSequence operator;
  @Deprecated public java.lang.CharSequence platform;
  @Deprecated public java.lang.CharSequence name;
  @Deprecated public java.lang.CharSequence industry;
  @Deprecated public java.lang.Integer status;
  @Deprecated public java.lang.Integer rank;
  @Deprecated public java.lang.CharSequence longitude;
  @Deprecated public java.lang.CharSequence latitude;
  @Deprecated public java.lang.CharSequence province;
  @Deprecated public java.lang.CharSequence city;
  @Deprecated public java.lang.CharSequence district;
  @Deprecated public java.lang.CharSequence street_address;
  @Deprecated public java.lang.CharSequence contact_name;
  @Deprecated public java.lang.CharSequence contract_cellphone;
  @Deprecated public java.lang.CharSequence contact_phone;
  @Deprecated public java.lang.CharSequence contact_email;
  @Deprecated public java.lang.CharSequence operation_contents;
  @Deprecated public java.lang.Integer type;
  @Deprecated public java.lang.Integer open_account_way;
  @Deprecated public java.lang.Boolean brand_photo;
  @Deprecated public java.lang.Boolean indoor_material;
  @Deprecated public java.lang.Boolean outdoor_material;
  @Deprecated public java.lang.Boolean product_price;
  @Deprecated public java.lang.Boolean audit_picture;
  @Deprecated public java.lang.CharSequence video;
  @Deprecated public java.lang.CharSequence business_hour;
  @Deprecated public java.lang.CharSequence store_area;
  @Deprecated public java.lang.CharSequence room_count;
  @Deprecated public java.lang.CharSequence table_count;
  @Deprecated public java.lang.CharSequence average_consumption_time;
  @Deprecated public java.lang.CharSequence around_type;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public ChangeStoreSensor() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param merchant_sn The new value for merchant_sn
   * @param store_id The new value for store_id
   * @param store_sn The new value for store_sn
   * @param mtime The new value for mtime
   * @param operator The new value for operator
   * @param platform The new value for platform
   * @param name The new value for name
   * @param industry The new value for industry
   * @param status The new value for status
   * @param rank The new value for rank
   * @param longitude The new value for longitude
   * @param latitude The new value for latitude
   * @param province The new value for province
   * @param city The new value for city
   * @param district The new value for district
   * @param street_address The new value for street_address
   * @param contact_name The new value for contact_name
   * @param contract_cellphone The new value for contract_cellphone
   * @param contact_phone The new value for contact_phone
   * @param contact_email The new value for contact_email
   * @param operation_contents The new value for operation_contents
   * @param type The new value for type
   * @param open_account_way The new value for open_account_way
   * @param brand_photo The new value for brand_photo
   * @param indoor_material The new value for indoor_material
   * @param outdoor_material The new value for outdoor_material
   * @param product_price The new value for product_price
   * @param audit_picture The new value for audit_picture
   * @param video The new value for video
   * @param business_hour The new value for business_hour
   * @param store_area The new value for store_area
   * @param room_count The new value for room_count
   * @param table_count The new value for table_count
   * @param average_consumption_time The new value for average_consumption_time
   * @param around_type The new value for around_type
   */
  public ChangeStoreSensor(java.lang.CharSequence merchant_id, java.lang.CharSequence merchant_sn, java.lang.CharSequence store_id, java.lang.CharSequence store_sn, java.lang.Long mtime, java.lang.CharSequence operator, java.lang.CharSequence platform, java.lang.CharSequence name, java.lang.CharSequence industry, java.lang.Integer status, java.lang.Integer rank, java.lang.CharSequence longitude, java.lang.CharSequence latitude, java.lang.CharSequence province, java.lang.CharSequence city, java.lang.CharSequence district, java.lang.CharSequence street_address, java.lang.CharSequence contact_name, java.lang.CharSequence contract_cellphone, java.lang.CharSequence contact_phone, java.lang.CharSequence contact_email, java.lang.CharSequence operation_contents, java.lang.Integer type, java.lang.Integer open_account_way, java.lang.Boolean brand_photo, java.lang.Boolean indoor_material, java.lang.Boolean outdoor_material, java.lang.Boolean product_price, java.lang.Boolean audit_picture, java.lang.CharSequence video, java.lang.CharSequence business_hour, java.lang.CharSequence store_area, java.lang.CharSequence room_count, java.lang.CharSequence table_count, java.lang.CharSequence average_consumption_time, java.lang.CharSequence around_type) {
    this.merchant_id = merchant_id;
    this.merchant_sn = merchant_sn;
    this.store_id = store_id;
    this.store_sn = store_sn;
    this.mtime = mtime;
    this.operator = operator;
    this.platform = platform;
    this.name = name;
    this.industry = industry;
    this.status = status;
    this.rank = rank;
    this.longitude = longitude;
    this.latitude = latitude;
    this.province = province;
    this.city = city;
    this.district = district;
    this.street_address = street_address;
    this.contact_name = contact_name;
    this.contract_cellphone = contract_cellphone;
    this.contact_phone = contact_phone;
    this.contact_email = contact_email;
    this.operation_contents = operation_contents;
    this.type = type;
    this.open_account_way = open_account_way;
    this.brand_photo = brand_photo;
    this.indoor_material = indoor_material;
    this.outdoor_material = outdoor_material;
    this.product_price = product_price;
    this.audit_picture = audit_picture;
    this.video = video;
    this.business_hour = business_hour;
    this.store_area = store_area;
    this.room_count = room_count;
    this.table_count = table_count;
    this.average_consumption_time = average_consumption_time;
    this.around_type = around_type;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return merchant_sn;
    case 2: return store_id;
    case 3: return store_sn;
    case 4: return mtime;
    case 5: return operator;
    case 6: return platform;
    case 7: return name;
    case 8: return industry;
    case 9: return status;
    case 10: return rank;
    case 11: return longitude;
    case 12: return latitude;
    case 13: return province;
    case 14: return city;
    case 15: return district;
    case 16: return street_address;
    case 17: return contact_name;
    case 18: return contract_cellphone;
    case 19: return contact_phone;
    case 20: return contact_email;
    case 21: return operation_contents;
    case 22: return type;
    case 23: return open_account_way;
    case 24: return brand_photo;
    case 25: return indoor_material;
    case 26: return outdoor_material;
    case 27: return product_price;
    case 28: return audit_picture;
    case 29: return video;
    case 30: return business_hour;
    case 31: return store_area;
    case 32: return room_count;
    case 33: return table_count;
    case 34: return average_consumption_time;
    case 35: return around_type;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_id = (java.lang.CharSequence)value$; break;
    case 1: merchant_sn = (java.lang.CharSequence)value$; break;
    case 2: store_id = (java.lang.CharSequence)value$; break;
    case 3: store_sn = (java.lang.CharSequence)value$; break;
    case 4: mtime = (java.lang.Long)value$; break;
    case 5: operator = (java.lang.CharSequence)value$; break;
    case 6: platform = (java.lang.CharSequence)value$; break;
    case 7: name = (java.lang.CharSequence)value$; break;
    case 8: industry = (java.lang.CharSequence)value$; break;
    case 9: status = (java.lang.Integer)value$; break;
    case 10: rank = (java.lang.Integer)value$; break;
    case 11: longitude = (java.lang.CharSequence)value$; break;
    case 12: latitude = (java.lang.CharSequence)value$; break;
    case 13: province = (java.lang.CharSequence)value$; break;
    case 14: city = (java.lang.CharSequence)value$; break;
    case 15: district = (java.lang.CharSequence)value$; break;
    case 16: street_address = (java.lang.CharSequence)value$; break;
    case 17: contact_name = (java.lang.CharSequence)value$; break;
    case 18: contract_cellphone = (java.lang.CharSequence)value$; break;
    case 19: contact_phone = (java.lang.CharSequence)value$; break;
    case 20: contact_email = (java.lang.CharSequence)value$; break;
    case 21: operation_contents = (java.lang.CharSequence)value$; break;
    case 22: type = (java.lang.Integer)value$; break;
    case 23: open_account_way = (java.lang.Integer)value$; break;
    case 24: brand_photo = (java.lang.Boolean)value$; break;
    case 25: indoor_material = (java.lang.Boolean)value$; break;
    case 26: outdoor_material = (java.lang.Boolean)value$; break;
    case 27: product_price = (java.lang.Boolean)value$; break;
    case 28: audit_picture = (java.lang.Boolean)value$; break;
    case 29: video = (java.lang.CharSequence)value$; break;
    case 30: business_hour = (java.lang.CharSequence)value$; break;
    case 31: store_area = (java.lang.CharSequence)value$; break;
    case 32: room_count = (java.lang.CharSequence)value$; break;
    case 33: table_count = (java.lang.CharSequence)value$; break;
    case 34: average_consumption_time = (java.lang.CharSequence)value$; break;
    case 35: around_type = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'store_id' field.
   * @return The value of the 'store_id' field.
   */
  public java.lang.CharSequence getStoreId() {
    return store_id;
  }

  /**
   * Sets the value of the 'store_id' field.
   * @param value the value to set.
   */
  public void setStoreId(java.lang.CharSequence value) {
    this.store_id = value;
  }

  /**
   * Gets the value of the 'store_sn' field.
   * @return The value of the 'store_sn' field.
   */
  public java.lang.CharSequence getStoreSn() {
    return store_sn;
  }

  /**
   * Sets the value of the 'store_sn' field.
   * @param value the value to set.
   */
  public void setStoreSn(java.lang.CharSequence value) {
    this.store_sn = value;
  }

  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public java.lang.Long getMtime() {
    return mtime;
  }

  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(java.lang.Long value) {
    this.mtime = value;
  }

  /**
   * Gets the value of the 'operator' field.
   * @return The value of the 'operator' field.
   */
  public java.lang.CharSequence getOperator() {
    return operator;
  }

  /**
   * Sets the value of the 'operator' field.
   * @param value the value to set.
   */
  public void setOperator(java.lang.CharSequence value) {
    this.operator = value;
  }

  /**
   * Gets the value of the 'platform' field.
   * @return The value of the 'platform' field.
   */
  public java.lang.CharSequence getPlatform() {
    return platform;
  }

  /**
   * Sets the value of the 'platform' field.
   * @param value the value to set.
   */
  public void setPlatform(java.lang.CharSequence value) {
    this.platform = value;
  }

  /**
   * Gets the value of the 'name' field.
   * @return The value of the 'name' field.
   */
  public java.lang.CharSequence getName() {
    return name;
  }

  /**
   * Sets the value of the 'name' field.
   * @param value the value to set.
   */
  public void setName(java.lang.CharSequence value) {
    this.name = value;
  }

  /**
   * Gets the value of the 'industry' field.
   * @return The value of the 'industry' field.
   */
  public java.lang.CharSequence getIndustry() {
    return industry;
  }

  /**
   * Sets the value of the 'industry' field.
   * @param value the value to set.
   */
  public void setIndustry(java.lang.CharSequence value) {
    this.industry = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'rank' field.
   * @return The value of the 'rank' field.
   */
  public java.lang.Integer getRank() {
    return rank;
  }

  /**
   * Sets the value of the 'rank' field.
   * @param value the value to set.
   */
  public void setRank(java.lang.Integer value) {
    this.rank = value;
  }

  /**
   * Gets the value of the 'longitude' field.
   * @return The value of the 'longitude' field.
   */
  public java.lang.CharSequence getLongitude() {
    return longitude;
  }

  /**
   * Sets the value of the 'longitude' field.
   * @param value the value to set.
   */
  public void setLongitude(java.lang.CharSequence value) {
    this.longitude = value;
  }

  /**
   * Gets the value of the 'latitude' field.
   * @return The value of the 'latitude' field.
   */
  public java.lang.CharSequence getLatitude() {
    return latitude;
  }

  /**
   * Sets the value of the 'latitude' field.
   * @param value the value to set.
   */
  public void setLatitude(java.lang.CharSequence value) {
    this.latitude = value;
  }

  /**
   * Gets the value of the 'province' field.
   * @return The value of the 'province' field.
   */
  public java.lang.CharSequence getProvince() {
    return province;
  }

  /**
   * Sets the value of the 'province' field.
   * @param value the value to set.
   */
  public void setProvince(java.lang.CharSequence value) {
    this.province = value;
  }

  /**
   * Gets the value of the 'city' field.
   * @return The value of the 'city' field.
   */
  public java.lang.CharSequence getCity() {
    return city;
  }

  /**
   * Sets the value of the 'city' field.
   * @param value the value to set.
   */
  public void setCity(java.lang.CharSequence value) {
    this.city = value;
  }

  /**
   * Gets the value of the 'district' field.
   * @return The value of the 'district' field.
   */
  public java.lang.CharSequence getDistrict() {
    return district;
  }

  /**
   * Sets the value of the 'district' field.
   * @param value the value to set.
   */
  public void setDistrict(java.lang.CharSequence value) {
    this.district = value;
  }

  /**
   * Gets the value of the 'street_address' field.
   * @return The value of the 'street_address' field.
   */
  public java.lang.CharSequence getStreetAddress() {
    return street_address;
  }

  /**
   * Sets the value of the 'street_address' field.
   * @param value the value to set.
   */
  public void setStreetAddress(java.lang.CharSequence value) {
    this.street_address = value;
  }

  /**
   * Gets the value of the 'contact_name' field.
   * @return The value of the 'contact_name' field.
   */
  public java.lang.CharSequence getContactName() {
    return contact_name;
  }

  /**
   * Sets the value of the 'contact_name' field.
   * @param value the value to set.
   */
  public void setContactName(java.lang.CharSequence value) {
    this.contact_name = value;
  }

  /**
   * Gets the value of the 'contract_cellphone' field.
   * @return The value of the 'contract_cellphone' field.
   */
  public java.lang.CharSequence getContractCellphone() {
    return contract_cellphone;
  }

  /**
   * Sets the value of the 'contract_cellphone' field.
   * @param value the value to set.
   */
  public void setContractCellphone(java.lang.CharSequence value) {
    this.contract_cellphone = value;
  }

  /**
   * Gets the value of the 'contact_phone' field.
   * @return The value of the 'contact_phone' field.
   */
  public java.lang.CharSequence getContactPhone() {
    return contact_phone;
  }

  /**
   * Sets the value of the 'contact_phone' field.
   * @param value the value to set.
   */
  public void setContactPhone(java.lang.CharSequence value) {
    this.contact_phone = value;
  }

  /**
   * Gets the value of the 'contact_email' field.
   * @return The value of the 'contact_email' field.
   */
  public java.lang.CharSequence getContactEmail() {
    return contact_email;
  }

  /**
   * Sets the value of the 'contact_email' field.
   * @param value the value to set.
   */
  public void setContactEmail(java.lang.CharSequence value) {
    this.contact_email = value;
  }

  /**
   * Gets the value of the 'operation_contents' field.
   * @return The value of the 'operation_contents' field.
   */
  public java.lang.CharSequence getOperationContents() {
    return operation_contents;
  }

  /**
   * Sets the value of the 'operation_contents' field.
   * @param value the value to set.
   */
  public void setOperationContents(java.lang.CharSequence value) {
    this.operation_contents = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return The value of the 'type' field.
   */
  public java.lang.Integer getType() {
    return type;
  }

  /**
   * Sets the value of the 'type' field.
   * @param value the value to set.
   */
  public void setType(java.lang.Integer value) {
    this.type = value;
  }

  /**
   * Gets the value of the 'open_account_way' field.
   * @return The value of the 'open_account_way' field.
   */
  public java.lang.Integer getOpenAccountWay() {
    return open_account_way;
  }

  /**
   * Sets the value of the 'open_account_way' field.
   * @param value the value to set.
   */
  public void setOpenAccountWay(java.lang.Integer value) {
    this.open_account_way = value;
  }

  /**
   * Gets the value of the 'brand_photo' field.
   * @return The value of the 'brand_photo' field.
   */
  public java.lang.Boolean getBrandPhoto() {
    return brand_photo;
  }

  /**
   * Sets the value of the 'brand_photo' field.
   * @param value the value to set.
   */
  public void setBrandPhoto(java.lang.Boolean value) {
    this.brand_photo = value;
  }

  /**
   * Gets the value of the 'indoor_material' field.
   * @return The value of the 'indoor_material' field.
   */
  public java.lang.Boolean getIndoorMaterial() {
    return indoor_material;
  }

  /**
   * Sets the value of the 'indoor_material' field.
   * @param value the value to set.
   */
  public void setIndoorMaterial(java.lang.Boolean value) {
    this.indoor_material = value;
  }

  /**
   * Gets the value of the 'outdoor_material' field.
   * @return The value of the 'outdoor_material' field.
   */
  public java.lang.Boolean getOutdoorMaterial() {
    return outdoor_material;
  }

  /**
   * Sets the value of the 'outdoor_material' field.
   * @param value the value to set.
   */
  public void setOutdoorMaterial(java.lang.Boolean value) {
    this.outdoor_material = value;
  }

  /**
   * Gets the value of the 'product_price' field.
   * @return The value of the 'product_price' field.
   */
  public java.lang.Boolean getProductPrice() {
    return product_price;
  }

  /**
   * Sets the value of the 'product_price' field.
   * @param value the value to set.
   */
  public void setProductPrice(java.lang.Boolean value) {
    this.product_price = value;
  }

  /**
   * Gets the value of the 'audit_picture' field.
   * @return The value of the 'audit_picture' field.
   */
  public java.lang.Boolean getAuditPicture() {
    return audit_picture;
  }

  /**
   * Sets the value of the 'audit_picture' field.
   * @param value the value to set.
   */
  public void setAuditPicture(java.lang.Boolean value) {
    this.audit_picture = value;
  }

  /**
   * Gets the value of the 'video' field.
   * @return The value of the 'video' field.
   */
  public java.lang.CharSequence getVideo() {
    return video;
  }

  /**
   * Sets the value of the 'video' field.
   * @param value the value to set.
   */
  public void setVideo(java.lang.CharSequence value) {
    this.video = value;
  }

  /**
   * Gets the value of the 'business_hour' field.
   * @return The value of the 'business_hour' field.
   */
  public java.lang.CharSequence getBusinessHour() {
    return business_hour;
  }

  /**
   * Sets the value of the 'business_hour' field.
   * @param value the value to set.
   */
  public void setBusinessHour(java.lang.CharSequence value) {
    this.business_hour = value;
  }

  /**
   * Gets the value of the 'store_area' field.
   * @return The value of the 'store_area' field.
   */
  public java.lang.CharSequence getStoreArea() {
    return store_area;
  }

  /**
   * Sets the value of the 'store_area' field.
   * @param value the value to set.
   */
  public void setStoreArea(java.lang.CharSequence value) {
    this.store_area = value;
  }

  /**
   * Gets the value of the 'room_count' field.
   * @return The value of the 'room_count' field.
   */
  public java.lang.CharSequence getRoomCount() {
    return room_count;
  }

  /**
   * Sets the value of the 'room_count' field.
   * @param value the value to set.
   */
  public void setRoomCount(java.lang.CharSequence value) {
    this.room_count = value;
  }

  /**
   * Gets the value of the 'table_count' field.
   * @return The value of the 'table_count' field.
   */
  public java.lang.CharSequence getTableCount() {
    return table_count;
  }

  /**
   * Sets the value of the 'table_count' field.
   * @param value the value to set.
   */
  public void setTableCount(java.lang.CharSequence value) {
    this.table_count = value;
  }

  /**
   * Gets the value of the 'average_consumption_time' field.
   * @return The value of the 'average_consumption_time' field.
   */
  public java.lang.CharSequence getAverageConsumptionTime() {
    return average_consumption_time;
  }

  /**
   * Sets the value of the 'average_consumption_time' field.
   * @param value the value to set.
   */
  public void setAverageConsumptionTime(java.lang.CharSequence value) {
    this.average_consumption_time = value;
  }

  /**
   * Gets the value of the 'around_type' field.
   * @return The value of the 'around_type' field.
   */
  public java.lang.CharSequence getAroundType() {
    return around_type;
  }

  /**
   * Sets the value of the 'around_type' field.
   * @param value the value to set.
   */
  public void setAroundType(java.lang.CharSequence value) {
    this.around_type = value;
  }

  /**
   * Creates a new ChangeStoreSensor RecordBuilder.
   * @return A new ChangeStoreSensor RecordBuilder
   */
  public static com.wosai.mc.avro.ChangeStoreSensor.Builder newBuilder() {
    return new com.wosai.mc.avro.ChangeStoreSensor.Builder();
  }

  /**
   * Creates a new ChangeStoreSensor RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new ChangeStoreSensor RecordBuilder
   */
  public static com.wosai.mc.avro.ChangeStoreSensor.Builder newBuilder(com.wosai.mc.avro.ChangeStoreSensor.Builder other) {
    return new com.wosai.mc.avro.ChangeStoreSensor.Builder(other);
  }

  /**
   * Creates a new ChangeStoreSensor RecordBuilder by copying an existing ChangeStoreSensor instance.
   * @param other The existing instance to copy.
   * @return A new ChangeStoreSensor RecordBuilder
   */
  public static com.wosai.mc.avro.ChangeStoreSensor.Builder newBuilder(com.wosai.mc.avro.ChangeStoreSensor other) {
    return new com.wosai.mc.avro.ChangeStoreSensor.Builder(other);
  }

  /**
   * RecordBuilder for ChangeStoreSensor instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<ChangeStoreSensor>
    implements org.apache.avro.data.RecordBuilder<ChangeStoreSensor> {

    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence store_id;
    private java.lang.CharSequence store_sn;
    private java.lang.Long mtime;
    private java.lang.CharSequence operator;
    private java.lang.CharSequence platform;
    private java.lang.CharSequence name;
    private java.lang.CharSequence industry;
    private java.lang.Integer status;
    private java.lang.Integer rank;
    private java.lang.CharSequence longitude;
    private java.lang.CharSequence latitude;
    private java.lang.CharSequence province;
    private java.lang.CharSequence city;
    private java.lang.CharSequence district;
    private java.lang.CharSequence street_address;
    private java.lang.CharSequence contact_name;
    private java.lang.CharSequence contract_cellphone;
    private java.lang.CharSequence contact_phone;
    private java.lang.CharSequence contact_email;
    private java.lang.CharSequence operation_contents;
    private java.lang.Integer type;
    private java.lang.Integer open_account_way;
    private java.lang.Boolean brand_photo;
    private java.lang.Boolean indoor_material;
    private java.lang.Boolean outdoor_material;
    private java.lang.Boolean product_price;
    private java.lang.Boolean audit_picture;
    private java.lang.CharSequence video;
    private java.lang.CharSequence business_hour;
    private java.lang.CharSequence store_area;
    private java.lang.CharSequence room_count;
    private java.lang.CharSequence table_count;
    private java.lang.CharSequence average_consumption_time;
    private java.lang.CharSequence around_type;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.mc.avro.ChangeStoreSensor.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.store_id)) {
        this.store_id = data().deepCopy(fields()[2].schema(), other.store_id);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.store_sn)) {
        this.store_sn = data().deepCopy(fields()[3].schema(), other.store_sn);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.mtime)) {
        this.mtime = data().deepCopy(fields()[4].schema(), other.mtime);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.operator)) {
        this.operator = data().deepCopy(fields()[5].schema(), other.operator);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.platform)) {
        this.platform = data().deepCopy(fields()[6].schema(), other.platform);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.name)) {
        this.name = data().deepCopy(fields()[7].schema(), other.name);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.industry)) {
        this.industry = data().deepCopy(fields()[8].schema(), other.industry);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.status)) {
        this.status = data().deepCopy(fields()[9].schema(), other.status);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.rank)) {
        this.rank = data().deepCopy(fields()[10].schema(), other.rank);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.longitude)) {
        this.longitude = data().deepCopy(fields()[11].schema(), other.longitude);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.latitude)) {
        this.latitude = data().deepCopy(fields()[12].schema(), other.latitude);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.province)) {
        this.province = data().deepCopy(fields()[13].schema(), other.province);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.city)) {
        this.city = data().deepCopy(fields()[14].schema(), other.city);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.district)) {
        this.district = data().deepCopy(fields()[15].schema(), other.district);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.street_address)) {
        this.street_address = data().deepCopy(fields()[16].schema(), other.street_address);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.contact_name)) {
        this.contact_name = data().deepCopy(fields()[17].schema(), other.contact_name);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.contract_cellphone)) {
        this.contract_cellphone = data().deepCopy(fields()[18].schema(), other.contract_cellphone);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.contact_phone)) {
        this.contact_phone = data().deepCopy(fields()[19].schema(), other.contact_phone);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.contact_email)) {
        this.contact_email = data().deepCopy(fields()[20].schema(), other.contact_email);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.operation_contents)) {
        this.operation_contents = data().deepCopy(fields()[21].schema(), other.operation_contents);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.type)) {
        this.type = data().deepCopy(fields()[22].schema(), other.type);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.open_account_way)) {
        this.open_account_way = data().deepCopy(fields()[23].schema(), other.open_account_way);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.brand_photo)) {
        this.brand_photo = data().deepCopy(fields()[24].schema(), other.brand_photo);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.indoor_material)) {
        this.indoor_material = data().deepCopy(fields()[25].schema(), other.indoor_material);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.outdoor_material)) {
        this.outdoor_material = data().deepCopy(fields()[26].schema(), other.outdoor_material);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.product_price)) {
        this.product_price = data().deepCopy(fields()[27].schema(), other.product_price);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.audit_picture)) {
        this.audit_picture = data().deepCopy(fields()[28].schema(), other.audit_picture);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.video)) {
        this.video = data().deepCopy(fields()[29].schema(), other.video);
        fieldSetFlags()[29] = true;
      }
      if (isValidValue(fields()[30], other.business_hour)) {
        this.business_hour = data().deepCopy(fields()[30].schema(), other.business_hour);
        fieldSetFlags()[30] = true;
      }
      if (isValidValue(fields()[31], other.store_area)) {
        this.store_area = data().deepCopy(fields()[31].schema(), other.store_area);
        fieldSetFlags()[31] = true;
      }
      if (isValidValue(fields()[32], other.room_count)) {
        this.room_count = data().deepCopy(fields()[32].schema(), other.room_count);
        fieldSetFlags()[32] = true;
      }
      if (isValidValue(fields()[33], other.table_count)) {
        this.table_count = data().deepCopy(fields()[33].schema(), other.table_count);
        fieldSetFlags()[33] = true;
      }
      if (isValidValue(fields()[34], other.average_consumption_time)) {
        this.average_consumption_time = data().deepCopy(fields()[34].schema(), other.average_consumption_time);
        fieldSetFlags()[34] = true;
      }
      if (isValidValue(fields()[35], other.around_type)) {
        this.around_type = data().deepCopy(fields()[35].schema(), other.around_type);
        fieldSetFlags()[35] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing ChangeStoreSensor instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.mc.avro.ChangeStoreSensor other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.store_id)) {
        this.store_id = data().deepCopy(fields()[2].schema(), other.store_id);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.store_sn)) {
        this.store_sn = data().deepCopy(fields()[3].schema(), other.store_sn);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.mtime)) {
        this.mtime = data().deepCopy(fields()[4].schema(), other.mtime);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.operator)) {
        this.operator = data().deepCopy(fields()[5].schema(), other.operator);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.platform)) {
        this.platform = data().deepCopy(fields()[6].schema(), other.platform);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.name)) {
        this.name = data().deepCopy(fields()[7].schema(), other.name);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.industry)) {
        this.industry = data().deepCopy(fields()[8].schema(), other.industry);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.status)) {
        this.status = data().deepCopy(fields()[9].schema(), other.status);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.rank)) {
        this.rank = data().deepCopy(fields()[10].schema(), other.rank);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.longitude)) {
        this.longitude = data().deepCopy(fields()[11].schema(), other.longitude);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.latitude)) {
        this.latitude = data().deepCopy(fields()[12].schema(), other.latitude);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.province)) {
        this.province = data().deepCopy(fields()[13].schema(), other.province);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.city)) {
        this.city = data().deepCopy(fields()[14].schema(), other.city);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.district)) {
        this.district = data().deepCopy(fields()[15].schema(), other.district);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.street_address)) {
        this.street_address = data().deepCopy(fields()[16].schema(), other.street_address);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.contact_name)) {
        this.contact_name = data().deepCopy(fields()[17].schema(), other.contact_name);
        fieldSetFlags()[17] = true;
      }
      if (isValidValue(fields()[18], other.contract_cellphone)) {
        this.contract_cellphone = data().deepCopy(fields()[18].schema(), other.contract_cellphone);
        fieldSetFlags()[18] = true;
      }
      if (isValidValue(fields()[19], other.contact_phone)) {
        this.contact_phone = data().deepCopy(fields()[19].schema(), other.contact_phone);
        fieldSetFlags()[19] = true;
      }
      if (isValidValue(fields()[20], other.contact_email)) {
        this.contact_email = data().deepCopy(fields()[20].schema(), other.contact_email);
        fieldSetFlags()[20] = true;
      }
      if (isValidValue(fields()[21], other.operation_contents)) {
        this.operation_contents = data().deepCopy(fields()[21].schema(), other.operation_contents);
        fieldSetFlags()[21] = true;
      }
      if (isValidValue(fields()[22], other.type)) {
        this.type = data().deepCopy(fields()[22].schema(), other.type);
        fieldSetFlags()[22] = true;
      }
      if (isValidValue(fields()[23], other.open_account_way)) {
        this.open_account_way = data().deepCopy(fields()[23].schema(), other.open_account_way);
        fieldSetFlags()[23] = true;
      }
      if (isValidValue(fields()[24], other.brand_photo)) {
        this.brand_photo = data().deepCopy(fields()[24].schema(), other.brand_photo);
        fieldSetFlags()[24] = true;
      }
      if (isValidValue(fields()[25], other.indoor_material)) {
        this.indoor_material = data().deepCopy(fields()[25].schema(), other.indoor_material);
        fieldSetFlags()[25] = true;
      }
      if (isValidValue(fields()[26], other.outdoor_material)) {
        this.outdoor_material = data().deepCopy(fields()[26].schema(), other.outdoor_material);
        fieldSetFlags()[26] = true;
      }
      if (isValidValue(fields()[27], other.product_price)) {
        this.product_price = data().deepCopy(fields()[27].schema(), other.product_price);
        fieldSetFlags()[27] = true;
      }
      if (isValidValue(fields()[28], other.audit_picture)) {
        this.audit_picture = data().deepCopy(fields()[28].schema(), other.audit_picture);
        fieldSetFlags()[28] = true;
      }
      if (isValidValue(fields()[29], other.video)) {
        this.video = data().deepCopy(fields()[29].schema(), other.video);
        fieldSetFlags()[29] = true;
      }
      if (isValidValue(fields()[30], other.business_hour)) {
        this.business_hour = data().deepCopy(fields()[30].schema(), other.business_hour);
        fieldSetFlags()[30] = true;
      }
      if (isValidValue(fields()[31], other.store_area)) {
        this.store_area = data().deepCopy(fields()[31].schema(), other.store_area);
        fieldSetFlags()[31] = true;
      }
      if (isValidValue(fields()[32], other.room_count)) {
        this.room_count = data().deepCopy(fields()[32].schema(), other.room_count);
        fieldSetFlags()[32] = true;
      }
      if (isValidValue(fields()[33], other.table_count)) {
        this.table_count = data().deepCopy(fields()[33].schema(), other.table_count);
        fieldSetFlags()[33] = true;
      }
      if (isValidValue(fields()[34], other.average_consumption_time)) {
        this.average_consumption_time = data().deepCopy(fields()[34].schema(), other.average_consumption_time);
        fieldSetFlags()[34] = true;
      }
      if (isValidValue(fields()[35], other.around_type)) {
        this.around_type = data().deepCopy(fields()[35].schema(), other.around_type);
        fieldSetFlags()[35] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'store_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getStoreId() {
      return store_id;
    }

    /**
      * Sets the value of the 'store_id' field.
      * @param value The value of 'store_id'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setStoreId(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.store_id = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'store_id' field has been set.
      * @return True if the 'store_id' field has been set, false otherwise.
      */
    public boolean hasStoreId() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'store_id' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearStoreId() {
      store_id = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'store_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getStoreSn() {
      return store_sn;
    }

    /**
      * Sets the value of the 'store_sn' field.
      * @param value The value of 'store_sn'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setStoreSn(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.store_sn = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'store_sn' field has been set.
      * @return True if the 'store_sn' field has been set, false otherwise.
      */
    public boolean hasStoreSn() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'store_sn' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearStoreSn() {
      store_sn = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public java.lang.Long getMtime() {
      return mtime;
    }

    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setMtime(java.lang.Long value) {
      validate(fields()[4], value);
      this.mtime = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearMtime() {
      mtime = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'operator' field.
      * @return The value.
      */
    public java.lang.CharSequence getOperator() {
      return operator;
    }

    /**
      * Sets the value of the 'operator' field.
      * @param value The value of 'operator'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setOperator(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.operator = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'operator' field has been set.
      * @return True if the 'operator' field has been set, false otherwise.
      */
    public boolean hasOperator() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'operator' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearOperator() {
      operator = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'platform' field.
      * @return The value.
      */
    public java.lang.CharSequence getPlatform() {
      return platform;
    }

    /**
      * Sets the value of the 'platform' field.
      * @param value The value of 'platform'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setPlatform(java.lang.CharSequence value) {
      validate(fields()[6], value);
      this.platform = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'platform' field has been set.
      * @return True if the 'platform' field has been set, false otherwise.
      */
    public boolean hasPlatform() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'platform' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearPlatform() {
      platform = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'name' field.
      * @return The value.
      */
    public java.lang.CharSequence getName() {
      return name;
    }

    /**
      * Sets the value of the 'name' field.
      * @param value The value of 'name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setName(java.lang.CharSequence value) {
      validate(fields()[7], value);
      this.name = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'name' field has been set.
      * @return True if the 'name' field has been set, false otherwise.
      */
    public boolean hasName() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearName() {
      name = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'industry' field.
      * @return The value.
      */
    public java.lang.CharSequence getIndustry() {
      return industry;
    }

    /**
      * Sets the value of the 'industry' field.
      * @param value The value of 'industry'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setIndustry(java.lang.CharSequence value) {
      validate(fields()[8], value);
      this.industry = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'industry' field has been set.
      * @return True if the 'industry' field has been set, false otherwise.
      */
    public boolean hasIndustry() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'industry' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearIndustry() {
      industry = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setStatus(java.lang.Integer value) {
      validate(fields()[9], value);
      this.status = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearStatus() {
      status = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'rank' field.
      * @return The value.
      */
    public java.lang.Integer getRank() {
      return rank;
    }

    /**
      * Sets the value of the 'rank' field.
      * @param value The value of 'rank'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setRank(java.lang.Integer value) {
      validate(fields()[10], value);
      this.rank = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'rank' field has been set.
      * @return True if the 'rank' field has been set, false otherwise.
      */
    public boolean hasRank() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'rank' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearRank() {
      rank = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'longitude' field.
      * @return The value.
      */
    public java.lang.CharSequence getLongitude() {
      return longitude;
    }

    /**
      * Sets the value of the 'longitude' field.
      * @param value The value of 'longitude'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setLongitude(java.lang.CharSequence value) {
      validate(fields()[11], value);
      this.longitude = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'longitude' field has been set.
      * @return True if the 'longitude' field has been set, false otherwise.
      */
    public boolean hasLongitude() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'longitude' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearLongitude() {
      longitude = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'latitude' field.
      * @return The value.
      */
    public java.lang.CharSequence getLatitude() {
      return latitude;
    }

    /**
      * Sets the value of the 'latitude' field.
      * @param value The value of 'latitude'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setLatitude(java.lang.CharSequence value) {
      validate(fields()[12], value);
      this.latitude = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'latitude' field has been set.
      * @return True if the 'latitude' field has been set, false otherwise.
      */
    public boolean hasLatitude() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'latitude' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearLatitude() {
      latitude = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'province' field.
      * @return The value.
      */
    public java.lang.CharSequence getProvince() {
      return province;
    }

    /**
      * Sets the value of the 'province' field.
      * @param value The value of 'province'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setProvince(java.lang.CharSequence value) {
      validate(fields()[13], value);
      this.province = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'province' field has been set.
      * @return True if the 'province' field has been set, false otherwise.
      */
    public boolean hasProvince() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'province' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearProvince() {
      province = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'city' field.
      * @return The value.
      */
    public java.lang.CharSequence getCity() {
      return city;
    }

    /**
      * Sets the value of the 'city' field.
      * @param value The value of 'city'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setCity(java.lang.CharSequence value) {
      validate(fields()[14], value);
      this.city = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'city' field has been set.
      * @return True if the 'city' field has been set, false otherwise.
      */
    public boolean hasCity() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'city' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearCity() {
      city = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'district' field.
      * @return The value.
      */
    public java.lang.CharSequence getDistrict() {
      return district;
    }

    /**
      * Sets the value of the 'district' field.
      * @param value The value of 'district'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setDistrict(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.district = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'district' field has been set.
      * @return True if the 'district' field has been set, false otherwise.
      */
    public boolean hasDistrict() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'district' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearDistrict() {
      district = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'street_address' field.
      * @return The value.
      */
    public java.lang.CharSequence getStreetAddress() {
      return street_address;
    }

    /**
      * Sets the value of the 'street_address' field.
      * @param value The value of 'street_address'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setStreetAddress(java.lang.CharSequence value) {
      validate(fields()[16], value);
      this.street_address = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'street_address' field has been set.
      * @return True if the 'street_address' field has been set, false otherwise.
      */
    public boolean hasStreetAddress() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'street_address' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearStreetAddress() {
      street_address = null;
      fieldSetFlags()[16] = false;
      return this;
    }

    /**
      * Gets the value of the 'contact_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getContactName() {
      return contact_name;
    }

    /**
      * Sets the value of the 'contact_name' field.
      * @param value The value of 'contact_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setContactName(java.lang.CharSequence value) {
      validate(fields()[17], value);
      this.contact_name = value;
      fieldSetFlags()[17] = true;
      return this;
    }

    /**
      * Checks whether the 'contact_name' field has been set.
      * @return True if the 'contact_name' field has been set, false otherwise.
      */
    public boolean hasContactName() {
      return fieldSetFlags()[17];
    }


    /**
      * Clears the value of the 'contact_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearContactName() {
      contact_name = null;
      fieldSetFlags()[17] = false;
      return this;
    }

    /**
      * Gets the value of the 'contract_cellphone' field.
      * @return The value.
      */
    public java.lang.CharSequence getContractCellphone() {
      return contract_cellphone;
    }

    /**
      * Sets the value of the 'contract_cellphone' field.
      * @param value The value of 'contract_cellphone'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setContractCellphone(java.lang.CharSequence value) {
      validate(fields()[18], value);
      this.contract_cellphone = value;
      fieldSetFlags()[18] = true;
      return this;
    }

    /**
      * Checks whether the 'contract_cellphone' field has been set.
      * @return True if the 'contract_cellphone' field has been set, false otherwise.
      */
    public boolean hasContractCellphone() {
      return fieldSetFlags()[18];
    }


    /**
      * Clears the value of the 'contract_cellphone' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearContractCellphone() {
      contract_cellphone = null;
      fieldSetFlags()[18] = false;
      return this;
    }

    /**
      * Gets the value of the 'contact_phone' field.
      * @return The value.
      */
    public java.lang.CharSequence getContactPhone() {
      return contact_phone;
    }

    /**
      * Sets the value of the 'contact_phone' field.
      * @param value The value of 'contact_phone'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setContactPhone(java.lang.CharSequence value) {
      validate(fields()[19], value);
      this.contact_phone = value;
      fieldSetFlags()[19] = true;
      return this;
    }

    /**
      * Checks whether the 'contact_phone' field has been set.
      * @return True if the 'contact_phone' field has been set, false otherwise.
      */
    public boolean hasContactPhone() {
      return fieldSetFlags()[19];
    }


    /**
      * Clears the value of the 'contact_phone' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearContactPhone() {
      contact_phone = null;
      fieldSetFlags()[19] = false;
      return this;
    }

    /**
      * Gets the value of the 'contact_email' field.
      * @return The value.
      */
    public java.lang.CharSequence getContactEmail() {
      return contact_email;
    }

    /**
      * Sets the value of the 'contact_email' field.
      * @param value The value of 'contact_email'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setContactEmail(java.lang.CharSequence value) {
      validate(fields()[20], value);
      this.contact_email = value;
      fieldSetFlags()[20] = true;
      return this;
    }

    /**
      * Checks whether the 'contact_email' field has been set.
      * @return True if the 'contact_email' field has been set, false otherwise.
      */
    public boolean hasContactEmail() {
      return fieldSetFlags()[20];
    }


    /**
      * Clears the value of the 'contact_email' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearContactEmail() {
      contact_email = null;
      fieldSetFlags()[20] = false;
      return this;
    }

    /**
      * Gets the value of the 'operation_contents' field.
      * @return The value.
      */
    public java.lang.CharSequence getOperationContents() {
      return operation_contents;
    }

    /**
      * Sets the value of the 'operation_contents' field.
      * @param value The value of 'operation_contents'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setOperationContents(java.lang.CharSequence value) {
      validate(fields()[21], value);
      this.operation_contents = value;
      fieldSetFlags()[21] = true;
      return this;
    }

    /**
      * Checks whether the 'operation_contents' field has been set.
      * @return True if the 'operation_contents' field has been set, false otherwise.
      */
    public boolean hasOperationContents() {
      return fieldSetFlags()[21];
    }


    /**
      * Clears the value of the 'operation_contents' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearOperationContents() {
      operation_contents = null;
      fieldSetFlags()[21] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * @return The value.
      */
    public java.lang.Integer getType() {
      return type;
    }

    /**
      * Sets the value of the 'type' field.
      * @param value The value of 'type'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setType(java.lang.Integer value) {
      validate(fields()[22], value);
      this.type = value;
      fieldSetFlags()[22] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[22];
    }


    /**
      * Clears the value of the 'type' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearType() {
      type = null;
      fieldSetFlags()[22] = false;
      return this;
    }

    /**
      * Gets the value of the 'open_account_way' field.
      * @return The value.
      */
    public java.lang.Integer getOpenAccountWay() {
      return open_account_way;
    }

    /**
      * Sets the value of the 'open_account_way' field.
      * @param value The value of 'open_account_way'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setOpenAccountWay(java.lang.Integer value) {
      validate(fields()[23], value);
      this.open_account_way = value;
      fieldSetFlags()[23] = true;
      return this;
    }

    /**
      * Checks whether the 'open_account_way' field has been set.
      * @return True if the 'open_account_way' field has been set, false otherwise.
      */
    public boolean hasOpenAccountWay() {
      return fieldSetFlags()[23];
    }


    /**
      * Clears the value of the 'open_account_way' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearOpenAccountWay() {
      open_account_way = null;
      fieldSetFlags()[23] = false;
      return this;
    }

    /**
      * Gets the value of the 'brand_photo' field.
      * @return The value.
      */
    public java.lang.Boolean getBrandPhoto() {
      return brand_photo;
    }

    /**
      * Sets the value of the 'brand_photo' field.
      * @param value The value of 'brand_photo'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setBrandPhoto(java.lang.Boolean value) {
      validate(fields()[24], value);
      this.brand_photo = value;
      fieldSetFlags()[24] = true;
      return this;
    }

    /**
      * Checks whether the 'brand_photo' field has been set.
      * @return True if the 'brand_photo' field has been set, false otherwise.
      */
    public boolean hasBrandPhoto() {
      return fieldSetFlags()[24];
    }


    /**
      * Clears the value of the 'brand_photo' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearBrandPhoto() {
      brand_photo = null;
      fieldSetFlags()[24] = false;
      return this;
    }

    /**
      * Gets the value of the 'indoor_material' field.
      * @return The value.
      */
    public java.lang.Boolean getIndoorMaterial() {
      return indoor_material;
    }

    /**
      * Sets the value of the 'indoor_material' field.
      * @param value The value of 'indoor_material'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setIndoorMaterial(java.lang.Boolean value) {
      validate(fields()[25], value);
      this.indoor_material = value;
      fieldSetFlags()[25] = true;
      return this;
    }

    /**
      * Checks whether the 'indoor_material' field has been set.
      * @return True if the 'indoor_material' field has been set, false otherwise.
      */
    public boolean hasIndoorMaterial() {
      return fieldSetFlags()[25];
    }


    /**
      * Clears the value of the 'indoor_material' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearIndoorMaterial() {
      indoor_material = null;
      fieldSetFlags()[25] = false;
      return this;
    }

    /**
      * Gets the value of the 'outdoor_material' field.
      * @return The value.
      */
    public java.lang.Boolean getOutdoorMaterial() {
      return outdoor_material;
    }

    /**
      * Sets the value of the 'outdoor_material' field.
      * @param value The value of 'outdoor_material'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setOutdoorMaterial(java.lang.Boolean value) {
      validate(fields()[26], value);
      this.outdoor_material = value;
      fieldSetFlags()[26] = true;
      return this;
    }

    /**
      * Checks whether the 'outdoor_material' field has been set.
      * @return True if the 'outdoor_material' field has been set, false otherwise.
      */
    public boolean hasOutdoorMaterial() {
      return fieldSetFlags()[26];
    }


    /**
      * Clears the value of the 'outdoor_material' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearOutdoorMaterial() {
      outdoor_material = null;
      fieldSetFlags()[26] = false;
      return this;
    }

    /**
      * Gets the value of the 'product_price' field.
      * @return The value.
      */
    public java.lang.Boolean getProductPrice() {
      return product_price;
    }

    /**
      * Sets the value of the 'product_price' field.
      * @param value The value of 'product_price'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setProductPrice(java.lang.Boolean value) {
      validate(fields()[27], value);
      this.product_price = value;
      fieldSetFlags()[27] = true;
      return this;
    }

    /**
      * Checks whether the 'product_price' field has been set.
      * @return True if the 'product_price' field has been set, false otherwise.
      */
    public boolean hasProductPrice() {
      return fieldSetFlags()[27];
    }


    /**
      * Clears the value of the 'product_price' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearProductPrice() {
      product_price = null;
      fieldSetFlags()[27] = false;
      return this;
    }

    /**
      * Gets the value of the 'audit_picture' field.
      * @return The value.
      */
    public java.lang.Boolean getAuditPicture() {
      return audit_picture;
    }

    /**
      * Sets the value of the 'audit_picture' field.
      * @param value The value of 'audit_picture'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setAuditPicture(java.lang.Boolean value) {
      validate(fields()[28], value);
      this.audit_picture = value;
      fieldSetFlags()[28] = true;
      return this;
    }

    /**
      * Checks whether the 'audit_picture' field has been set.
      * @return True if the 'audit_picture' field has been set, false otherwise.
      */
    public boolean hasAuditPicture() {
      return fieldSetFlags()[28];
    }


    /**
      * Clears the value of the 'audit_picture' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearAuditPicture() {
      audit_picture = null;
      fieldSetFlags()[28] = false;
      return this;
    }

    /**
      * Gets the value of the 'video' field.
      * @return The value.
      */
    public java.lang.CharSequence getVideo() {
      return video;
    }

    /**
      * Sets the value of the 'video' field.
      * @param value The value of 'video'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setVideo(java.lang.CharSequence value) {
      validate(fields()[29], value);
      this.video = value;
      fieldSetFlags()[29] = true;
      return this;
    }

    /**
      * Checks whether the 'video' field has been set.
      * @return True if the 'video' field has been set, false otherwise.
      */
    public boolean hasVideo() {
      return fieldSetFlags()[29];
    }


    /**
      * Clears the value of the 'video' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearVideo() {
      video = null;
      fieldSetFlags()[29] = false;
      return this;
    }

    /**
      * Gets the value of the 'business_hour' field.
      * @return The value.
      */
    public java.lang.CharSequence getBusinessHour() {
      return business_hour;
    }

    /**
      * Sets the value of the 'business_hour' field.
      * @param value The value of 'business_hour'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setBusinessHour(java.lang.CharSequence value) {
      validate(fields()[30], value);
      this.business_hour = value;
      fieldSetFlags()[30] = true;
      return this;
    }

    /**
      * Checks whether the 'business_hour' field has been set.
      * @return True if the 'business_hour' field has been set, false otherwise.
      */
    public boolean hasBusinessHour() {
      return fieldSetFlags()[30];
    }


    /**
      * Clears the value of the 'business_hour' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearBusinessHour() {
      business_hour = null;
      fieldSetFlags()[30] = false;
      return this;
    }

    /**
      * Gets the value of the 'store_area' field.
      * @return The value.
      */
    public java.lang.CharSequence getStoreArea() {
      return store_area;
    }

    /**
      * Sets the value of the 'store_area' field.
      * @param value The value of 'store_area'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setStoreArea(java.lang.CharSequence value) {
      validate(fields()[31], value);
      this.store_area = value;
      fieldSetFlags()[31] = true;
      return this;
    }

    /**
      * Checks whether the 'store_area' field has been set.
      * @return True if the 'store_area' field has been set, false otherwise.
      */
    public boolean hasStoreArea() {
      return fieldSetFlags()[31];
    }


    /**
      * Clears the value of the 'store_area' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearStoreArea() {
      store_area = null;
      fieldSetFlags()[31] = false;
      return this;
    }

    /**
      * Gets the value of the 'room_count' field.
      * @return The value.
      */
    public java.lang.CharSequence getRoomCount() {
      return room_count;
    }

    /**
      * Sets the value of the 'room_count' field.
      * @param value The value of 'room_count'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setRoomCount(java.lang.CharSequence value) {
      validate(fields()[32], value);
      this.room_count = value;
      fieldSetFlags()[32] = true;
      return this;
    }

    /**
      * Checks whether the 'room_count' field has been set.
      * @return True if the 'room_count' field has been set, false otherwise.
      */
    public boolean hasRoomCount() {
      return fieldSetFlags()[32];
    }


    /**
      * Clears the value of the 'room_count' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearRoomCount() {
      room_count = null;
      fieldSetFlags()[32] = false;
      return this;
    }

    /**
      * Gets the value of the 'table_count' field.
      * @return The value.
      */
    public java.lang.CharSequence getTableCount() {
      return table_count;
    }

    /**
      * Sets the value of the 'table_count' field.
      * @param value The value of 'table_count'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setTableCount(java.lang.CharSequence value) {
      validate(fields()[33], value);
      this.table_count = value;
      fieldSetFlags()[33] = true;
      return this;
    }

    /**
      * Checks whether the 'table_count' field has been set.
      * @return True if the 'table_count' field has been set, false otherwise.
      */
    public boolean hasTableCount() {
      return fieldSetFlags()[33];
    }


    /**
      * Clears the value of the 'table_count' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearTableCount() {
      table_count = null;
      fieldSetFlags()[33] = false;
      return this;
    }

    /**
      * Gets the value of the 'average_consumption_time' field.
      * @return The value.
      */
    public java.lang.CharSequence getAverageConsumptionTime() {
      return average_consumption_time;
    }

    /**
      * Sets the value of the 'average_consumption_time' field.
      * @param value The value of 'average_consumption_time'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setAverageConsumptionTime(java.lang.CharSequence value) {
      validate(fields()[34], value);
      this.average_consumption_time = value;
      fieldSetFlags()[34] = true;
      return this;
    }

    /**
      * Checks whether the 'average_consumption_time' field has been set.
      * @return True if the 'average_consumption_time' field has been set, false otherwise.
      */
    public boolean hasAverageConsumptionTime() {
      return fieldSetFlags()[34];
    }


    /**
      * Clears the value of the 'average_consumption_time' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearAverageConsumptionTime() {
      average_consumption_time = null;
      fieldSetFlags()[34] = false;
      return this;
    }

    /**
      * Gets the value of the 'around_type' field.
      * @return The value.
      */
    public java.lang.CharSequence getAroundType() {
      return around_type;
    }

    /**
      * Sets the value of the 'around_type' field.
      * @param value The value of 'around_type'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder setAroundType(java.lang.CharSequence value) {
      validate(fields()[35], value);
      this.around_type = value;
      fieldSetFlags()[35] = true;
      return this;
    }

    /**
      * Checks whether the 'around_type' field has been set.
      * @return True if the 'around_type' field has been set, false otherwise.
      */
    public boolean hasAroundType() {
      return fieldSetFlags()[35];
    }


    /**
      * Clears the value of the 'around_type' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ChangeStoreSensor.Builder clearAroundType() {
      around_type = null;
      fieldSetFlags()[35] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ChangeStoreSensor build() {
      try {
        ChangeStoreSensor record = new ChangeStoreSensor();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_sn = fieldSetFlags()[1] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.store_id = fieldSetFlags()[2] ? this.store_id : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.store_sn = fieldSetFlags()[3] ? this.store_sn : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.mtime = fieldSetFlags()[4] ? this.mtime : (java.lang.Long) defaultValue(fields()[4]);
        record.operator = fieldSetFlags()[5] ? this.operator : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.platform = fieldSetFlags()[6] ? this.platform : (java.lang.CharSequence) defaultValue(fields()[6]);
        record.name = fieldSetFlags()[7] ? this.name : (java.lang.CharSequence) defaultValue(fields()[7]);
        record.industry = fieldSetFlags()[8] ? this.industry : (java.lang.CharSequence) defaultValue(fields()[8]);
        record.status = fieldSetFlags()[9] ? this.status : (java.lang.Integer) defaultValue(fields()[9]);
        record.rank = fieldSetFlags()[10] ? this.rank : (java.lang.Integer) defaultValue(fields()[10]);
        record.longitude = fieldSetFlags()[11] ? this.longitude : (java.lang.CharSequence) defaultValue(fields()[11]);
        record.latitude = fieldSetFlags()[12] ? this.latitude : (java.lang.CharSequence) defaultValue(fields()[12]);
        record.province = fieldSetFlags()[13] ? this.province : (java.lang.CharSequence) defaultValue(fields()[13]);
        record.city = fieldSetFlags()[14] ? this.city : (java.lang.CharSequence) defaultValue(fields()[14]);
        record.district = fieldSetFlags()[15] ? this.district : (java.lang.CharSequence) defaultValue(fields()[15]);
        record.street_address = fieldSetFlags()[16] ? this.street_address : (java.lang.CharSequence) defaultValue(fields()[16]);
        record.contact_name = fieldSetFlags()[17] ? this.contact_name : (java.lang.CharSequence) defaultValue(fields()[17]);
        record.contract_cellphone = fieldSetFlags()[18] ? this.contract_cellphone : (java.lang.CharSequence) defaultValue(fields()[18]);
        record.contact_phone = fieldSetFlags()[19] ? this.contact_phone : (java.lang.CharSequence) defaultValue(fields()[19]);
        record.contact_email = fieldSetFlags()[20] ? this.contact_email : (java.lang.CharSequence) defaultValue(fields()[20]);
        record.operation_contents = fieldSetFlags()[21] ? this.operation_contents : (java.lang.CharSequence) defaultValue(fields()[21]);
        record.type = fieldSetFlags()[22] ? this.type : (java.lang.Integer) defaultValue(fields()[22]);
        record.open_account_way = fieldSetFlags()[23] ? this.open_account_way : (java.lang.Integer) defaultValue(fields()[23]);
        record.brand_photo = fieldSetFlags()[24] ? this.brand_photo : (java.lang.Boolean) defaultValue(fields()[24]);
        record.indoor_material = fieldSetFlags()[25] ? this.indoor_material : (java.lang.Boolean) defaultValue(fields()[25]);
        record.outdoor_material = fieldSetFlags()[26] ? this.outdoor_material : (java.lang.Boolean) defaultValue(fields()[26]);
        record.product_price = fieldSetFlags()[27] ? this.product_price : (java.lang.Boolean) defaultValue(fields()[27]);
        record.audit_picture = fieldSetFlags()[28] ? this.audit_picture : (java.lang.Boolean) defaultValue(fields()[28]);
        record.video = fieldSetFlags()[29] ? this.video : (java.lang.CharSequence) defaultValue(fields()[29]);
        record.business_hour = fieldSetFlags()[30] ? this.business_hour : (java.lang.CharSequence) defaultValue(fields()[30]);
        record.store_area = fieldSetFlags()[31] ? this.store_area : (java.lang.CharSequence) defaultValue(fields()[31]);
        record.room_count = fieldSetFlags()[32] ? this.room_count : (java.lang.CharSequence) defaultValue(fields()[32]);
        record.table_count = fieldSetFlags()[33] ? this.table_count : (java.lang.CharSequence) defaultValue(fields()[33]);
        record.average_consumption_time = fieldSetFlags()[34] ? this.average_consumption_time : (java.lang.CharSequence) defaultValue(fields()[34]);
        record.around_type = fieldSetFlags()[35] ? this.around_type : (java.lang.CharSequence) defaultValue(fields()[35]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<ChangeStoreSensor>
    WRITER$ = (org.apache.avro.io.DatumWriter<ChangeStoreSensor>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<ChangeStoreSensor>
    READER$ = (org.apache.avro.io.DatumReader<ChangeStoreSensor>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
