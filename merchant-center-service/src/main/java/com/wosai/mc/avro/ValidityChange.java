/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.mc.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class ValidityChange extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -2453530615502201250L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"ValidityChange\",\"namespace\":\"com.wosai.mc.avro\",\"fields\":[{\"name\":\"object_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"table_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"field_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"before_validity\",\"type\":[\"string\",\"null\"]},{\"name\":\"after_validity\",\"type\":[\"string\",\"null\"]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<ValidityChange> ENCODER =
      new BinaryMessageEncoder<ValidityChange>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<ValidityChange> DECODER =
      new BinaryMessageDecoder<ValidityChange>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<ValidityChange> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<ValidityChange> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<ValidityChange>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this ValidityChange to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a ValidityChange from a ByteBuffer. */
  public static ValidityChange fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence object_id;
  @Deprecated public java.lang.CharSequence table_name;
  @Deprecated public java.lang.CharSequence field_name;
  @Deprecated public java.lang.CharSequence before_validity;
  @Deprecated public java.lang.CharSequence after_validity;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public ValidityChange() {}

  /**
   * All-args constructor.
   * @param object_id The new value for object_id
   * @param table_name The new value for table_name
   * @param field_name The new value for field_name
   * @param before_validity The new value for before_validity
   * @param after_validity The new value for after_validity
   */
  public ValidityChange(java.lang.CharSequence object_id, java.lang.CharSequence table_name, java.lang.CharSequence field_name, java.lang.CharSequence before_validity, java.lang.CharSequence after_validity) {
    this.object_id = object_id;
    this.table_name = table_name;
    this.field_name = field_name;
    this.before_validity = before_validity;
    this.after_validity = after_validity;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return object_id;
    case 1: return table_name;
    case 2: return field_name;
    case 3: return before_validity;
    case 4: return after_validity;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: object_id = (java.lang.CharSequence)value$; break;
    case 1: table_name = (java.lang.CharSequence)value$; break;
    case 2: field_name = (java.lang.CharSequence)value$; break;
    case 3: before_validity = (java.lang.CharSequence)value$; break;
    case 4: after_validity = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'object_id' field.
   * @return The value of the 'object_id' field.
   */
  public java.lang.CharSequence getObjectId() {
    return object_id;
  }

  /**
   * Sets the value of the 'object_id' field.
   * @param value the value to set.
   */
  public void setObjectId(java.lang.CharSequence value) {
    this.object_id = value;
  }

  /**
   * Gets the value of the 'table_name' field.
   * @return The value of the 'table_name' field.
   */
  public java.lang.CharSequence getTableName() {
    return table_name;
  }

  /**
   * Sets the value of the 'table_name' field.
   * @param value the value to set.
   */
  public void setTableName(java.lang.CharSequence value) {
    this.table_name = value;
  }

  /**
   * Gets the value of the 'field_name' field.
   * @return The value of the 'field_name' field.
   */
  public java.lang.CharSequence getFieldName() {
    return field_name;
  }

  /**
   * Sets the value of the 'field_name' field.
   * @param value the value to set.
   */
  public void setFieldName(java.lang.CharSequence value) {
    this.field_name = value;
  }

  /**
   * Gets the value of the 'before_validity' field.
   * @return The value of the 'before_validity' field.
   */
  public java.lang.CharSequence getBeforeValidity() {
    return before_validity;
  }

  /**
   * Sets the value of the 'before_validity' field.
   * @param value the value to set.
   */
  public void setBeforeValidity(java.lang.CharSequence value) {
    this.before_validity = value;
  }

  /**
   * Gets the value of the 'after_validity' field.
   * @return The value of the 'after_validity' field.
   */
  public java.lang.CharSequence getAfterValidity() {
    return after_validity;
  }

  /**
   * Sets the value of the 'after_validity' field.
   * @param value the value to set.
   */
  public void setAfterValidity(java.lang.CharSequence value) {
    this.after_validity = value;
  }

  /**
   * Creates a new ValidityChange RecordBuilder.
   * @return A new ValidityChange RecordBuilder
   */
  public static com.wosai.mc.avro.ValidityChange.Builder newBuilder() {
    return new com.wosai.mc.avro.ValidityChange.Builder();
  }

  /**
   * Creates a new ValidityChange RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new ValidityChange RecordBuilder
   */
  public static com.wosai.mc.avro.ValidityChange.Builder newBuilder(com.wosai.mc.avro.ValidityChange.Builder other) {
    return new com.wosai.mc.avro.ValidityChange.Builder(other);
  }

  /**
   * Creates a new ValidityChange RecordBuilder by copying an existing ValidityChange instance.
   * @param other The existing instance to copy.
   * @return A new ValidityChange RecordBuilder
   */
  public static com.wosai.mc.avro.ValidityChange.Builder newBuilder(com.wosai.mc.avro.ValidityChange other) {
    return new com.wosai.mc.avro.ValidityChange.Builder(other);
  }

  /**
   * RecordBuilder for ValidityChange instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<ValidityChange>
    implements org.apache.avro.data.RecordBuilder<ValidityChange> {

    private java.lang.CharSequence object_id;
    private java.lang.CharSequence table_name;
    private java.lang.CharSequence field_name;
    private java.lang.CharSequence before_validity;
    private java.lang.CharSequence after_validity;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.mc.avro.ValidityChange.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.object_id)) {
        this.object_id = data().deepCopy(fields()[0].schema(), other.object_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.table_name)) {
        this.table_name = data().deepCopy(fields()[1].schema(), other.table_name);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.field_name)) {
        this.field_name = data().deepCopy(fields()[2].schema(), other.field_name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.before_validity)) {
        this.before_validity = data().deepCopy(fields()[3].schema(), other.before_validity);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.after_validity)) {
        this.after_validity = data().deepCopy(fields()[4].schema(), other.after_validity);
        fieldSetFlags()[4] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing ValidityChange instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.mc.avro.ValidityChange other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.object_id)) {
        this.object_id = data().deepCopy(fields()[0].schema(), other.object_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.table_name)) {
        this.table_name = data().deepCopy(fields()[1].schema(), other.table_name);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.field_name)) {
        this.field_name = data().deepCopy(fields()[2].schema(), other.field_name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.before_validity)) {
        this.before_validity = data().deepCopy(fields()[3].schema(), other.before_validity);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.after_validity)) {
        this.after_validity = data().deepCopy(fields()[4].schema(), other.after_validity);
        fieldSetFlags()[4] = true;
      }
    }

    /**
      * Gets the value of the 'object_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getObjectId() {
      return object_id;
    }

    /**
      * Sets the value of the 'object_id' field.
      * @param value The value of 'object_id'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder setObjectId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.object_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'object_id' field has been set.
      * @return True if the 'object_id' field has been set, false otherwise.
      */
    public boolean hasObjectId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'object_id' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder clearObjectId() {
      object_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'table_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getTableName() {
      return table_name;
    }

    /**
      * Sets the value of the 'table_name' field.
      * @param value The value of 'table_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder setTableName(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.table_name = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'table_name' field has been set.
      * @return True if the 'table_name' field has been set, false otherwise.
      */
    public boolean hasTableName() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'table_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder clearTableName() {
      table_name = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'field_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getFieldName() {
      return field_name;
    }

    /**
      * Sets the value of the 'field_name' field.
      * @param value The value of 'field_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder setFieldName(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.field_name = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'field_name' field has been set.
      * @return True if the 'field_name' field has been set, false otherwise.
      */
    public boolean hasFieldName() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'field_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder clearFieldName() {
      field_name = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'before_validity' field.
      * @return The value.
      */
    public java.lang.CharSequence getBeforeValidity() {
      return before_validity;
    }

    /**
      * Sets the value of the 'before_validity' field.
      * @param value The value of 'before_validity'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder setBeforeValidity(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.before_validity = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'before_validity' field has been set.
      * @return True if the 'before_validity' field has been set, false otherwise.
      */
    public boolean hasBeforeValidity() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'before_validity' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder clearBeforeValidity() {
      before_validity = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'after_validity' field.
      * @return The value.
      */
    public java.lang.CharSequence getAfterValidity() {
      return after_validity;
    }

    /**
      * Sets the value of the 'after_validity' field.
      * @param value The value of 'after_validity'.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder setAfterValidity(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.after_validity = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'after_validity' field has been set.
      * @return True if the 'after_validity' field has been set, false otherwise.
      */
    public boolean hasAfterValidity() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'after_validity' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.ValidityChange.Builder clearAfterValidity() {
      after_validity = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ValidityChange build() {
      try {
        ValidityChange record = new ValidityChange();
        record.object_id = fieldSetFlags()[0] ? this.object_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.table_name = fieldSetFlags()[1] ? this.table_name : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.field_name = fieldSetFlags()[2] ? this.field_name : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.before_validity = fieldSetFlags()[3] ? this.before_validity : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.after_validity = fieldSetFlags()[4] ? this.after_validity : (java.lang.CharSequence) defaultValue(fields()[4]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<ValidityChange>
    WRITER$ = (org.apache.avro.io.DatumWriter<ValidityChange>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<ValidityChange>
    READER$ = (org.apache.avro.io.DatumReader<ValidityChange>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
