/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.mc.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class CreateMerchantSensor extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -4358360039919002248L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"CreateMerchantSensor\",\"namespace\":\"com.wosai.mc.avro\",\"fields\":[{\"name\":\"merchant_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_sn\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"ctime\",\"type\":[\"long\",\"null\"]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<CreateMerchantSensor> ENCODER =
      new BinaryMessageEncoder<CreateMerchantSensor>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<CreateMerchantSensor> DECODER =
      new BinaryMessageDecoder<CreateMerchantSensor>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<CreateMerchantSensor> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<CreateMerchantSensor> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<CreateMerchantSensor>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this CreateMerchantSensor to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a CreateMerchantSensor from a ByteBuffer. */
  public static CreateMerchantSensor fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_name;
  @Deprecated public java.lang.Long ctime;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public CreateMerchantSensor() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_name The new value for merchant_name
   * @param ctime The new value for ctime
   */
  public CreateMerchantSensor(java.lang.CharSequence merchant_id, java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_name, java.lang.Long ctime) {
    this.merchant_id = merchant_id;
    this.merchant_sn = merchant_sn;
    this.merchant_name = merchant_name;
    this.ctime = ctime;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return merchant_sn;
    case 2: return merchant_name;
    case 3: return ctime;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_id = (java.lang.CharSequence)value$; break;
    case 1: merchant_sn = (java.lang.CharSequence)value$; break;
    case 2: merchant_name = (java.lang.CharSequence)value$; break;
    case 3: ctime = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_name' field.
   * @return The value of the 'merchant_name' field.
   */
  public java.lang.CharSequence getMerchantName() {
    return merchant_name;
  }

  /**
   * Sets the value of the 'merchant_name' field.
   * @param value the value to set.
   */
  public void setMerchantName(java.lang.CharSequence value) {
    this.merchant_name = value;
  }

  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public java.lang.Long getCtime() {
    return ctime;
  }

  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(java.lang.Long value) {
    this.ctime = value;
  }

  /**
   * Creates a new CreateMerchantSensor RecordBuilder.
   * @return A new CreateMerchantSensor RecordBuilder
   */
  public static com.wosai.mc.avro.CreateMerchantSensor.Builder newBuilder() {
    return new com.wosai.mc.avro.CreateMerchantSensor.Builder();
  }

  /**
   * Creates a new CreateMerchantSensor RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new CreateMerchantSensor RecordBuilder
   */
  public static com.wosai.mc.avro.CreateMerchantSensor.Builder newBuilder(com.wosai.mc.avro.CreateMerchantSensor.Builder other) {
    return new com.wosai.mc.avro.CreateMerchantSensor.Builder(other);
  }

  /**
   * Creates a new CreateMerchantSensor RecordBuilder by copying an existing CreateMerchantSensor instance.
   * @param other The existing instance to copy.
   * @return A new CreateMerchantSensor RecordBuilder
   */
  public static com.wosai.mc.avro.CreateMerchantSensor.Builder newBuilder(com.wosai.mc.avro.CreateMerchantSensor other) {
    return new com.wosai.mc.avro.CreateMerchantSensor.Builder(other);
  }

  /**
   * RecordBuilder for CreateMerchantSensor instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<CreateMerchantSensor>
    implements org.apache.avro.data.RecordBuilder<CreateMerchantSensor> {

    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_name;
    private java.lang.Long ctime;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.mc.avro.CreateMerchantSensor.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_name)) {
        this.merchant_name = data().deepCopy(fields()[2].schema(), other.merchant_name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.ctime)) {
        this.ctime = data().deepCopy(fields()[3].schema(), other.ctime);
        fieldSetFlags()[3] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing CreateMerchantSensor instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.mc.avro.CreateMerchantSensor other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_name)) {
        this.merchant_name = data().deepCopy(fields()[2].schema(), other.merchant_name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.ctime)) {
        this.ctime = data().deepCopy(fields()[3].schema(), other.ctime);
        fieldSetFlags()[3] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantName() {
      return merchant_name;
    }

    /**
      * Sets the value of the 'merchant_name' field.
      * @param value The value of 'merchant_name'.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder setMerchantName(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.merchant_name = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_name' field has been set.
      * @return True if the 'merchant_name' field has been set, false otherwise.
      */
    public boolean hasMerchantName() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'merchant_name' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder clearMerchantName() {
      merchant_name = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public java.lang.Long getCtime() {
      return ctime;
    }

    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder setCtime(java.lang.Long value) {
      validate(fields()[3], value);
      this.ctime = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public com.wosai.mc.avro.CreateMerchantSensor.Builder clearCtime() {
      ctime = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public CreateMerchantSensor build() {
      try {
        CreateMerchantSensor record = new CreateMerchantSensor();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_sn = fieldSetFlags()[1] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.merchant_name = fieldSetFlags()[2] ? this.merchant_name : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.ctime = fieldSetFlags()[3] ? this.ctime : (java.lang.Long) defaultValue(fields()[3]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<CreateMerchantSensor>
    WRITER$ = (org.apache.avro.io.DatumWriter<CreateMerchantSensor>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<CreateMerchantSensor>
    READER$ = (org.apache.avro.io.DatumReader<CreateMerchantSensor>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
