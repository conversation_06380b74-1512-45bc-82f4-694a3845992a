package com.wosai.mc.aop;

import com.wosai.app.dto.pwd.PasswordTokenReq;
import com.wosai.app.service.ManagerPasswordService;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mc.annotation.MaskData;
import com.wosai.mc.aop.annotation.RequireMask;
import com.wosai.mc.model.app.ManagePassTokenReq;
import com.wosai.mc.utils.DataMaskingUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

@Slf4j
@Aspect
@Component
public class MaskDataAspect {

    @Autowired
    private ManagerPasswordService managerPasswordService;

    @Pointcut("@annotation(com.wosai.mc.aop.annotation.RequireMask)")
    public void mask() {
    }

    @AfterReturning(value = "mask()", returning = "result")
    public void afterReturning(JoinPoint point, Object result) throws Throwable {
        try {
            // 先只处理平铺的情况 没有处理父类、嵌套对象等情况
            MethodSignature signature = (MethodSignature) point.getSignature();
            Object arg = point.getArgs()[0];
            Method method = signature.getMethod();
            Class<?> clazz = result.getClass();
            if (method.isAnnotationPresent(RequireMask.class) && needMask(arg)) {
                if (result instanceof List) {
                    for (Object o : ((List) result)) {
                        maskData(o, o.getClass());
                    }
                } else {
                    maskData(result, clazz);
                }
            }
        } catch (Throwable e) {
            log.error("roles check error", e);
            throw e;
        }
    }

    private boolean needMask(Object arg) {
        if (arg instanceof ManagePassTokenReq)  {
            String managePasswordToken = ((ManagePassTokenReq) arg).getManagePasswordToken();
            if (WosaiStringUtils.isNotEmpty(managePasswordToken)) {
                return !managerPasswordService.checkToken(new PasswordTokenReq().setPwd_token(managePasswordToken));
            }
        }
        return true;
    }

    private void maskData(Object result, Class<?> clazz) throws Exception {
        for (Field declaredField : clazz.getDeclaredFields()) {
            MaskData maskData = declaredField.getAnnotation(MaskData.class);
            if (Objects.nonNull(maskData)) {
                declaredField.setAccessible(true);
                String maskDataString = DataMaskingUtils.maskData(String.valueOf(declaredField.get(result)), maskData.value());
                declaredField.set(result, maskDataString);
            }
        }
        Class<?> superclass = clazz.getSuperclass();
        if (Objects.nonNull(superclass) && !superclass.equals(Object.class)) {
            maskData(result, superclass);
        }
    }
}
