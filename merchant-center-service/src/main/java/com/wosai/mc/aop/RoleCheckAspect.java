package com.wosai.mc.aop;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.mc.aop.annotation.RequireRoles;
import com.wosai.mc.model.app.BaseAppReq;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;

@Slf4j
@Aspect
@Component
public class RoleCheckAspect {

    @Pointcut("@annotation(com.wosai.mc.aop.annotation.RequireRoles)")
    public void roleCheck() {
    }

    @Before("roleCheck()")
    public void around(JoinPoint point) throws Throwable {
        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            Object arg = point.getArgs()[0];
            if (arg instanceof BaseAppReq) {
                RequireRoles requireRoles = method.getAnnotation(RequireRoles.class);
                String role = ((BaseAppReq) arg).getRole();
                if (!Arrays.asList(requireRoles.value()).contains(role)) {
                    log.error("not permission session for roles {}, method {}", role, method.getName());
                    throw new CommonInvalidParameterException("无权访问");
                }
            }
        } catch (Throwable e) {
            log.error("roles check error", e);
            throw e;
        }
    }
}
