package com.wosai.mc.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要的角色 只有拥有指定角色的才能访问
 * 数组类型，角色只要满足数组内的其中一个即可
 * <AUTHOR>
 */
@Target({ElementType.TYPE,ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireRoles {
    String[] value();
}