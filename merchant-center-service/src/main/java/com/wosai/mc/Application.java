package com.wosai.mc;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wosai.database.instrumentation.springboot.v2.EnableDataSourceTranslate;
import com.wosai.databus.event.audit.store.events.StoreBusinessLicenseAuditEvent;
import com.wosai.web.rpc.EnableJsonRpc;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2020-08-04
 */
@EnableJsonRpc
@EnableDataSourceTranslate
@EnableApolloConfig
@EnableScheduling
@MapperScan("com.wosai.mc.mapper")
@ComponentScan({"com.wosai.mc","com.wosai.oss"})
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        // 随便new一个AbstractEvent的子类，在项目启动之前将AbstractEvent里面的static代码块执行完毕
        StoreBusinessLicenseAuditEvent event = new StoreBusinessLicenseAuditEvent();
        SpringApplication.run(Application.class, args);
    }
}
