package com.wosai.mc.volcengine.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DataCenterMessageBody {
    /**
     * 判断是事件还是用户属性，以便调取不同的api进行上报数据 EVENT
     */
    private String message_type;
    /**
     * 用户uuid
     */
    private String user_unique_id;
    /**
     * 应用id
     */
    private String app_id;
    /**
     * 事件名称
     */
    private String event_name;
    /**
     * 业务方自己定义即可，事件参数
     */
    private Object event_params;



}
