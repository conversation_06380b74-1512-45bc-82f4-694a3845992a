package com.wosai.mc.volcengine.dataCenter;

import com.alibaba.fastjson.JSONObject;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.mc.config.exception.McException;
import com.wosai.mc.volcengine.dto.DataCenterMessageBody;
import com.wosai.mc.volcengine.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.Map;
import java.util.Objects;

/**
 * 数据中心服务
 */
@Slf4j
@Component
public class DataCenterProducer {

    @Autowired
    @Qualifier("dataCenterKafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String TOPIC = "analytics_data_volcengine_push";

    /**
     * 发送火山消息
     *
     * @param body
     */
    public void publish(DataCenterMessageBody body) {
        if (Objects.isNull(body)) {
            throw new McException("当前事件为空.");
        }
        if (StringUtils.isBlank(body.getMessage_type())) {
            body.setMessage_type(MessageTypeEnum.EVENT.name());
        }
        if (Objects.isNull(body.getEvent_params())) {
            throw new McException(String.format("当前事件eventParams为空. body:%s", JSONObject.toJSONString(body)));
        }
        //追加时间戳
        appendTimestamp(body.getEvent_params());

        String key = body.getUser_unique_id();
        if (Objects.isNull(key)) {
            key = "N/A";
        }
        String bodyStr = null;
        try {
            bodyStr = JSONObject.toJSONString(body);
            ListenableFuture<SendResult<String, Object>> result = kafkaTemplate
                    .send(TOPIC, key, bodyStr);
            final String finalBodyStr = bodyStr;
            result.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
                @Override
                public void onFailure(Throwable ex) {
                    log.error("发送火山消息失败. body:{} ", finalBodyStr, ex);
                }

                @Override
                public void onSuccess(SendResult<String, Object> result) {
                    log.info("发送火山消息成功 body:{} ", finalBodyStr);
                }
            });
        } catch (Exception e) {
            log.error("写入火山消息失败. body:{}", bodyStr, e);
            throw new CommonPubBizException("写入kafka失败", e);
        }
    }


    private void appendTimestamp(Object eventParams) {
        try {
            if (!(eventParams instanceof Map)) {
                return;
            }
            Map<String, Object> eventParamsMap = (Map<String, Object>) eventParams;
            if (MapUtils.isEmpty(eventParamsMap)) {
                return;
            }
            final String timestampFiled = "timestamp";
            if (eventParamsMap.get(timestampFiled) != null) {
                return;
            }
            eventParamsMap.put(timestampFiled, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("appendTimestamp error eventParams:{},cause:{}", JSONObject.toJSONString(eventParams), e);
        }
    }

}
