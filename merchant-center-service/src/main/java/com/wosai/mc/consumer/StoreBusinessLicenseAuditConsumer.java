package com.wosai.mc.consumer;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.databus.LogEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.audit.store.events.StoreBusinessLicenseAuditEvent;
import com.wosai.databus.event.audit.store.events.StoreLicenseAuditEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.mc.biz.LicenseBiz;
import com.wosai.mc.biz.StoreBusinessLicenseBiz;
import com.wosai.mc.model.License;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.StoreBusinessLicenseInfo;
import com.wosai.mc.model.req.VerifyLicenseReq;
import com.wosai.mc.model.req.VerifyStoreBusinessLicenseReq;
import com.wosai.mc.service.LicenseService;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.StoreBusinessLicenseService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.service.McPreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.util.List;
import java.util.Map;
import java.util.Objects;
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
@Component
@Slf4j
public class StoreBusinessLicenseAuditConsumer {
    private static final String STORE_BUSINESS_LICENSE_AUDIT = "callback_OSP_store-audit";

    protected EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();

    @Autowired
    private StoreBusinessLicenseService storeBusinessLicenseService;
    @Autowired
    private com.wosai.upay.core.service.StoreBusinessLicenseService coreStoreBusinessLicenseService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private McPreService mcPreService;
    @Autowired
    private StoreService storeService;


    @KafkaListener(topics = STORE_BUSINESS_LICENSE_AUDIT, containerFactory = "auditKafkaListenerContainerFactory")
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        GenericRecord value = record.value();
        if (value == null) {
            return;
        }

        AbstractEvent event = null;
        try {
            ByteBuffer buffer = (ByteBuffer) value.get(LogEntry.EVENT);
            event = (AbstractEvent) persistenceHelper.fromJsonBytes(buffer.array(), AbstractEvent.class);
            event.setSeq((Long) value.get(LogEntry.SEQ));
            event.setTimestamp((Long) value.get(LogEntry.TIMESTAMP));
            handleEvent(event);
        } catch (Exception e) {
            log.error("dataBus consume error {}", event, e);
            throw e;
        }


    }

    /**
     * 不复用的时候两个事件都发 ，
     * 复用商户维度时，只发第2个事件。
     */
    private void handleEvent(AbstractEvent event) {
        if (event instanceof StoreBusinessLicenseAuditEvent) {
            handleStoreBusinessLicenseAuditEvent((StoreBusinessLicenseAuditEvent) event);
        } else if (event instanceof StoreLicenseAuditEvent) {
            handleStoreLicenseAuditEvent((StoreLicenseAuditEvent) event);
        } else {
            log.info("未知事件 {}", event);
        }

    }


    /**
     * @param event 门店营业执照审核,通过认为都通过.
     *              不通过,认为全部不通过
     *              1-待审核; 2-审核通过; 3-审核驳回
     */
    private void handleStoreBusinessLicenseAuditEvent(StoreBusinessLicenseAuditEvent event) {
        log.info("处理门店营业执照审核结果  {}", JSON.toJSONString(event));
        String storeId = event.getStoreId();
        if (storeId == null) {
            return;
        }
        StoreBusinessLicenseInfo storeBusinessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeId, "StoreBusinessLicenseAuditEvent");

        if (1 == event.getStatus()) {
            storeBusinessLicenseService.verifyStoreBusinessLicense(new VerifyStoreBusinessLicenseReq().setStoreId(storeId).setVerifyStatus("1").setVerifyParams(StoreBusinessLicenseBiz.getAllAuditProp()));
            licenseService.verifyLicense(new VerifyLicenseReq().setBusiness_license_id(storeBusinessLicense.getId()).setVerifyStatus("1").setVerifyParams(LicenseBiz.getAllAuditProp()));
            return;
        }

        //List<License> licenses = storeBusinessLicense.getTrade_license_list();

        if (2 == event.getStatus()) {
            storeBusinessLicenseService.copyMcInfoToStoreBusinessLicenseByStoreId(storeId, "StoreBusinessLicenseAuditEvent");
            storeBusinessLicenseService.verifyStoreBusinessLicense(new VerifyStoreBusinessLicenseReq().setStoreId(storeId).setVerifyStatus("2").setVerifyParams(StoreBusinessLicenseBiz.getAllAuditProp()));
            //if (WosaiCollectionUtils.isNotEmpty(licenses)) {
            //licenseService.copyMcInfoToLicenseByBusinessLicenseId(storeBusinessLicense.getId(), "StoreBusinessLicenseAuditEvent");
            licenseService.verifyLicense(new VerifyLicenseReq().setBusiness_license_id(storeBusinessLicense.getId()).setVerifyStatus("2").setVerifyParams(LicenseBiz.getAllAuditProp()));
            // }

            MerchantBusinessLicenseInfo merchantBusinessLicenseOrigin = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(storeBusinessLicense.getMerchant_id(), null);
            mcPreService.deleteExcessData("license", merchantBusinessLicenseOrigin.getId());

        } /*else if (3 == event.getStatus()) {
            storeBusinessLicenseService.copyMcInfoToStoreBusinessLicenseByStoreId(storeId, "StoreBusinessLicenseAuditEvent");
            storeBusinessLicenseService.verifyStoreBusinessLicense(new VerifyStoreBusinessLicenseReq().setStoreId(storeId).setVerifyStatus("3").setVerifyParams(StoreBusinessLicenseBiz.getAllAuditProp()));
            if (WosaiCollectionUtils.isNotEmpty(licenses)) {
                licenseService.copyMcInfoToLicenseByBusinessLicenseId(storeBusinessLicense.getId(), "StoreBusinessLicenseAuditEvent");
                licenseService.verifyLicense(new VerifyLicenseReq().setBusiness_license_id(storeBusinessLicense.getId()).setVerifyStatus("3").setVerifyParams(LicenseBiz.getAllAuditProp()));
            }
        }*/
    }

    /**
     * @param event 门店许可证审核,通过认为许可证都通过. 复用的时候才发 ，
     *              不通过,认为全部不通过
     *              1-待审核; 2-审核通过; 3-审核驳回
     */
    private void handleStoreLicenseAuditEvent(StoreLicenseAuditEvent event) {
        log.info("处理门店许可证审核结果  {}", JSON.toJSONString(event));
        String storeId = event.getStoreId();
        if (storeId == null) {
            return;
        }
        if (1 == event.getStatus()) {
            log.info("门店许可证审核进行中...  event : {}", event);
            return;
        }
        StoreBusinessLicenseInfo storeBusinessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeId, "LicenseAuditEvent");
        if (Objects.isNull(storeBusinessLicense)) {
            log.info("消费许可证审核事件,门店营业执照为空,storeId : {}", storeId);
            return;
        }

        if (storeBusinessLicense.isUse_merchant_business_license()) {
            //复用商户营业执照情况下, 更新商户维度数据
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(event.getMerchantId(), null);
            //复用场景下,无需审核的状态(4),执行和 (2) 一样的逻辑
            if (2 == event.getStatus() || 4 == event.getStatus()) {
                storeBusinessLicenseService.copyMcInfoToStoreBusinessLicenseByStoreId(storeId, "LicenseAuditEvent");
                licenseService.copyMcInfoToLicenseByBusinessLicenseId(merchantBusinessLicense.getId(), "LicenseAuditEvent");
                licenseService.verifyLicense(new VerifyLicenseReq().setBusiness_license_id(merchantBusinessLicense.getId()).setVerifyStatus("2").setVerifyParams(LicenseBiz.getAllAuditProp()));
                storeService.copyMcInfoToStoreByStoreId(storeId, "LicenseAuditEvent");
                //复用关系时,审核通过时合并商户维度数据后,删除门店维度许可证中间表数据(如有)
                Map<String, Object> storeBusinessLicenseOrigin = coreStoreBusinessLicenseService.getStoreBusinessLicenseByStoreId(storeId);
                mcPreService.deleteExcessData("license", MapUtils.getString(storeBusinessLicenseOrigin, "id"));
            }
        } else {
            //中间表为不复用 , 则发送第一个事件,已合并过数据
        }
    }


}

