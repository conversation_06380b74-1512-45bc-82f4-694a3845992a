package com.wosai.mc.consumer;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.avro.ValidityChange;
import com.wosai.mc.utils.IdentityCardUtils;
import com.wosai.mc.volcengine.dataCenter.DataCenterProducer;
import com.wosai.mc.volcengine.dto.DataCenterMessageBody;
import com.wosai.mc.volcengine.enums.MessageTypeEnum;
import com.wosai.upay.core.model.License;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.StoreBusinessLicence;
import com.wosai.upay.core.service.LicenseService;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.StoreBusinessLicenseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
@Component
@Slf4j
public class CanalEventConsumer {

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private StoreBusinessLicenseService storeBusinessLicenseService;
    @Autowired
    private LicenseService licenseService;

    @Autowired
    @Qualifier("aliAvroKafkaTemplate")
    private KafkaTemplate<String, Object> aliAvroKafkaTemplate;
    @Autowired
    private DataCenterProducer dataCenterProducer;

    @Value("${datacenter.appid.bmerchant}")
    private String bMerchantAppId;
    @Value("${datacenter.appid.store}")
    private String storeAppId;

    /**
     * 有效期更新的事件
     */
    private static final String VALIDITY_CHANGE_TOPIC = "events_CUA_canal_validity_change";
    /**
     * 商户主体有效期更新火山事件
     */
    private static final String MERCHANT_VALIDITY_UPDATE_EVENT = "MerchantLicenseAndCertificateUpdate";
    /**
     * 门店主体有效期更新火山事件
     */
    private static final String STORE_VALIDITY_UPDATE_EVENT = "StoreLicenseAndCertificateUpdate";
    /**
     * 商户营业执照的ID格式
     */
    private static final Pattern MERCHANT_BUSINESS_LICENSE_ID_PATTERN = Pattern.compile("\\d{1,20}");
    private static final List<String> HANDLE_TABLES = Arrays.asList("merchant_business_license", "merchant_bank_account", "store_business_license", "license");


    @KafkaListener(topics = "events_CUA_canal", containerFactory = "canalKafkaListenerContainerFactory")
    public void consume(ConsumerRecord<String, String> record) {
        String value = record.value();
        if (value == null) {
            return;
        }
        try {
            handleBinLog(value);
        } catch (Exception e) {
            log.error("处理binlog事件失败: {} ", value, e);
        }
    }

    private void handleBinLog(String binLogString) {
        Map binLog = JSON.parseObject(binLogString, Map.class);
        String type = BeanUtil.getPropString(binLog, "type", "");
        String database = BeanUtil.getPropString(binLog, "database");
        if ("UPDATE".equals(type) && "upay_core".equals(database)) {
            String tableName = BeanUtil.getPropString(binLog, "table");
            if (HANDLE_TABLES.contains(tableName)) {
                log.info("binlog: {}", binLogString);
            } else {
                return;
            }
            List<Map> afterDatas = (List<Map>) BeanUtil.getProperty(binLog, "data");
            afterDatas = afterDatas == null ? new ArrayList<>() : afterDatas;
            List<Map> beforeDatas = (List<Map>) BeanUtil.getProperty(binLog, "old");
            for (int i = 0; i < afterDatas.size(); i++) {
                Map after = getData(afterDatas, i);
                Map before = getData(beforeDatas, i);
                Map newBefore = new HashMap();
                newBefore.putAll(after);
                newBefore.putAll(before);
                switch (tableName) {
                    case "merchant_business_license":
                        handleBusinessLicense(newBefore, after);
                        break;
                    case "merchant_bank_account":
                        handleMerchantBankAccount(newBefore, after);
                        break;
                    case "store_business_license":
                        handleStoreBusinessLicense(newBefore, after);
                        break;
                    case "license":
                        handleTradeLicense(newBefore, after);
                        break;
                    default:
                }
            }
        }
    }

    private void handleBusinessLicense(Map before, Map after) {
        String merchantId = BeanUtil.getPropString(after, MerchantBusinessLicence.MERCHANT_ID);
        String beforeValidity = BeanUtil.getPropString(before, MerchantBusinessLicence.VALIDITY, "");
        String afterValidity = BeanUtil.getPropString(after, MerchantBusinessLicence.VALIDITY, "");
        if (!Objects.equals(beforeValidity, afterValidity)) {
            sendValidityChangeMessage(ValidityChange.newBuilder()
                    .setObjectId(merchantId)
                    .setTableName("merchant_business_license")
                    .setFieldName("validity")
                    .setBeforeValidity(beforeValidity)
                    .setAfterValidity(afterValidity)
                    .build());

            dataCenterProducer.publish(new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.EVENT.name())
                    .setApp_id(bMerchantAppId)
                    .setUser_unique_id(merchantId)
                    .setEvent_name(MERCHANT_VALIDITY_UPDATE_EVENT)
                    .setEvent_params(CollectionUtil.hashMap(
                            "update_content", "营业执照",
                            "license_validity_days", IdentityCardUtils.calculateDiffDay(afterValidity)
                    )));

        }
        String beforeIdValidity = BeanUtil.getPropString(before, MerchantBusinessLicence.ID_VALIDITY, "");
        String afterIdValidity = BeanUtil.getPropString(after, MerchantBusinessLicence.ID_VALIDITY, "");
        if (!Objects.equals(beforeIdValidity, afterIdValidity)) {
            sendValidityChangeMessage(ValidityChange.newBuilder()
                    .setObjectId(BeanUtil.getPropString(before, MerchantBusinessLicence.MERCHANT_ID))
                    .setTableName("merchant_business_license")
                    .setFieldName("id_validity")
                    .setBeforeValidity(beforeIdValidity)
                    .setAfterValidity(afterIdValidity)
                    .build());
            dataCenterProducer.publish(new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.EVENT.name())
                    .setApp_id(bMerchantAppId)
                    .setUser_unique_id(merchantId)
                    .setEvent_name(MERCHANT_VALIDITY_UPDATE_EVENT)
                    .setEvent_params(CollectionUtil.hashMap(
                            "update_content", "法人代表证件",
                            "legal_validity_days", IdentityCardUtils.calculateDiffDay(afterIdValidity)
                    )));
        }
    }

    private void handleMerchantBankAccount(Map before, Map after) {
        String merchantId = BeanUtil.getPropString(after, MerchantBankAccount.MERCHANT_ID);
        String beforeIdValidity = BeanUtil.getPropString(before, MerchantBankAccount.ID_VALIDITY, "");
        String afterIdValidity = BeanUtil.getPropString(after, MerchantBankAccount.ID_VALIDITY, "");
        if (!Objects.equals(beforeIdValidity, afterIdValidity)) {
            sendValidityChangeMessage(ValidityChange.newBuilder()
                    .setObjectId(BeanUtil.getPropString(before, MerchantBankAccount.MERCHANT_ID))
                    .setTableName("merchant_bank_account")
                    .setFieldName("id_validity")
                    .setBeforeValidity(beforeIdValidity)
                    .setAfterValidity(afterIdValidity)
                    .build());
            dataCenterProducer.publish(new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.EVENT.name())
                    .setApp_id(bMerchantAppId)
                    .setUser_unique_id(merchantId)
                    .setEvent_name(MERCHANT_VALIDITY_UPDATE_EVENT)
                    .setEvent_params(CollectionUtil.hashMap(
                            "update_content", "结算人证件",
                            "clearing_validity_days", IdentityCardUtils.calculateDiffDay(afterIdValidity)
                    )));
        }
    }

    private void handleStoreBusinessLicense(Map before, Map after) {
        String storeId = BeanUtil.getPropString(after, StoreBusinessLicence.STORE_ID);
        String beforeValidity = BeanUtil.getPropString(before, StoreBusinessLicence.VALIDITY, "");
        String afterValidity = BeanUtil.getPropString(after, StoreBusinessLicence.VALIDITY, "");
        if (!Objects.equals(beforeValidity, afterValidity)) {
            sendValidityChangeMessage(ValidityChange.newBuilder()
                    .setObjectId(BeanUtil.getPropString(before, StoreBusinessLicence.STORE_ID))
                    .setTableName("store_business_license")
                    .setFieldName("validity")
                    .setBeforeValidity(beforeValidity)
                    .setAfterValidity(afterValidity)
                    .build());
            dataCenterProducer.publish(new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.EVENT.name())
                    .setApp_id(storeAppId)
                    .setUser_unique_id(storeId)
                    .setEvent_name(STORE_VALIDITY_UPDATE_EVENT)
                    .setEvent_params(CollectionUtil.hashMap(
                            "update_content", "营业执照",
                            "license_validity_days", IdentityCardUtils.calculateDiffDay(afterValidity)
                    )));
        }
        String beforeIdValidity = BeanUtil.getPropString(before, StoreBusinessLicence.ID_VALIDITY, "");
        String afterIdValidity = BeanUtil.getPropString(after, StoreBusinessLicence.ID_VALIDITY, "");
        if (!Objects.equals(beforeIdValidity, afterIdValidity)) {
            sendValidityChangeMessage(ValidityChange.newBuilder()
                    .setObjectId(BeanUtil.getPropString(before, StoreBusinessLicence.STORE_ID))
                    .setTableName("store_business_license")
                    .setFieldName("id_validity")
                    .setBeforeValidity(beforeIdValidity)
                    .setAfterValidity(afterIdValidity)
                    .build());
            dataCenterProducer.publish(new DataCenterMessageBody()
                    .setMessage_type(MessageTypeEnum.EVENT.name())
                    .setApp_id(storeAppId)
                    .setUser_unique_id(storeId)
                    .setEvent_name(STORE_VALIDITY_UPDATE_EVENT)
                    .setEvent_params(CollectionUtil.hashMap(
                            "update_content", "法人代表证件",
                            "legal_validity_days", IdentityCardUtils.calculateDiffDay(afterIdValidity)
                    )));
        }
    }

    private void handleTradeLicense(Map before, Map after) {
        String licenseId = BeanUtil.getPropString(after, DaoConstants.ID);
        String businessLicenseId = BeanUtil.getPropString(after, License.BUSINESS_LICENSE_ID);
        String beforeValidity = BeanUtil.getPropString(before, License.LICENSE_VALIDITY, "");
        String afterValidity = BeanUtil.getPropString(after, License.LICENSE_VALIDITY, "");
        if (!Objects.equals(beforeValidity, afterValidity)) {
            sendValidityChangeMessage(ValidityChange.newBuilder()
                    .setObjectId(BeanUtil.getPropString(before, DaoConstants.ID))
                    .setTableName("license")
                    .setFieldName("license_validity")
                    .setBeforeValidity(beforeValidity)
                    .setAfterValidity(afterValidity)
                    .build());
            // 找到所有的许可证的有效期，判断过期时间最近的有效期是否发生了变化
            String uniqueId;
            String appId;
            String eventName;
            if (MERCHANT_BUSINESS_LICENSE_ID_PATTERN.matcher(businessLicenseId).matches()) {
                Map<String, Object> licenseInfo = merchantBusinessLicenseService.getBusinessLicenseById(Long.parseLong(businessLicenseId));
                appId = bMerchantAppId;
                uniqueId = BeanUtil.getPropString(licenseInfo, MerchantBusinessLicence.MERCHANT_ID);
                eventName = MERCHANT_VALIDITY_UPDATE_EVENT;
            } else {
                Map<String, Object> licenseInfo = storeBusinessLicenseService.getStoreBusinessLicenseById(businessLicenseId);
                appId = storeAppId;
                uniqueId = BeanUtil.getPropString(licenseInfo, StoreBusinessLicence.STORE_ID);
                eventName = STORE_VALIDITY_UPDATE_EVENT;
            }
            List licenses = licenseService.getLicenseByBusinessLicenseId(businessLicenseId);
            if (licenses.size() == 1) {
                dataCenterProducer.publish(new DataCenterMessageBody()
                        .setMessage_type(MessageTypeEnum.EVENT.name())
                        .setApp_id(appId)
                        .setUser_unique_id(uniqueId)
                        .setEvent_name(eventName)
                        .setEvent_params(CollectionUtil.hashMap(
                                "update_content", "许可证",
                                "permit_validity_days", IdentityCardUtils.calculateDiffDay(afterValidity)
                        )));
                return;
            }
            List<String> licenseValidities = (List<String>) licenses.stream().filter(r -> !BeanUtil.getPropString(r, DaoConstants.ID).equals(licenseId)).map(r -> BeanUtil.getPropString(r, License.LICENSE_VALIDITY)).collect(Collectors.toList());
            licenseValidities.sort(Comparator.comparingInt(IdentityCardUtils::calculateDiffDay));
            int earliestDiff = IdentityCardUtils.calculateDiffDay(licenseValidities.get(0));
            int afterDiff = Math.min(IdentityCardUtils.calculateDiffDay(afterValidity), earliestDiff);
            int beforeDiff = Math.min(IdentityCardUtils.calculateDiffDay(beforeValidity), earliestDiff);
            if (afterDiff != beforeDiff) {
                dataCenterProducer.publish(new DataCenterMessageBody()
                        .setMessage_type(MessageTypeEnum.EVENT.name())
                        .setApp_id(appId)
                        .setUser_unique_id(uniqueId)
                        .setEvent_name(eventName)
                        .setEvent_params(CollectionUtil.hashMap(
                                "update_content", "许可证",
                                "permit_validity_days", afterDiff
                        )));
            }
        }
    }

    private Map getData(List<Map> datas, int index) {
        if (datas != null && index < datas.size()) {
            return datas.get(index);
        }
        return new HashMap<>();
    }

    private void sendValidityChangeMessage(ValidityChange validityChange) {
        aliAvroKafkaTemplate.send(VALIDITY_CHANGE_TOPIC, validityChange).addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
            @Override
            public void onFailure(Throwable ex) {
                log.error("发送有效期变更事件异常: {}", validityChange, ex);
            }

            @Override
            public void onSuccess(SendResult<String, Object> result) {
                log.info("发送有效期变更事件成功: {}", validityChange);
            }
        });
    }

}

