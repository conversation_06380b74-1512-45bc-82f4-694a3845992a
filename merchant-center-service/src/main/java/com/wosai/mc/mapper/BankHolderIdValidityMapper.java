package com.wosai.mc.mapper;

import com.wosai.mc.entity.BankHolderIdValidity;

import java.util.List;

public interface BankHolderIdValidityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BankHolderIdValidity record);

    int insertSelective(BankHolderIdValidity record);

    BankHolderIdValidity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BankHolderIdValidity record);

    int updateByPrimaryKey(BankHolderIdValidity record);

    List<BankHolderIdValidity> selectByPt(String pt);
}