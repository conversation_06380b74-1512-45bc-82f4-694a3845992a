package com.wosai.mc.mapper;

import com.wosai.mc.entity.StoreListWaitHandle;
import com.wosai.mc.entity.StoreListWaitHandleExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface StoreListWaitHandleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StoreListWaitHandle record);

    int insertSelective(StoreListWaitHandle record);

    List<StoreListWaitHandle> selectByExampleWithBLOBs(StoreListWaitHandleExample example);

    List<StoreListWaitHandle> selectByExample(StoreListWaitHandleExample example);

    StoreListWaitHandle selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StoreListWaitHandle record);

    int updateByPrimaryKeyWithBLOBs(StoreListWaitHandle record);

    int updateByPrimaryKey(StoreListWaitHandle record);

    @Select("select * from store_list_wait_handle where merchant_id = #{merchantId}")
    List<StoreListWaitHandle> selectByMerchantId(@Param("merchantId") String merchantId);

    @Select("select * from store_list_wait_handle where merchant_id = #{merchantId} and is_merged_name_address = 0")
    List<StoreListWaitHandle> selectByMerchantIdNotMergeAddress(@Param("merchantId") String merchantId);

    @Select("select * from store_list_wait_handle where merchant_id = #{merchantId} and is_merged_name_address = 1")
    List<StoreListWaitHandle> selectByMerchantIdMergedAddress(@Param("merchantId") String merchantId);

    @Select("select * from store_list_wait_handle where store_id = #{storeId}")
    StoreListWaitHandle selectByStoreId(String storeId);

    int updateNameAddressNoticeTime(@Param("ids") List<Long> ids, @Param("noticeTime") Date noticeTime);

    int update10DaysNoticeTime(@Param("ids") List<Long> ids, @Param("noticeTime") Date noticeTime);

    int update20DaysNoticeTime(@Param("ids") List<Long> ids, @Param("noticeTime") Date noticeTime);
}