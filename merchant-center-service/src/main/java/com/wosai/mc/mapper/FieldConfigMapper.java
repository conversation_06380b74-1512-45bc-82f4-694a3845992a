package com.wosai.mc.mapper;

import com.wosai.mc.model.FieldConfig;

import com.wosai.mc.entity.FieldConfigExample;
import java.util.List;

public interface FieldConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FieldConfig record);

    int insertSelective(FieldConfig record);

    List<FieldConfig> selectByExample(FieldConfigExample example);

    FieldConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FieldConfig record);

    int updateByPrimaryKey(FieldConfig record);
}