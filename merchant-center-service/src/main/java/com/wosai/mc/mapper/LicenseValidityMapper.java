package com.wosai.mc.mapper;

import com.wosai.mc.entity.LicenseValidity;

import java.util.List;

public interface LicenseValidityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LicenseValidity record);

    int insertSelective(LicenseValidity record);

    LicenseValidity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LicenseValidity record);

    int updateByPrimaryKey(LicenseValidity record);

    List<LicenseValidity> selectByPt(String pt);
}