package com.wosai.mc.mapper;

import com.wosai.mc.entity.StoreListHandleSuccess;
import com.wosai.mc.entity.StoreListHandleSuccessExample;
import java.util.List;

public interface StoreListHandleSuccessMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StoreListHandleSuccess record);

    int insertSelective(StoreListHandleSuccess record);

    List<StoreListHandleSuccess> selectByExampleWithBLOBs(StoreListHandleSuccessExample example);

    List<StoreListHandleSuccess> selectByExample(StoreListHandleSuccessExample example);

    StoreListHandleSuccess selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StoreListHandleSuccess record);

    int updateByPrimaryKeyWithBLOBs(StoreListHandleSuccess record);

    int updateByPrimaryKey(StoreListHandleSuccess record);
}