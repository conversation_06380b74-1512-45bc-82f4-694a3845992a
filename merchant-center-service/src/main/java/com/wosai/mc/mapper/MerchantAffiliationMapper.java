package com.wosai.mc.mapper;

import com.wosai.mc.entity.MerchantAffiliation;

public interface MerchantAffiliationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MerchantAffiliation record);

    int insertSelective(MerchantAffiliation record);

    MerchantAffiliation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantAffiliation record);

    int updateByPrimaryKey(MerchantAffiliation record);

    MerchantAffiliation selectByMerchantSn(String merchantSn);
}