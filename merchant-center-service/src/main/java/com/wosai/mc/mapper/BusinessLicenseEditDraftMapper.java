package com.wosai.mc.mapper;

import com.wosai.mc.entity.BusinessLicenseEditDraft;
import com.wosai.mc.entity.BusinessLicenseEditDraftExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessLicenseEditDraftMapper {

    int deleteByExample(BusinessLicenseEditDraftExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BusinessLicenseEditDraft record);

    int insertSelective(BusinessLicenseEditDraft record);

    List<BusinessLicenseEditDraft> selectByExampleWithBLOBs(BusinessLicenseEditDraftExample example);

    List<BusinessLicenseEditDraft> selectByExample(BusinessLicenseEditDraftExample example);

    BusinessLicenseEditDraft selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BusinessLicenseEditDraft record, @Param("example") BusinessLicenseEditDraftExample example);

    int updateByExampleWithBLOBs(@Param("record") BusinessLicenseEditDraft record, @Param("example") BusinessLicenseEditDraftExample example);

    int updateByExample(@Param("record") BusinessLicenseEditDraft record, @Param("example") BusinessLicenseEditDraftExample example);

    int updateByPrimaryKeySelective(BusinessLicenseEditDraft record);

    int updateByPrimaryKeyWithBLOBs(BusinessLicenseEditDraft record);

    int updateByPrimaryKey(BusinessLicenseEditDraft record);
}