package com.wosai.mc.mapper;

import com.wosai.mc.entity.NingboMchPool;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface NingboMchPoolMapper {
    int deleteByPrimaryKey(Long id);

    int insert(NingboMchPool record);

    int insertSelective(NingboMchPool record);

    NingboMchPool selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(NingboMchPool record);

    int updateByPrimaryKey(NingboMchPool record);

    @Select("select * from ningbo_mch_pool where city = #{city} limit 1")
    NingboMchPool selectOneByCity(@Param("city") String city);
}