package com.wosai.mc.mapper;

import com.wosai.mc.entity.MerchantIdList;
import com.wosai.mc.entity.MerchantIdListExample;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MerchantIdListMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MerchantIdList record);

    int insertSelective(MerchantIdList record);

    List<MerchantIdList> selectByExample(MerchantIdListExample example);

    MerchantIdList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantIdList record);

    int updateByPrimaryKey(MerchantIdList record);


    @Select("select id from merchant_id_list order by id asc limit 1")
    MerchantIdList selectMinId();

    @Select("select * from merchant_id_list where id >= #{id} order by id asc limit 100")
    List<MerchantIdList> selectOrderByIdAsc(Long id);
}