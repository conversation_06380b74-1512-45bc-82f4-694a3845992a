package com.wosai.mc.mapper;

import com.wosai.mc.entity.LicenseLegalPersonIdValidity;

import java.util.List;

public interface LicenseLegalPersonIdValidityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LicenseLegalPersonIdValidity record);

    int insertSelective(LicenseLegalPersonIdValidity record);

    LicenseLegalPersonIdValidity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LicenseLegalPersonIdValidity record);

    int updateByPrimaryKey(LicenseLegalPersonIdValidity record);

    List<LicenseLegalPersonIdValidity> selectByPt(String pt);
}