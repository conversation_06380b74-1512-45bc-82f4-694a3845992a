package com.wosai.mc.controller;

import com.aliyun.oss.common.auth.Credentials;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.wosai.mc.model.resp.AliyunTokenResp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/8/31 Time: 10:05
 */
@RestController
@RequestMapping("/support")
public class SupportController {

    @Resource
    private CredentialsProvider credentialsProvider;


    @GetMapping("/aliyun/token")
    public AliyunTokenResp queryAliyunToken() {
        Credentials credentials = credentialsProvider.getCredentials();
        return new AliyunTokenResp()
                .setAccessKeyId(credentials.getAccessKeyId())
                .setSecretAccessKey(credentials.getSecretAccessKey())
                .setSecurityToken(credentials.getSecurityToken());
    }

}
