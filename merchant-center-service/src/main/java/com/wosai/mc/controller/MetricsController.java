package com.wosai.mc.controller;

import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控指标收集接口
 * hera会定时通过此接口抓取服务的Metrics
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@RestController
public class MetricsController {
    @GetMapping(path = "/metrics", produces = {"text/plain;version=0.0.4;charset=utf-8"})
    public String endpoint() {
        String data = MetricsManager.scrape();
        return data;
    }
}
