package com.wosai.mc.utils;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import org.apache.commons.lang3.time.DateUtils;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class EffectiveTimeUtil {

    public static final String FORMATTER_DATE_INT = "yyyyMMdd";

    public static void checkoutEffectiveTime(String endDate) {
        try {
            if (WosaiStringUtils.isEmpty(endDate) || endDate.length() < 8) {
                throw new CoreInvalidParameterException("证件有效期格式错误");
            }
            // 校验日期是否正确
            DateUtils.parseDate(endDate, FORMATTER_DATE_INT);
            if (getDiffDay(Integer.valueOf(endDate)) <= 7) {
                throw new CoreInvalidParameterException("证件有效期需大于7天");
            }
        } catch (CoreInvalidParameterException e) {
            throw e;
        } catch (Exception e) {
            throw new CoreInvalidParameterException("证件有效期格式错误");
        }
    }

    private static int getDiffDay(Integer day) {
        LocalDate today = LocalDate.now();
        LocalDate statisticsDay = LocalDate.of(day / 10000, day % 10000 / 100, day % 100);
        return (int) ChronoUnit.DAYS.between(today, statisticsDay);
    }

    public static int periodYear(String startDate, String endDate){
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA));
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA));
        return end.getYear() - start.getYear();
    }

    public static boolean isPeriodDay(String startDate, String endDate){
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA));
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA));
        if (start.getMonth().equals(end.getMonth())){
            return start.getDayOfMonth() - end.getDayOfMonth() == 1;
        }else if (start.getMonth().minus(1L).equals(end.getMonth())){
            return start.getDayOfMonth() == 1 &&
                    isValid(end.isLeapYear(), end.getMonth().getValue(), end.getDayOfMonth());
        }
        return false;
    }

    private static boolean isValid(boolean isLeapYear, int endMonth, int day){
        switch(endMonth){
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:{
                return day == 31;
            }
            case 2:{
                if (isLeapYear){
                    return day == 29;
                }
                return day == 28;
            }
            case 4:
            case 6:
            case 9:
            case 11:{
                return day == 30;
            } default : return false;
        }
    }

    public static boolean isDateValid(String startDate, String endDate){
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA));
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA));
        // 处理闰日扩展规则
        if (isLeapDay(start)) {
            return isLeapDayWithTolerance(end);
        }
        if (isLeapDay(end)) {
            return isLeapDayWithTolerance(start);
        }
        //其余得都得是开始月==结束月，开始日==结束日
        return start.getMonth().equals(end.getMonth())
                && start.getDayOfMonth() == end.getDayOfMonth();
    }

    // 闰日宽容规则判断
    private static boolean isLeapDayWithTolerance(LocalDate dateB) {
        // 构建允许的日期范围（前后两天+跨月情况）
        Month targetMonth = dateB.getMonth();
        int targetDay = dateB.getDayOfMonth();

        // 允许的日期组合
        return (targetMonth == Month.FEBRUARY && targetDay >= 27) ||  // 2月27-29日
                (targetMonth == Month.MARCH && targetDay <= 2);     // 3月1-2日
    }

    // 判断是否为闰日
    private static boolean isLeapDay(LocalDate date) {
        return date.isLeapYear()
                && date.getMonth() == Month.FEBRUARY
                && date.getDayOfMonth() == 29;
    }

}
