package com.wosai.mc.utils;

import com.wosai.data.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;
import org.slf4j.Logger;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2020-12-25
 */
@Slf4j
public class LogUtil {

    /**
     * 打印 info 日志
     *
     * @param logger
     * @param message
     * @param args
     */
    public static void info(Logger logger, String message, Object... args) {
        if (args.length > 0) {
            logger.info(markers(args), message, args);
        } else {
            logger.info(message, args);
        }
    }

    /**
     * 打印 error 日志
     * 如果要打印 Throwable 信息，Throwable 需要放在 args 最后
     *
     * @param logger
     * @param message
     * @param args
     */
    public static void error(Logger logger, String message, Object... args) {
        Object[] args2 = removeThrowable(args);
        if (args2.length > 0) {
            logger.error(markers(args2), message, args);
        } else {
            logger.error(message, args);
        }
    }

    private static Object[] removeThrowable(Object[] args) {
        if (args.length == 0 || !(args[args.length - 1] instanceof Throwable)) {
            return args;
        }
        return Arrays.copyOf(args, args.length - 1);
    }

    private static LogstashMarker markers(Object... args) {
        return Markers.appendEntries(CollectionUtil.hashMap(
                "request", args
        ));
    }
}
