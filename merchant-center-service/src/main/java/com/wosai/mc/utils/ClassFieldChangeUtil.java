package com.wosai.mc.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.common.utils.WosaiStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ClassFieldChangeUtil {

    private static ObjectMapper objectMapper;

    @Autowired
    public ClassFieldChangeUtil (ObjectMapper objectMapper) {
        ClassFieldChangeUtil.objectMapper = objectMapper;
    }

    private static final List<String> PHOTO_LIST = Arrays
            .asList("concat_id_card_front_photo", "business_license_photo", "photo", "letter_of_authorization", "trade_license", "legal_person_id_card_front_photo", "legal_person_id_card_back_photo",
                    "brand_photo","indoor_material", "outdoor_material", "audit_picture", "product_price");


    /**
     * 比较map
     *
     * @param source            元数据
     * @param target            目标数据
     * @param ignoreProperties 需比较的属性
     * @return
     */
    public static void getModifyContent(Object source ,Object target, List<String> ignoreProperties, Map modifies) {
        if (null == source || null == target) {
            return ;
        }

        Map<String, Object> sourceMap = objectMapper.convertValue(source, new TypeReference<Map<String, Object>>() {
        });
        Map<String, Object> targetMap = objectMapper.convertValue(target, new TypeReference<Map<String, Object>>() {
        });

        sourceMap.forEach((k, v) -> {
            Object targetValue = targetMap.get(k);
            if (v == null && targetValue == null) {
                return;
            }
            if (ignoreProperties != null && ignoreProperties.contains(k)) {
                return;
            }
            if (PHOTO_LIST.contains(k)) {
                if (judgePhotoChanged((String) v, (String) targetValue)) {
                    modifies.put(k, true);
                }
            } else {
                if (!Objects.equals(v, targetValue)) {
                    modifies.put(k, targetValue);
                }
            }
        });
    }

    private static boolean judgePhotoChanged(String oldPhoto, String newPhoto) {
        if (WosaiStringUtils.isNotEmpty(oldPhoto) && WosaiStringUtils.isNotEmpty(newPhoto)) {
            String[] oldPhotoUrl = oldPhoto.split("\\?");
            String[] newPhotoUrl = newPhoto.split("\\?");
            if (Objects.equals(oldPhotoUrl[0], newPhotoUrl[0])) {
                return false;
            }
            return true;
        }
        if (WosaiStringUtils.isEmpty(oldPhoto) && WosaiStringUtils.isEmpty(newPhoto)) {
            return false;
        }
        return true;
    }
}
