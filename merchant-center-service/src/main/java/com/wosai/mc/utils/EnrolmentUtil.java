package com.wosai.mc.utils;

import java.util.Random;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-17
 * @Description:
 */


public class EnrolmentUtil {
    private static final Random RANDOM = new Random();

    private static final char[] CHAR_PASS = {'a','b','c','d','e','f','g','h','i','j','k','m','n','p','q','r','s','t','u','v','w','x','y','z'};

    /**
     * 数字加字符串
     */
    public static String randomEdge(int length) {
        int charLength = CHAR_PASS.length;
        StringBuilder val = new StringBuilder();
        //参数length，表示生成几位随机数
        for (int i = 0; i < length - 2; i++) {
            String charOrNum = RANDOM.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出小写字母
                val.append(CHAR_PASS[RANDOM.nextInt(charLength)]);
            } else {
                //输出数字
                val.append(RANDOM.nextInt(9) + 1);
            }
        }
        // 防止出现全部数字或全部字母
        val.append(RANDOM.nextInt(9) + 1).append(CHAR_PASS[RANDOM.nextInt(charLength)]);
        return val.toString();
    }

    public static String random() {
        return (RANDOM.nextInt(899999) + 100000) + "";
    }
}
