package com.wosai.mc.utils;

import com.wosai.common.exception.CommonPubBizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-09-07
 */
@Component
@DependsOn("smsConfig")
public class SmsBiz {
    private Logger log = LoggerFactory.getLogger(SmsBiz.class);


    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private RestTemplate restTemplate;

    public static final String MINI_PLATFORM = "mini_app";

    public void send(String platform, String cellphone, String template, Map vars) {
        if (MINI_PLATFORM.equals(platform)) {
            log.info("小程序入网不发送短信");
            return;
        }
        Map body = new HashMap();
        body.put("to", cellphone);
        body.put("template", template);
        body.put("vars", vars);

        log.info("send sms body {}", body.toString());
        try {
            String result = restTemplate.postForObject(smsConfig.getUrl(), body, String.class);
            if (!"{}".equals(result)) {
                throw new CommonPubBizException("发送短信验证码失败");
            }
        } catch (Exception e) {
            throw new CommonPubBizException("短信服务异常", e);
        }
    }
}
