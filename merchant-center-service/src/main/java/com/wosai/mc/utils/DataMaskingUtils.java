package com.wosai.mc.utils;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.mc.annotation.MaskDataEnum;

/**
 * 数据脱敏处理
 *
 * <AUTHOR>
 * @date 2023/5/23
 */
public class DataMaskingUtils {


    public static String maskData(String data, MaskDataEnum type) {
        switch (type) {
            case NAME:
                return maskName(data);
            case CELLPHONE:
                return maskCellphone(data);
            case NUMBER:
                return maskNumber(data);
            case PHOTO:
                return maskPhoto(data);
            case EMAIL:
                return maskMail(data);
            default:
                throw new CommonInvalidParameterException("不支持的脱敏方式");

        }
    }

    private static String maskPhoto(String data) {
        return "****";
    }

    /**
     * 脱敏证件号
     *
     * @param certNo 证件号
     * @return 脱敏后的证件号
     */
    public static String maskCellphone(String certNo) {
        if (certNo == null || certNo.isEmpty()) {
            return "****";
        }
        return certNo.replaceAll("(?<=\\w{3})\\w(?=\\w{4})", "*");
    }

    public static String maskMail(String email) {
        if (email == null || email.isEmpty()) {
            return "****";
        }
        // 分离邮箱用户名和域名
        String[] parts = email.split("\\.");
        if (parts.length < 2) {
            return "****";
        }
        String username = parts[0];
        String domain = parts[1];


        return username.substring(0, 2) + "***." + domain;
    }

    /**
     * 脱敏名字
     *
     * @param name 名字
     * @return 脱敏后的名字
     */
    public static String maskName(String name) {
        if (name == null || name.isEmpty()) {
            return "***";
        }
        int len = name.length();
        if (len == 2) {
            return name.charAt(0) + "*";
        } else if (len >= 3) {
            String firstChar = name.substring(0, 1);
            String lastChar = name.substring(len - 1);
            StringBuilder middle = new StringBuilder();
            for (int i = 0; i < len - 2; i++) {
                middle.append("*");
            }
            return firstChar + middle + lastChar;
        } else {
            return name;
        }
    }

    public static String maskNumber(String number) {
        if (number == null || number.isEmpty()) {
            return "****";
        }
        return number.replaceAll("(?<=\\w)\\w(?=\\w)", "*");
    }


}
