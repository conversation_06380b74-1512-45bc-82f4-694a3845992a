package com.wosai.mc.utils;

import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class RegularUtil {

    /**
     * yyyyMMdd日期格式
     */
    private static final String REGULAR_DATE_FMT_1 = "[0-9]{8}";


    /**
     * 校验日期格式是不是yyyyMMdd-yyyyMMdd
     * @param string
     * @return
     */
    public static boolean isFormatDate(String string){
        if (StringUtils.isBlank(string)){
            return false;
        }
        String[] newString = string.split("-");
        if (newString.length != 2){
            return false;
        }
        Pattern pat = Pattern.compile(REGULAR_DATE_FMT_1);
        Matcher matchers = pat.matcher(newString[0]);
        boolean result = matchers.matches();
        if (result){
            matchers = pat.matcher(newString[1]);
            return matchers.matches();
        }
        return false;
    }

    public static boolean isFormat(String date){
        if (StringUtils.isBlank(date)){
            return false;
        }

        Pattern pat = Pattern.compile(REGULAR_DATE_FMT_1);
        Matcher matchers = pat.matcher(date);
        return matchers.matches();
    }

    public static boolean isNotValid(String rule, String data){
        Pattern pattern = Pattern.compile(rule);
        Matcher matchers = pattern.matcher(data);
        return !matchers.matches();
    }
}
