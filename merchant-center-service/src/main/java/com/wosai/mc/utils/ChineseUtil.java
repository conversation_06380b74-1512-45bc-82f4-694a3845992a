package com.wosai.mc.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.collections.MapUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class ChineseUtil {
    public static String getFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return "";
        }

        char firstChar = str.charAt(0);
        if (isChinese(firstChar)) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(firstChar);
            if (pinyinArray != null && pinyinArray.length > 0) {
                String pinyin = pinyinArray[0];
                return String.valueOf(pinyin.charAt(0)).toLowerCase();
            }
        }

        return String.valueOf(firstChar);
    }

    private static boolean isChinese(char c) {
        Character.UnicodeBlock block = Character.UnicodeBlock.of(c);
        return block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || block == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || block == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || block == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT;
    }

    public static void sortByFieldName(List<Map> dataList, String fieldName) {
        // 使用Comparator进行排序
        Collections.sort(dataList, Comparator.comparing(o -> {
            String name = MapUtils.getString(o, fieldName, "");
            StringBuilder pinyin = new StringBuilder();

            for (char c : name.toCharArray()) {
                // 判断是否为汉字
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    // 汉字转拼音
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        // 汉字的每个字符按照拼音顺序添加到结果中
                        for (char pinyinChar : pinyinArray[0].toCharArray()) {
                            pinyin.append(Character.toLowerCase(pinyinChar));
                        }
                    }
                } else {
                    // 非汉字直接取小写
                    pinyin.append(Character.toLowerCase(c));
                }
            }

            return pinyin.toString();
        }));
    }
}