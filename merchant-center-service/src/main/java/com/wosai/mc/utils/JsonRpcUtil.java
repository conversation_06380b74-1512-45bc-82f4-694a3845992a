package com.wosai.mc.utils;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.data.util.CollectionUtil;

public class JsonRpcUtil {

    public static JsonProxyFactoryBean getJsonProxyFactoryBean(String serverUrl, Class<?> clazz, int readTimeOut, int connectTimeOut) {
        JsonProxyFactoryBean bean = new JsonProxyFactoryBean();
        bean.setServiceUrl(serverUrl);
        bean.setServiceInterface(clazz);
        bean.setConnectionTimeoutMillis(connectTimeOut);
        bean.setReadTimeoutMillis(readTimeOut);
        // bean.setExtraHttpHeaders(CollectionUtil.hashMap("x-env-flag", "8911"));
        return bean;
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryBean(String serverUrl, Class<?> clazz, int readTimeOut) {
        return getJsonProxyFactoryBean(serverUrl, clazz, readTimeOut, 2000);
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryBean(String serverUrl, Class<?> clazz) {
        return getJsonProxyFactoryBean(serverUrl, clazz, 2000, 2000);
    }
}
