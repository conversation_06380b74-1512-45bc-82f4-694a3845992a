package com.wosai.mc.utils;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by lihebin on 2018/9/10.
 */
@Configuration
public class ApolloParamsConfig {
    private final static Logger log = LoggerFactory.getLogger(ApolloParamsConfig.class);

    private static final int QUERY_LIMIT = 100;
    private static final long QUERY_TIME = 2 * 60 * 60 * 1000L;

    private Config config;
    private ObjectMapper objectMapper = new ObjectMapper();


    @PostConstruct
    public void config() {
        config = ConfigService.getAppConfig();
    }


    public Map getMap(String key, String defaultParams) {
        try {
            return objectMapper.readValue(config.getProperty(key, defaultParams), Map.class);
        } catch (IOException e) {
            log.error("ApolloConfig getMap:{},{},{}", key, defaultParams, e);
            return new HashMap();
        }
    }


    public List getList(String key, String defaultParams) {
        try {
            return objectMapper.readValue(config.getProperty(key, defaultParams), List.class);
        } catch (IOException e) {
            log.error("ApolloConfig getList:{},{},{}", key, defaultParams, e);
            return new ArrayList();
        }
    }

    public String getString(String key, String defaultParams) {
        return config.getProperty(key, defaultParams);
    }

    public int getInt(String key, int defaultParams) {
        return config.getIntProperty(key, defaultParams);
    }

    public boolean getBoolean(String key, boolean defaultParams) {
        return config.getBooleanProperty(key, defaultParams);
    }


}
