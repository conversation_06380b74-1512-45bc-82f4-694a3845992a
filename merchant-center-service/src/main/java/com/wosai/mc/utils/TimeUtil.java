package com.wosai.mc.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil {

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat sdfJustUtilDay = new SimpleDateFormat("yyyy-MM-dd");


    /**
     * 计算参数日期还有几天会"过期"
     * 一般来说,传入的日期比当前日期要 "迟", 天数不足一天的, +1天
     */
    public static int getRemainDay(Date date) {
        if (date == null) {
            return 99;
        }
        long willCheck = date.getTime();
        long current = System.currentTimeMillis();
        if (current >= willCheck) {
            return 0;
        }
        return (int) ((willCheck - current) / (1000 * 60 * 60 * 24)) + 1;
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return sdf.format(date);
    }

    public static String formatDateJustUtilDay(Date date) {
        if (date == null) {
            return "";
        }
        return sdfJustUtilDay.format(date);
    }

    public static Date parseDate(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return sdf.parse(date);
    }

    /**
     * 检查当前时间距离给定日期是否已经超过指定的天数
     *
     * @param days 检查的天数
     * @param date 给定的日期
     * @return 如果当前时间距给定日期已经超过指定的天数，返回true；否则返回false
     */
    public static boolean isDateBeyondDays(int days, Date date) {
        if (date == null) {
            throw new IllegalArgumentException("日期参数不能为空");
        }
        // 获取当前时间的日期对象
        Date currentDate = new Date();
        // 使用日历计算给定日期加上指定天数后的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        Date targetDate = calendar.getTime();
        // 比较当前日期和目标日期
        return currentDate.after(targetDate);
    }


}
