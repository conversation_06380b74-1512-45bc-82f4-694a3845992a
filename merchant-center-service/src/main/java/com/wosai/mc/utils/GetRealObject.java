package com.wosai.mc.utils;

import avro.shaded.com.google.common.collect.Maps;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-24
 * @Description:
 */


public class GetRealObject {

    public static final List STORE = Lists.newArrayList("contact_email", "client_sn", "merchant_id", "operation_contents",
            "solicitor_id", "vendor_id", "contact_cellphone", "contact_phone", "contact_name", "street_address", "street_address_desc",
            "district", "city", "province", "latitude", "longitude", "name", "id", "open_account_way", "district_code", "industry", "extra", "poi_name", "poi_simple_address");

    public static final List STORE_EXT = Lists.newArrayList("around_type",
            "average_consumption_time", "table_count", "room_count", "store_area", "business_hour", "video",
            "store_id");

    /*
     * 以下和表定义除去version保持一致
     */
    public static final List MERCHANT_DATA = Lists.newArrayList("id", "sn",
            "name", "alias", "industry", "status", "rank", "type", "withdraw_mode", "longitude", "latitude",
            "country", "province", "city", "district", "street_address", "street_address_desc", "contact_name", "contact_phone", "contact_cellphone",
            "contact_email", "concat_id_card_front_photo", "legal_person_type", "legal_person_name", "legal_person_id_type", "legal_person_id_number",
            "legal_person_id_card_front_photo", "legal_person_id_card_back_photo", "legal_person_register_no", "business_license_photo", "business",
            "currency", "owner_name", "owner_cellphone", "customer_phone", "logo", "bank_account_verify_status", "client_sn", "vendor_id", "solicitor_id",
            "platform", "extra", "mtime", "deleted", "business_name", "concat_identity", "merchant_type", "merchant_scale", "verify_status",
            "district_code", "binded_store_id");
    public static final List MERCHANT_BUSINESS_LICENSE_DATA = Lists.newArrayList("id", "merchant_id",
            "type", "photo", "number", "name", "business_scope", "validity", "address", "registered_legal_person_name", "letter_of_authorization",
            "trade_license", "legal_person_id_type", "legal_person_id_card_front_photo", "legal_person_id_card_back_photo", "legal_person_name",
            "legal_person_id_number", "id_validity", "ctime", "mtime", "deleted", "legal_person_id_card_address", "legal_person_id_card_issuing_authority",
            "verify_status", "extra", "announcement_website_photo");
    public static final List STORE_EXT_DATA = Lists.newArrayList("id", "store_id", "brand_photo_id", "brand_only_scene_photo_id", "indoor_material_photo_id", "indoor_only_scene_photo_id",
            "outdoor_material_photo_id", "outdoor_only_scene_photo_id", "other_photo_id", "order_price_photo_id", "product_price_id", "audit_picture_id", "video", "business_hour", "store_area", "room_count",
            "table_count", "average_consumption_time", "around_type", "extra", "ctime", "mtime");
    public static final List STORE_DATA = Lists.newArrayList("id", "sn", "name", "industry", "status", "rank", "longitude", "latitude",
            "province", "city", "district", "street_address", "street_address_desc", "contact_name", "contact_phone", "contact_cellphone", "contact_email",
            "client_sn", "merchant_id", "solicitor_id", "vendor_id", "extra", "ctime", "mtime", "deleted", "operation_contents", "verify_status", "type",
            "open_account_way", "district_code", "poi_name", "poi_simple_address");
    public static final List<String> STORE_BUSINESS_LICENSE_DATA = Lists.newArrayList("id", "merchant_id", "store_id",
            "type", "photo", "number", "name", "business_scope", "validity", "address", "registered_legal_person_name", "letter_of_authorization",
            "trade_license", "legal_person_id_type", "legal_person_id_card_front_photo", "legal_person_id_card_back_photo", "legal_person_name",
            "legal_person_id_number", "id_validity", "ctime", "mtime", "deleted", "legal_person_id_card_address", "legal_person_id_card_issuing_authority",
            "verify_status", "extra", "use_merchant_business_license");
    public static final List<String> LICENSE_DATA = Lists.newArrayList("id", "business_license_id", "license_name", "license_number", "license_validity",
            "license_photo", "license_type", "verify_status", "extra", "ctime", "mtime", "deleted");

    public static final List<String> LOG_REQ = Lists.newArrayList("log_template_code", "op_user_id", "op_user_name", "remark");

    public static Map filterParams(Map requestMap, List<String> filterList) {
        //为了返回的map可以被添加新的key
        return new HashMap<>(Maps.filterKeys(requestMap, filterList::contains));
    }

    public static Map removeKeys(Map requestMap, List<Object> filterList) {
        for (Object s : filterList) {
            requestMap.remove(s);
        }
        return requestMap;
    }
}
