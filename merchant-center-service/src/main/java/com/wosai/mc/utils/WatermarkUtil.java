package com.wosai.mc.utils;

import com.wosai.oss.OssUrlEncrypt;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;

/**
 * 水印工具
 *
 * <AUTHOR>
 * @date 2025/5/9 17:25
 */
@Component
public class WatermarkUtil {

    private static OssUrlEncrypt OSS_UTIL;

    @Autowired
    public WatermarkUtil(OssUrlEncrypt ossUrlEncrypt) {
        WatermarkUtil.OSS_UTIL = ossUrlEncrypt;
    }

    public static String encryptUrlWithWatermark(String sourceUrl, String watermarkText) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(watermarkText)) {
            return OSS_UTIL.encryptUrl(sourceUrl);
        } else {
            String base64Text = Base64.encodeBase64URLSafeString(watermarkText.getBytes());
            String newUrl = buildWatermark(sourceUrl, base64Text);
            String encryptUrl = OSS_UTIL.encryptUrl(newUrl);
            if (StringUtils.isBlank(encryptUrl)) {
                return encryptUrl;
            }
            int queryStartIndex = encryptUrl.indexOf('?');
            if (queryStartIndex == -1) {
                return encryptUrl;
            }
            String path = encryptUrl.substring(0, queryStartIndex);
            String query = encryptUrl.substring(queryStartIndex + 1);
            // 仅编码查询参数中的逗号（, -> %2C）
            String encodedQuery = query.replace(",", "%2C");
            return path + "?" + encodedQuery;
        }
    }

    private static String buildWatermark(String originalValue, String base64Text) {
        if (StringUtils.isEmpty(originalValue)) {
            return originalValue;
        } else {
            String watermarkContent = String.format(",text_%s,color_505050,size_30,g_center,rotate_330,t_30,fill_1", base64Text);
            String watermarkStr = "x-oss-process=image/watermark" + watermarkContent;
            String newValue;
            if (originalValue.contains("x-oss-process=image")) {
                newValue = originalValue.replace("x-oss-process=image", watermarkStr);
            } else if (originalValue.contains("?")) {
                newValue = originalValue + "&" + watermarkStr;
            } else {
                newValue = originalValue + "?" + watermarkStr;
            }
            return newValue;
        }
    }
}
