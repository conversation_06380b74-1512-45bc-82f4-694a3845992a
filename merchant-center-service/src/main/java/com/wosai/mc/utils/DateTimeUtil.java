package com.wosai.mc.utils;


import java.text.SimpleDateFormat;
import java.util.Calendar;

public class DateTimeUtil {

    /**
     * 格式：yyyy-MM-dd
     */
    public static final String FORMAT_DAY = "yyyy-MM-dd";

    // 返回当前timestamp所代表的天
    public static long getDate(long timestamp) {
            Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timestamp);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        long result = c.getTimeInMillis();
        return result;
    }

    public static SimpleDateFormat getSdf(String format){
        return new SimpleDateFormat(format);
    }

    public static long getTime(String dateStr,String format){
        try {
            return getSdf(format).parse(dateStr).getTime();
        }catch (Exception e){
            return 0;
        }
    }

}
