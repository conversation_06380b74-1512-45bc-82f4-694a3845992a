package com.wosai.mc.utils;

import com.shouqianba.cua.chatbot.ChatBot;
import com.shouqianba.cua.chatbot.client.ChatBotClient;
import com.shouqianba.cua.chatbot.client.FeishuChatBotClient;
import com.shouqianba.cua.chatbot.message.TextMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


@Component
@Slf4j
public class DingUtil {

    private static final ChatBotClient client = new FeishuChatBotClient();
    @Value("${merge_store.bot_url}")
    private String webhook;
    private ChatBot chatBot;

    @PostConstruct
    public void init() {
        chatBot = new ChatBot(webhook, null, client);
    }

    public void sendMessage(String message) {
        try {
            chatBot.sendMessageImmediately(new TextMessage(message));
        } catch (Exception e) {
            log.error("{} 发送业务警告失败", message, e);
        }
    }

}
