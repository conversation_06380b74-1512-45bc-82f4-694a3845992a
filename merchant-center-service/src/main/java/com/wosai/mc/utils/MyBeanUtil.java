package com.wosai.mc.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.CaseFormat;
import com.wosai.upay.common.helper.MyObjectMapper;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-08-21
 */
public class MyBeanUtil {

    private static ObjectMapper mapper = new MyObjectMapper();

    public static Map toMap(Object bean) {
        try {
            return mapper.readValue(mapper.writeValueAsString(bean), Map.class);
        } catch (Exception e) {
            return new HashMap();
        }
    }


    public static <T> T toBean(Map map, Class<T> tClass) {
        try {
            return mapper.readValue(mapper.writeValueAsString(map), tClass);
        } catch (Exception e) {
            return null;
        }
    }


    public static Map<String, String> getUnderscoreFromLowerCameL(Map<String, String> map) {
        Map<String, String> req = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, entry.getKey());
            req.put(key, entry.getValue());
        }
        return req;
    }

    public static Map<String, String> getLowerCameLFromUnderscore(Map<String, String> map) {
        Map<String, String> req = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, entry.getKey());
            req.put(key, entry.getValue());
        }
        return req;
    }
}
