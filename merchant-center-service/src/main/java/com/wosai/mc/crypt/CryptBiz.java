package com.wosai.mc.crypt;

import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.core.crypto.constant.RequestConstant;
import com.wosai.core.crypto.constant.ResponseConstant;
import com.wosai.core.crypto.service.CryptoService;
import com.wosai.core.crypto.util.CryptoUtil;
import com.wosai.core.crypto.util.SignUtil;
import com.wosai.pantheon.util.StringUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020-05-07 10:41
 * @Description:
 */
@Component
public class CryptBiz implements InitializingBean {

    @Value("${core-crypto.access_id}")
    private String access_id;

    @Value("${core-crypto.access_secret}")
    private String access_secret;

    @Autowired
    private CryptoService cryptoService;

    private static CryptoClient cryptoClient;

    private void init() {
        cryptoClient = new CryptoClient(cryptoService, access_id, access_secret);

    }


//    public void encrypt(Object bean) {
//        Map<String, Object> cryptMap = getCryptMap(bean, true);
//        if (!cryptMap.isEmpty()) {
//            setCryptMap(bean, cryptoClient.encrypt(cryptMap, columns));
//        }
//    }

    public String decryptString(String ciphertext){
        return cryptoClient.decrypt(ciphertext);
    }

    public List<Object> decryptList(List<Object> beans, List<String> columns) {
        if (beans == null || beans.size() == 0) {
            return null;
        }
        List<Map<String, Object>> decryptList = cryptoClient.decrypts(getCryptList(beans, columns), columns);
        for (int i = 0; i < beans.size(); i++) {
            setCryptMap((Map) beans.get(i), decryptList.get(i), columns);
        }
        return beans;

    }

    public String encrypt(String plaintext) {
        if (StringUtil.isBlank(plaintext)) {
            return "";
        }
        return cryptoClient.encrypt(plaintext);
    }

    public List<String> encrypts(List<String> plaintexts) {
        return cryptoClient.encrypts(plaintexts);
    }

    public Collection<String> allVerEncrypt(String plaintext) {
        if (StringUtil.isBlank(plaintext)) {
            return Collections.EMPTY_LIST;
        }
        return cryptoClient.allVerEncrypt(plaintext);
    }

    private Map<String, Object> getCryptMap(Map map, List<String> columns) {
        Map<String, Object> cryptMap = new HashMap<>(columns.size());
        for (String column : columns) {
            Object value = MapUtils.getString(map, column);
            if (value != null) {
                cryptMap.put(column, value);
            }
        }

        return cryptMap;
    }

    private void setCryptMap(Map bean, Map cryptMap, List<String> columns) {
        for (String column : columns) {
            Object value = cryptMap.get(column);
            if (value != null) {
                bean.put(column, value);
            }
        }
    }

    private List<Map<String, Object>> getCryptList(List<Object> beans, List<String> columns) {
        return beans.stream()
                .map(bean -> getCryptMap((Map) bean, columns))
                .collect(Collectors.toList());
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }
}
