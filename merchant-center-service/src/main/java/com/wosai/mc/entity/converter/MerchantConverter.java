package com.wosai.mc.entity.converter;

import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.resp.CreateMerchantResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MerchantConverter {

    MerchantConverter INSTANCE = Mappers.getMapper(MerchantConverter.class);

    @Mapping(source = "sn", target = "merchantSn")
    @Mapping(source = "id", target = "merchantId")
    CreateMerchantResp do2dto(MerchantInfo merchantInfo);

}