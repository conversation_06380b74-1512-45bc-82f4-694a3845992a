package com.wosai.mc.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class StoreListWaitHandleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public StoreListWaitHandleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchant_idIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchant_idEqualTo(String value) {
            addCriterion("merchant_id =", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotEqualTo(String value) {
            addCriterion("merchant_id <>", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idGreaterThan(String value) {
            addCriterion("merchant_id >", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_id >=", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idLessThan(String value) {
            addCriterion("merchant_id <", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idLessThanOrEqualTo(String value) {
            addCriterion("merchant_id <=", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idLike(String value) {
            addCriterion("merchant_id like", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotLike(String value) {
            addCriterion("merchant_id not like", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idIn(List<String> values) {
            addCriterion("merchant_id in", values, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotIn(List<String> values) {
            addCriterion("merchant_id not in", values, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idBetween(String value1, String value2) {
            addCriterion("merchant_id between", value1, value2, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotBetween(String value1, String value2) {
            addCriterion("merchant_id not between", value1, value2, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andStore_idIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStore_idIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStore_idEqualTo(String value) {
            addCriterion("store_id =", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idNotEqualTo(String value) {
            addCriterion("store_id <>", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idGreaterThan(String value) {
            addCriterion("store_id >", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idGreaterThanOrEqualTo(String value) {
            addCriterion("store_id >=", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idLessThan(String value) {
            addCriterion("store_id <", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idLessThanOrEqualTo(String value) {
            addCriterion("store_id <=", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idLike(String value) {
            addCriterion("store_id like", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idNotLike(String value) {
            addCriterion("store_id not like", value, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idIn(List<String> values) {
            addCriterion("store_id in", values, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idNotIn(List<String> values) {
            addCriterion("store_id not in", values, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idBetween(String value1, String value2) {
            addCriterion("store_id between", value1, value2, "store_id");
            return (Criteria) this;
        }

        public Criteria andStore_idNotBetween(String value1, String value2) {
            addCriterion("store_id not between", value1, value2, "store_id");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeIsNull() {
            addCriterion("is_takeout_active is null");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeIsNotNull() {
            addCriterion("is_takeout_active is not null");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeEqualTo(Integer value) {
            addCriterion("is_takeout_active =", value, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeNotEqualTo(Integer value) {
            addCriterion("is_takeout_active <>", value, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeGreaterThan(Integer value) {
            addCriterion("is_takeout_active >", value, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_takeout_active >=", value, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeLessThan(Integer value) {
            addCriterion("is_takeout_active <", value, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeLessThanOrEqualTo(Integer value) {
            addCriterion("is_takeout_active <=", value, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeIn(List<Integer> values) {
            addCriterion("is_takeout_active in", values, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeNotIn(List<Integer> values) {
            addCriterion("is_takeout_active not in", values, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeBetween(Integer value1, Integer value2) {
            addCriterion("is_takeout_active between", value1, value2, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andIs_takeout_activeNotBetween(Integer value1, Integer value2) {
            addCriterion("is_takeout_active not between", value1, value2, "is_takeout_active");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameIsNull() {
            addCriterion("choosed_name is null");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameIsNotNull() {
            addCriterion("choosed_name is not null");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameEqualTo(Integer value) {
            addCriterion("choosed_name =", value, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameNotEqualTo(Integer value) {
            addCriterion("choosed_name <>", value, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameGreaterThan(Integer value) {
            addCriterion("choosed_name >", value, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameGreaterThanOrEqualTo(Integer value) {
            addCriterion("choosed_name >=", value, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameLessThan(Integer value) {
            addCriterion("choosed_name <", value, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameLessThanOrEqualTo(Integer value) {
            addCriterion("choosed_name <=", value, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameIn(List<Integer> values) {
            addCriterion("choosed_name in", values, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameNotIn(List<Integer> values) {
            addCriterion("choosed_name not in", values, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameBetween(Integer value1, Integer value2) {
            addCriterion("choosed_name between", value1, value2, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_nameNotBetween(Integer value1, Integer value2) {
            addCriterion("choosed_name not between", value1, value2, "choosed_name");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressIsNull() {
            addCriterion("choosed_address is null");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressIsNotNull() {
            addCriterion("choosed_address is not null");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressEqualTo(Integer value) {
            addCriterion("choosed_address =", value, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressNotEqualTo(Integer value) {
            addCriterion("choosed_address <>", value, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressGreaterThan(Integer value) {
            addCriterion("choosed_address >", value, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressGreaterThanOrEqualTo(Integer value) {
            addCriterion("choosed_address >=", value, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressLessThan(Integer value) {
            addCriterion("choosed_address <", value, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressLessThanOrEqualTo(Integer value) {
            addCriterion("choosed_address <=", value, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressIn(List<Integer> values) {
            addCriterion("choosed_address in", values, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressNotIn(List<Integer> values) {
            addCriterion("choosed_address not in", values, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressBetween(Integer value1, Integer value2) {
            addCriterion("choosed_address between", value1, value2, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andChoosed_addressNotBetween(Integer value1, Integer value2) {
            addCriterion("choosed_address not between", value1, value2, "choosed_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressIsNull() {
            addCriterion("is_merged_name_address is null");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressIsNotNull() {
            addCriterion("is_merged_name_address is not null");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressEqualTo(Integer value) {
            addCriterion("is_merged_name_address =", value, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressNotEqualTo(Integer value) {
            addCriterion("is_merged_name_address <>", value, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressGreaterThan(Integer value) {
            addCriterion("is_merged_name_address >", value, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_merged_name_address >=", value, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressLessThan(Integer value) {
            addCriterion("is_merged_name_address <", value, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressLessThanOrEqualTo(Integer value) {
            addCriterion("is_merged_name_address <=", value, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressIn(List<Integer> values) {
            addCriterion("is_merged_name_address in", values, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressNotIn(List<Integer> values) {
            addCriterion("is_merged_name_address not in", values, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressBetween(Integer value1, Integer value2) {
            addCriterion("is_merged_name_address between", value1, value2, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andIs_merged_name_addressNotBetween(Integer value1, Integer value2) {
            addCriterion("is_merged_name_address not between", value1, value2, "is_merged_name_address");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeIsNull() {
            addCriterion("name_address_notice_time is null");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeIsNotNull() {
            addCriterion("name_address_notice_time is not null");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeEqualTo(Date value) {
            addCriterionForJDBCDate("name_address_notice_time =", value, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeNotEqualTo(Date value) {
            addCriterionForJDBCDate("name_address_notice_time <>", value, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeGreaterThan(Date value) {
            addCriterionForJDBCDate("name_address_notice_time >", value, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("name_address_notice_time >=", value, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeLessThan(Date value) {
            addCriterionForJDBCDate("name_address_notice_time <", value, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("name_address_notice_time <=", value, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeIn(List<Date> values) {
            addCriterionForJDBCDate("name_address_notice_time in", values, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeNotIn(List<Date> values) {
            addCriterionForJDBCDate("name_address_notice_time not in", values, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("name_address_notice_time between", value1, value2, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andName_address_notice_timeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("name_address_notice_time not between", value1, value2, "name_address_notice_time");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedIsNull() {
            addCriterion("is_contract_name_phone_merged is null");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedIsNotNull() {
            addCriterion("is_contract_name_phone_merged is not null");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedEqualTo(Integer value) {
            addCriterion("is_contract_name_phone_merged =", value, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedNotEqualTo(Integer value) {
            addCriterion("is_contract_name_phone_merged <>", value, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedGreaterThan(Integer value) {
            addCriterion("is_contract_name_phone_merged >", value, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_contract_name_phone_merged >=", value, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedLessThan(Integer value) {
            addCriterion("is_contract_name_phone_merged <", value, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedLessThanOrEqualTo(Integer value) {
            addCriterion("is_contract_name_phone_merged <=", value, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedIn(List<Integer> values) {
            addCriterion("is_contract_name_phone_merged in", values, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedNotIn(List<Integer> values) {
            addCriterion("is_contract_name_phone_merged not in", values, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedBetween(Integer value1, Integer value2) {
            addCriterion("is_contract_name_phone_merged between", value1, value2, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andIs_contract_name_phone_mergedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_contract_name_phone_merged not between", value1, value2, "is_contract_name_phone_merged");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeIsNull() {
            addCriterion("contract_name_phone_notice_time is null");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeIsNotNull() {
            addCriterion("contract_name_phone_notice_time is not null");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeEqualTo(Date value) {
            addCriterionForJDBCDate("contract_name_phone_notice_time =", value, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeNotEqualTo(Date value) {
            addCriterionForJDBCDate("contract_name_phone_notice_time <>", value, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeGreaterThan(Date value) {
            addCriterionForJDBCDate("contract_name_phone_notice_time >", value, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("contract_name_phone_notice_time >=", value, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeLessThan(Date value) {
            addCriterionForJDBCDate("contract_name_phone_notice_time <", value, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("contract_name_phone_notice_time <=", value, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeIn(List<Date> values) {
            addCriterionForJDBCDate("contract_name_phone_notice_time in", values, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeNotIn(List<Date> values) {
            addCriterionForJDBCDate("contract_name_phone_notice_time not in", values, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("contract_name_phone_notice_time between", value1, value2, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andContract_name_phone_notice_timeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("contract_name_phone_notice_time not between", value1, value2, "contract_name_phone_notice_time");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Long value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Long value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Long value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Long value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Long value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Long> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Long> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Long value1, Long value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Long value1, Long value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Long value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Long value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Long value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Long value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Long value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Long> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Long> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Long value1, Long value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Long value1, Long value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterIsNull() {
            addCriterion("urge_notice_10days_later is null");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterIsNotNull() {
            addCriterion("urge_notice_10days_later is not null");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_10days_later =", value, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterNotEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_10days_later <>", value, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterGreaterThan(Date value) {
            addCriterionForJDBCDate("urge_notice_10days_later >", value, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_10days_later >=", value, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterLessThan(Date value) {
            addCriterionForJDBCDate("urge_notice_10days_later <", value, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_10days_later <=", value, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterIn(List<Date> values) {
            addCriterionForJDBCDate("urge_notice_10days_later in", values, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterNotIn(List<Date> values) {
            addCriterionForJDBCDate("urge_notice_10days_later not in", values, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("urge_notice_10days_later between", value1, value2, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_10days_laterNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("urge_notice_10days_later not between", value1, value2, "urge_notice_10days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterIsNull() {
            addCriterion("urge_notice_20days_later is null");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterIsNotNull() {
            addCriterion("urge_notice_20days_later is not null");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_20days_later =", value, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterNotEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_20days_later <>", value, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterGreaterThan(Date value) {
            addCriterionForJDBCDate("urge_notice_20days_later >", value, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_20days_later >=", value, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterLessThan(Date value) {
            addCriterionForJDBCDate("urge_notice_20days_later <", value, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("urge_notice_20days_later <=", value, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterIn(List<Date> values) {
            addCriterionForJDBCDate("urge_notice_20days_later in", values, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterNotIn(List<Date> values) {
            addCriterionForJDBCDate("urge_notice_20days_later not in", values, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("urge_notice_20days_later between", value1, value2, "urge_notice_20days_later");
            return (Criteria) this;
        }

        public Criteria andUrge_notice_20days_laterNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("urge_notice_20days_later not between", value1, value2, "urge_notice_20days_later");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}