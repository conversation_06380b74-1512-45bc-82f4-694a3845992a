package com.wosai.mc.entity.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 营业执照认证状态
 */
public enum BusinessLicenseVerifyStatusEnum implements ITextValueEnum<Integer> {

    UNKNOWN(0, "未知"),

    NOT_VERIFIED(1, "未认证"),

    VERIFY_SUCCESS(2, "认证成功"),

    VERIFY_FAILED(3, "认证失败"),

    VERIFYING(4, "认证中");


    private final int value;
    private final String text;

    BusinessLicenseVerifyStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
