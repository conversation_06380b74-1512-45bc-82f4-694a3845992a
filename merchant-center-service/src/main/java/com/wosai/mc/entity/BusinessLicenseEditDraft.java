package com.wosai.mc.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.core.utils.StringUtils;
import com.wosai.mc.model.dto.rsp.BusinessLicenseAuditEditDraftDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;

import java.util.Date;
import java.util.Objects;

@Setter
@Getter
public class BusinessLicenseEditDraft {

    private Long id;

    private String merchantSn;

    private String operator;

    private Date mtime;

    private Date ctime;

    /**
     * 草稿
     */
    private String draft;

    public BusinessLicenseAuditEditDraftDTO convertDraftJsonToDTO() {
        String draft = this.getDraft();
        if (StringUtils.isBlank(draft)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(draft);
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        boolean bankAccountIsNull = !jsonObject.containsKey("bankAccount") || MapUtils.isEmpty(MapUtils.getMap(jsonObject, "bankAccount"));
        boolean licenseIsNull = !jsonObject.containsKey("businessLicense") || MapUtils.isEmpty(MapUtils.getMap(jsonObject, "businessLicense"));
        BusinessLicenseAuditEditDraftDTO businessLicenseAuditEditDraftDTO = JSON.parseObject(this.getDraft(), BusinessLicenseAuditEditDraftDTO.class);
        if (bankAccountIsNull) {
            businessLicenseAuditEditDraftDTO.setBankAccount(null);
        }
        if (licenseIsNull) {
            businessLicenseAuditEditDraftDTO.setBusinessLicense(null);
        }
        return businessLicenseAuditEditDraftDTO;
    }

}