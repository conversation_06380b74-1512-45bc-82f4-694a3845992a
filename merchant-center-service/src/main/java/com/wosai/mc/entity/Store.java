package com.wosai.mc.entity;

import javax.persistence.*;
import java.util.Map;

@Table(name = "store")
public class Store {
    /**
     * UUID
     */
    @Id
    private String id;

    /**
     * sn 对应1.0中的wosai_store_id
     */
    private String sn;

    /**
     * 名称
     */
    private String name;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 状态 0关闭  1正常  2禁用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer rank;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 街道地址
     */
    @Column(name = "street_address")
    private String streetAddress;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系固话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 联系手机号
     */
    @Column(name = "contact_cellphone")
    private String contactCellphone;

    /**
     * 联系邮箱
     */
    @Column(name = "contact_email")
    private String contactEmail;

    /**
     * 商户外部门店号
     */
    @Column(name = "client_sn")
    private String clientSn;

    /**
     * 商户id
     */
    @Column(name = "merchant_id")
    private String merchantId;

    @Column(name = "solicitor_id")
    private String solicitorId;

    @Column(name = "vendor_id")
    private String vendorId;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    /**
     * 经营内容
     */
    @Column(name = "operation_contents")
    private String operationContents;

    /**
     * 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    private Integer verify_status;

    /**
     * 类型 1固定门店  2移动门店
     */
    private Integer type;

    /**
     * 扩展字段
     */
    private Map extra;

    /**
     * 获取UUID
     *
     * @return id - UUID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置UUID
     *
     * @param id UUID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取sn 对应1.0中的wosai_store_id
     *
     * @return sn - sn 对应1.0中的wosai_store_id
     */
    public String getSn() {
        return sn;
    }

    /**
     * 设置sn 对应1.0中的wosai_store_id
     *
     * @param sn sn 对应1.0中的wosai_store_id
     */
    public void setSn(String sn) {
        this.sn = sn;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取行业id
     *
     * @return industry - 行业id
     */
    public String getIndustry() {
        return industry;
    }

    /**
     * 设置行业id
     *
     * @param industry 行业id
     */
    public void setIndustry(String industry) {
        this.industry = industry;
    }

    /**
     * 获取状态 0关闭  1正常  2禁用
     *
     * @return status - 状态 0关闭  1正常  2禁用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 0关闭  1正常  2禁用
     *
     * @param status 状态 0关闭  1正常  2禁用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取排序
     *
     * @return rank - 排序
     */
    public Integer getRank() {
        return rank;
    }

    /**
     * 设置排序
     *
     * @param rank 排序
     */
    public void setRank(Integer rank) {
        this.rank = rank;
    }

    /**
     * 获取经度
     *
     * @return longitude - 经度
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * 设置经度
     *
     * @param longitude 经度
     */
    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取纬度
     *
     * @return latitude - 纬度
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度
     *
     * @param latitude 纬度
     */
    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    /**
     * 获取省
     *
     * @return province - 省
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置省
     *
     * @param province 省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取市
     *
     * @return city - 市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置市
     *
     * @param city 市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取区
     *
     * @return district - 区
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置区
     *
     * @param district 区
     */
    public void setDistrict(String district) {
        this.district = district;
    }

    /**
     * 获取街道地址
     *
     * @return street_address - 街道地址
     */
    public String getStreetAddress() {
        return streetAddress;
    }

    /**
     * 设置街道地址
     *
     * @param streetAddress 街道地址
     */
    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    /**
     * 获取联系人姓名
     *
     * @return contact_name - 联系人姓名
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * 设置联系人姓名
     *
     * @param contactName 联系人姓名
     */
    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    /**
     * 获取联系固话
     *
     * @return contact_phone - 联系固话
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置联系固话
     *
     * @param contactPhone 联系固话
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    /**
     * 获取联系手机号
     *
     * @return contact_cellphone - 联系手机号
     */
    public String getContactCellphone() {
        return contactCellphone;
    }

    /**
     * 设置联系手机号
     *
     * @param contactCellphone 联系手机号
     */
    public void setContactCellphone(String contactCellphone) {
        this.contactCellphone = contactCellphone;
    }

    /**
     * 获取联系邮箱
     *
     * @return contact_email - 联系邮箱
     */
    public String getContactEmail() {
        return contactEmail;
    }

    /**
     * 设置联系邮箱
     *
     * @param contactEmail 联系邮箱
     */
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    /**
     * 获取商户外部门店号
     *
     * @return client_sn - 商户外部门店号
     */
    public String getClientSn() {
        return clientSn;
    }

    /**
     * 设置商户外部门店号
     *
     * @param clientSn 商户外部门店号
     */
    public void setClientSn(String clientSn) {
        this.clientSn = clientSn;
    }

    /**
     * 获取商户id
     *
     * @return merchant_id - 商户id
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * 设置商户id
     *
     * @param merchantId 商户id
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * @return solicitor_id
     */
    public String getSolicitorId() {
        return solicitorId;
    }

    /**
     * @param solicitorId
     */
    public void setSolicitorId(String solicitorId) {
        this.solicitorId = solicitorId;
    }

    /**
     * @return vendor_id
     */
    public String getVendorId() {
        return vendorId;
    }

    /**
     * @param vendorId
     */
    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    /**
     * @return ctime
     */
    public Long getCtime() {
        return ctime;
    }

    /**
     * @param ctime
     */
    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    /**
     * @return mtime
     */
    public Long getMtime() {
        return mtime;
    }

    /**
     * @param mtime
     */
    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    /**
     * @return deleted
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * @param deleted
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * @return version
     */
    public Long getVersion() {
        return version;
    }

    /**
     * @param version
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取经营内容
     *
     * @return operation_contents - 经营内容
     */
    public String getOperationContents() {
        return operationContents;
    }

    /**
     * 设置经营内容
     *
     * @param operationContents 经营内容
     */
    public void setOperationContents(String operationContents) {
        this.operationContents = operationContents;
    }

    /**
     * 获取认证状态 0未知  1未认证  2认证成功  3认证失败
     *
     * @return verify_status - 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    public Integer getVerify_status() {
        return verify_status;
    }

    /**
     * 设置认证状态 0未知  1未认证  2认证成功  3认证失败
     *
     * @param verify_status 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    public void setVerify_status(Integer verify_status) {
        this.verify_status = verify_status;
    }

    /**
     * 获取类型 1固定门店  2移动门店
     *
     * @return type - 类型 1固定门店  2移动门店
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 1固定门店  2移动门店
     *
     * @param type 类型 1固定门店  2移动门店
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取扩展字段
     *
     * @return extra - 扩展字段
     */
    public Map getExtra() {
        return extra;
    }

    /**
     * 设置扩展字段
     *
     * @param extra 扩展字段
     */
    public void setExtra(Map extra) {
        this.extra = extra;
    }
}