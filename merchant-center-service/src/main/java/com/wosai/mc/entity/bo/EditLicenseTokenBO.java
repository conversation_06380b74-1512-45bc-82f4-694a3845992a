package com.wosai.mc.entity.bo;

import com.wosai.mc.model.AppCommonFieldDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 编辑营业执照token bo
 *
 * <AUTHOR>
 * @date 2025/1/21 15:16
 */
@Data
@NoArgsConstructor
public class EditLicenseTokenBO {

    private String token;

    private Timestamp createTime;

    /**
     * 平台标识
     * APP:收钱吧app
     * CRM_APP:crm app
     * CRM_WEB:crm web
     * MSP:商户端网页版
     * SPA:单页应用
     */
    private String platform;

    public EditLicenseTokenBO(String token, Timestamp createTime) {
        this.token = token;
        this.createTime = createTime;
    }

    public EditLicenseTokenBO(String token, Timestamp createTime, String platform) {
        this.token = token;
        this.createTime = createTime;
        this.platform = platform;
    }

    /**
     * 判断是否来自商户端
     *
     * @return true:是 false:否
     */
    public boolean fromMerchant() {
        return AppCommonFieldDTO.PLATFORM_APP.equalsIgnoreCase(platform)
                || AppCommonFieldDTO.PLATFORM_MSP.equalsIgnoreCase(platform);
    }

    /**
     * 判断是否来自销售端
     *
     * @return true:是 false:否
     */
    public boolean fromSales() {
        return AppCommonFieldDTO.PLATFORM_CRM_APP.equalsIgnoreCase(platform)
                || AppCommonFieldDTO.PLATFORM_CRM_WEB.equalsIgnoreCase(platform);
    }
}
