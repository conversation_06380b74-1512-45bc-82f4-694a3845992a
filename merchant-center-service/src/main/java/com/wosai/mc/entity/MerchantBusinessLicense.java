package com.wosai.mc.entity;

import javax.persistence.*;

@Table(name = "merchant_business_license")
public class MerchantBusinessLicense {
    @Id
    private Long id;

    /**
     * 商户号
     */
    @Column(name = "merchant_id")
    private String merchantId;

    /**
     * 0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     */
    private Integer type;

    /**
     * 照片
     */
    private String photo;

    /**
     * 注册号
     */
    private String number;

    /**
     * 名称
     */
    private String name;

    /**
     * 有效期
     */
    private String validity;

    private String address;

    /**
     * 证照上登记的法人姓名
     */
    @Column(name = "registered_legal_person_name")
    private String registeredLegalPersonName;

    /**
     * 授权函
     */
    @Column(name = "letter_of_authorization")
    private String letterOfAuthorization;

    /**
     * 行业许可证
     */
    @Column(name = "trade_license")
    private String tradeLicense;

    /**
     * 法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     */
    @Column(name = "legal_person_id_type")
    private Integer legalPersonIdType;

    /**
     * 法人身份证正面照
     */
    @Column(name = "legal_person_id_card_front_photo")
    private String legalPersonIdCardFrontPhoto;

    /**
     * 法人身份证反面照
     */
    @Column(name = "legal_person_id_card_back_photo")
    private String legalPersonIdCardBackPhoto;

    /**
     * 法人证件姓名
     */
    @Column(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 法人证件号码
     */
    @Column(name = "legal_person_id_number")
    private String legalPersonIdNumber;

    /**
     * 身份证有效期
     */
    @Column(name = "id_validity")
    private String idValidity;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    /**
     * 法人身份证住址
     */
    @Column(name = "legal_person_id_card_address")
    private String legalPersonIdCardAddress;

    /**
     * 法人身份证签发机关
     */
    @Column(name = "legal_person_id_card_issuing_authority")
    private String legalPersonIdCardIssuingAuthority;

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取商户号
     *
     * @return merchant_id - 商户号
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * 设置商户号
     *
     * @param merchantId 商户号
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    /**
     * 获取0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     *
     * @return type - 0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     *
     * @param type 0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取照片
     *
     * @return photo - 照片
     */
    public String getPhoto() {
        return photo;
    }

    /**
     * 设置照片
     *
     * @param photo 照片
     */
    public void setPhoto(String photo) {
        this.photo = photo;
    }

    /**
     * 获取注册号
     *
     * @return number - 注册号
     */
    public String getNumber() {
        return number;
    }

    /**
     * 设置注册号
     *
     * @param number 注册号
     */
    public void setNumber(String number) {
        this.number = number;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取有效期
     *
     * @return validity - 有效期
     */
    public String getValidity() {
        return validity;
    }

    /**
     * 设置有效期
     *
     * @param validity 有效期
     */
    public void setValidity(String validity) {
        this.validity = validity;
    }

    /**
     * @return address
     */
    public String getAddress() {
        return address;
    }

    /**
     * @param address
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 获取证照上登记的法人姓名
     *
     * @return registered_legal_person_name - 证照上登记的法人姓名
     */
    public String getRegisteredLegalPersonName() {
        return registeredLegalPersonName;
    }

    /**
     * 设置证照上登记的法人姓名
     *
     * @param registeredLegalPersonName 证照上登记的法人姓名
     */
    public void setRegisteredLegalPersonName(String registeredLegalPersonName) {
        this.registeredLegalPersonName = registeredLegalPersonName;
    }

    /**
     * 获取授权函
     *
     * @return letter_of_authorization - 授权函
     */
    public String getLetterOfAuthorization() {
        return letterOfAuthorization;
    }

    /**
     * 设置授权函
     *
     * @param letterOfAuthorization 授权函
     */
    public void setLetterOfAuthorization(String letterOfAuthorization) {
        this.letterOfAuthorization = letterOfAuthorization;
    }

    /**
     * 获取行业许可证
     *
     * @return trade_license - 行业许可证
     */
    public String getTradeLicense() {
        return tradeLicense;
    }

    /**
     * 设置行业许可证
     *
     * @param tradeLicense 行业许可证
     */
    public void setTradeLicense(String tradeLicense) {
        this.tradeLicense = tradeLicense;
    }

    /**
     * 获取法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     *
     * @return legal_person_id_type - 法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     */
    public Integer getLegalPersonIdType() {
        return legalPersonIdType;
    }

    /**
     * 设置法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     *
     * @param legalPersonIdType 法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     */
    public void setLegalPersonIdType(Integer legalPersonIdType) {
        this.legalPersonIdType = legalPersonIdType;
    }

    /**
     * 获取法人身份证正面照
     *
     * @return legal_person_id_card_front_photo - 法人身份证正面照
     */
    public String getLegalPersonIdCardFrontPhoto() {
        return legalPersonIdCardFrontPhoto;
    }

    /**
     * 设置法人身份证正面照
     *
     * @param legalPersonIdCardFrontPhoto 法人身份证正面照
     */
    public void setLegalPersonIdCardFrontPhoto(String legalPersonIdCardFrontPhoto) {
        this.legalPersonIdCardFrontPhoto = legalPersonIdCardFrontPhoto;
    }

    /**
     * 获取法人身份证反面照
     *
     * @return legal_person_id_card_back_photo - 法人身份证反面照
     */
    public String getLegalPersonIdCardBackPhoto() {
        return legalPersonIdCardBackPhoto;
    }

    /**
     * 设置法人身份证反面照
     *
     * @param legalPersonIdCardBackPhoto 法人身份证反面照
     */
    public void setLegalPersonIdCardBackPhoto(String legalPersonIdCardBackPhoto) {
        this.legalPersonIdCardBackPhoto = legalPersonIdCardBackPhoto;
    }

    /**
     * 获取法人证件姓名
     *
     * @return legal_person_name - 法人证件姓名
     */
    public String getLegalPersonName() {
        return legalPersonName;
    }

    /**
     * 设置法人证件姓名
     *
     * @param legalPersonName 法人证件姓名
     */
    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    /**
     * 获取法人证件号码
     *
     * @return legal_person_id_number - 法人证件号码
     */
    public String getLegalPersonIdNumber() {
        return legalPersonIdNumber;
    }

    /**
     * 设置法人证件号码
     *
     * @param legalPersonIdNumber 法人证件号码
     */
    public void setLegalPersonIdNumber(String legalPersonIdNumber) {
        this.legalPersonIdNumber = legalPersonIdNumber;
    }

    /**
     * 获取身份证有效期
     *
     * @return id_validity - 身份证有效期
     */
    public String getIdValidity() {
        return idValidity;
    }

    /**
     * 设置身份证有效期
     *
     * @param idValidity 身份证有效期
     */
    public void setIdValidity(String idValidity) {
        this.idValidity = idValidity;
    }

    /**
     * @return ctime
     */
    public Long getCtime() {
        return ctime;
    }

    /**
     * @param ctime
     */
    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    /**
     * @return mtime
     */
    public Long getMtime() {
        return mtime;
    }

    /**
     * @param mtime
     */
    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    /**
     * @return deleted
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * @param deleted
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * @return version
     */
    public Long getVersion() {
        return version;
    }

    /**
     * @param version
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取法人身份证住址
     *
     * @return legal_person_id_card_address - 法人身份证住址
     */
    public String getLegalPersonIdCardAddress() {
        return legalPersonIdCardAddress;
    }

    /**
     * 设置法人身份证住址
     *
     * @param legalPersonIdCardAddress 法人身份证住址
     */
    public void setLegalPersonIdCardAddress(String legalPersonIdCardAddress) {
        this.legalPersonIdCardAddress = legalPersonIdCardAddress;
    }

    /**
     * 获取法人身份证签发机关
     *
     * @return legal_person_id_card_issuing_authority - 法人身份证签发机关
     */
    public String getLegalPersonIdCardIssuingAuthority() {
        return legalPersonIdCardIssuingAuthority;
    }

    /**
     * 设置法人身份证签发机关
     *
     * @param legalPersonIdCardIssuingAuthority 法人身份证签发机关
     */
    public void setLegalPersonIdCardIssuingAuthority(String legalPersonIdCardIssuingAuthority) {
        this.legalPersonIdCardIssuingAuthority = legalPersonIdCardIssuingAuthority;
    }
}