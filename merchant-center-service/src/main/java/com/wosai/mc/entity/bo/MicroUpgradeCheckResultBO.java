package com.wosai.mc.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小微升级校验结果
 *
 * <AUTHOR>
 * @date 2024/9/2 17:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MicroUpgradeCheckResultBO {

    private boolean canUpgrade;

    private String message;

    public static MicroUpgradeCheckResultBO success() {
        return new MicroUpgradeCheckResultBO(true, null);
    }

    public static MicroUpgradeCheckResultBO fail(String message) {
        return new MicroUpgradeCheckResultBO(false, message);
    }

}
