package com.wosai.mc.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class StoreListHandleSuccess {
    private Long id;

    private String merchant_id;

    private String store_id;

    private Integer is_takeout_active;

    private Integer choosed_name;

    private Integer choosed_address;

    private Integer is_merged_name_address;

    private Date name_address_notice_time;

    private Integer is_contract_name_phone_merged;

    private Date contract_name_phone_notice_time;

    private Long ctime;

    private Long mtime;

    private Date urge_notice_10days_later;

    private Date urge_notice_20days_later;

    private String update_params_name_address;

    private String original_name_address;

    private String update_params_contract_name_phone;

    private String original_contract_name_phone;

}