package com.wosai.mc.entity.converter;

import com.wosai.mc.entity.Store;
import com.wosai.mc.model.req.VerifyStoreReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: l<PERSON>uangqiang
 * @Date: 2020-08-18
 * @Description:
 */

@Mapper
public interface VerifyStoreConverter {
    VerifyStoreConverter INSTANCE = Mappers.getMapper(VerifyStoreConverter.class);

    @Mapping(source = "storeId", target = "id")
    @Mapping(target = "verify_status", expression = "java(verifyStatusToInteger(verifyStoreReq.getVerifyStatus()))")
    Store do2dto(VerifyStoreReq verifyStoreReq);

    default Integer verifyStatusToInteger(String verifyStatus) {
        return Integer.parseInt(verifyStatus);

    }
}
