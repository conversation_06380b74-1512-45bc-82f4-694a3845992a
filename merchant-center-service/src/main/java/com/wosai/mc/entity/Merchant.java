package com.wosai.mc.entity;

import javax.persistence.*;
import java.util.Map;

@Table(name = "merchant")
public class Merchant {
    /**
     * UUID
     */
    @Id
    private String id;

    /**
     * sn
     */
    private String sn;

    /**
     * 商户名
     */
    private String name;

    /**
     * 商户别名
     */
    private String alias;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 状态 0关闭  1正常  2禁用
     */
    private Integer status;

    /**
     * 商户信用等级，初始值从推广者继承过来
     */
    private Integer rank;

    /**
     * 1：人工提现；2：智能提现，其他值根据业务定义
     */
    @Column(name = "withdraw_mode")
    private Integer withdrawMode;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 街道地址
     */
    @Column(name = "street_address")
    private String streetAddress;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系固话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 联系手机号
     */
    @Column(name = "contact_cellphone")
    private String contactCellphone;

    /**
     * 联系邮箱
     */
    @Column(name = "contact_email")
    private String contactEmail;

    /**
     * 联系人身份证正面照片
     */
    @Column(name = "concat_id_card_front_photo")
    private String concatIdCardFrontPhoto;

    /**
     * 法人类型 1:个人 2:企业
     */
    @Column(name = "legal_person_type")
    private Integer legalPersonType;

    /**
     * 法人姓名
     */
    @Column(name = "legal_person_name")
    private String legalPersonName;

    /**
     * 法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     */
    @Column(name = "legal_person_id_type")
    private Integer legalPersonIdType;

    /**
     * 法人证件号码
     */
    @Column(name = "legal_person_id_number")
    private String legalPersonIdNumber;

    /**
     * 法人身份证正面照
     */
    @Column(name = "legal_person_id_card_front_photo")
    private String legalPersonIdCardFrontPhoto;

    /**
     * 法人身份证反面照
     */
    @Column(name = "legal_person_id_card_back_photo")
    private String legalPersonIdCardBackPhoto;

    /**
     * 营业执照注册号/个体户注册号
     */
    @Column(name = "legal_person_register_no")
    private String legalPersonRegisterNo;

    /**
     * 营业执照照片
     */
    @Column(name = "business_license_photo")
    private String businessLicensePhoto;

    /**
     * 经营内容
     */
    private String business;

    /**
     * 商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY
     */
    private String currency;

    /**
     * 所有人姓名
     */
    @Column(name = "owner_name")
    private String ownerName;

    /**
     * 所有人手机号
     */
    @Column(name = "owner_cellphone")
    private String ownerCellphone;

    /**
     * 客服电话
     */
    @Column(name = "customer_phone")
    private String customerPhone;

    /**
     * 商户logo
     */
    private String logo;

    /**
     * 卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败
     */
    @Column(name = "bank_account_verify_status")
    private Integer bankAccountVerifyStatus;

    /**
     * 外部商户号
     */
    @Column(name = "client_sn")
    private String clientSn;

    /**
     * 服务商UUID
     */
    @Column(name = "vendor_id")
    private String vendorId;

    /**
     * 推广者UUID
     */
    @Column(name = "solicitor_id")
    private String solicitorId;

    /**
     * 商户所属平台platform: 0: 收钱吧 1: 拉卡拉
     */
    private Integer platform;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    /**
     * 商户经营名称
     */
    @Column(name = "business_name")
    private String businessName;

    /**
     * 联系人身份证号
     */
    @Column(name = "concat_identity")
    private String concatIdentity;

    /**
     * 个人0、个体1、组织2
     */
    @Column(name = "merchant_type")
    private Integer merchantType;

    /**
     * 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    private Integer verify_status;

    /**
     * 扩展字段
     */
    private Map extra;

    /**
     * 获取UUID
     *
     * @return id - UUID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置UUID
     *
     * @param id UUID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取sn
     *
     * @return sn - sn
     */
    public String getSn() {
        return sn;
    }

    /**
     * 设置sn
     *
     * @param sn sn
     */
    public void setSn(String sn) {
        this.sn = sn;
    }

    /**
     * 获取商户名
     *
     * @return name - 商户名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置商户名
     *
     * @param name 商户名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取商户别名
     *
     * @return alias - 商户别名
     */
    public String getAlias() {
        return alias;
    }

    /**
     * 设置商户别名
     *
     * @param alias 商户别名
     */
    public void setAlias(String alias) {
        this.alias = alias;
    }

    /**
     * 获取行业id
     *
     * @return industry - 行业id
     */
    public String getIndustry() {
        return industry;
    }

    /**
     * 设置行业id
     *
     * @param industry 行业id
     */
    public void setIndustry(String industry) {
        this.industry = industry;
    }

    /**
     * 获取状态 0关闭  1正常  2禁用
     *
     * @return status - 状态 0关闭  1正常  2禁用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 0关闭  1正常  2禁用
     *
     * @param status 状态 0关闭  1正常  2禁用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取商户信用等级，初始值从推广者继承过来
     *
     * @return rank - 商户信用等级，初始值从推广者继承过来
     */
    public Integer getRank() {
        return rank;
    }

    /**
     * 设置商户信用等级，初始值从推广者继承过来
     *
     * @param rank 商户信用等级，初始值从推广者继承过来
     */
    public void setRank(Integer rank) {
        this.rank = rank;
    }

    /**
     * 获取1：人工提现；2：智能提现，其他值根据业务定义
     *
     * @return withdraw_mode - 1：人工提现；2：智能提现，其他值根据业务定义
     */
    public Integer getWithdrawMode() {
        return withdrawMode;
    }

    /**
     * 设置1：人工提现；2：智能提现，其他值根据业务定义
     *
     * @param withdrawMode 1：人工提现；2：智能提现，其他值根据业务定义
     */
    public void setWithdrawMode(Integer withdrawMode) {
        this.withdrawMode = withdrawMode;
    }

    /**
     * 获取经度
     *
     * @return longitude - 经度
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * 设置经度
     *
     * @param longitude 经度
     */
    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取纬度
     *
     * @return latitude - 纬度
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度
     *
     * @param latitude 纬度
     */
    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    /**
     * 获取国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN
     *
     * @return country - 国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN
     */
    public String getCountry() {
        return country;
    }

    /**
     * 设置国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN
     *
     * @param country 国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN
     */
    public void setCountry(String country) {
        this.country = country;
    }

    /**
     * 获取省
     *
     * @return province - 省
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置省
     *
     * @param province 省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 获取市
     *
     * @return city - 市
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置市
     *
     * @param city 市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 获取区
     *
     * @return district - 区
     */
    public String getDistrict() {
        return district;
    }

    /**
     * 设置区
     *
     * @param district 区
     */
    public void setDistrict(String district) {
        this.district = district;
    }

    /**
     * 获取街道地址
     *
     * @return street_address - 街道地址
     */
    public String getStreetAddress() {
        return streetAddress;
    }

    /**
     * 设置街道地址
     *
     * @param streetAddress 街道地址
     */
    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    /**
     * 获取联系人姓名
     *
     * @return contact_name - 联系人姓名
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * 设置联系人姓名
     *
     * @param contactName 联系人姓名
     */
    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    /**
     * 获取联系固话
     *
     * @return contact_phone - 联系固话
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置联系固话
     *
     * @param contactPhone 联系固话
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    /**
     * 获取联系手机号
     *
     * @return contact_cellphone - 联系手机号
     */
    public String getContactCellphone() {
        return contactCellphone;
    }

    /**
     * 设置联系手机号
     *
     * @param contactCellphone 联系手机号
     */
    public void setContactCellphone(String contactCellphone) {
        this.contactCellphone = contactCellphone;
    }

    /**
     * 获取联系邮箱
     *
     * @return contact_email - 联系邮箱
     */
    public String getContactEmail() {
        return contactEmail;
    }

    /**
     * 设置联系邮箱
     *
     * @param contactEmail 联系邮箱
     */
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    /**
     * 获取联系人身份证正面照片
     *
     * @return concat_id_card_front_photo - 联系人身份证正面照片
     */
    public String getConcatIdCardFrontPhoto() {
        return concatIdCardFrontPhoto;
    }

    /**
     * 设置联系人身份证正面照片
     *
     * @param concatIdCardFrontPhoto 联系人身份证正面照片
     */
    public void setConcatIdCardFrontPhoto(String concatIdCardFrontPhoto) {
        this.concatIdCardFrontPhoto = concatIdCardFrontPhoto;
    }

    /**
     * 获取法人类型 1:个人 2:企业
     *
     * @return legal_person_type - 法人类型 1:个人 2:企业
     */
    public Integer getLegalPersonType() {
        return legalPersonType;
    }

    /**
     * 设置法人类型 1:个人 2:企业
     *
     * @param legalPersonType 法人类型 1:个人 2:企业
     */
    public void setLegalPersonType(Integer legalPersonType) {
        this.legalPersonType = legalPersonType;
    }

    /**
     * 获取法人姓名
     *
     * @return legal_person_name - 法人姓名
     */
    public String getLegalPersonName() {
        return legalPersonName;
    }

    /**
     * 设置法人姓名
     *
     * @param legalPersonName 法人姓名
     */
    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    /**
     * 获取法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     *
     * @return legal_person_id_type - 法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     */
    public Integer getLegalPersonIdType() {
        return legalPersonIdType;
    }

    /**
     * 设置法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     *
     * @param legalPersonIdType 法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     */
    public void setLegalPersonIdType(Integer legalPersonIdType) {
        this.legalPersonIdType = legalPersonIdType;
    }

    /**
     * 获取法人证件号码
     *
     * @return legal_person_id_number - 法人证件号码
     */
    public String getLegalPersonIdNumber() {
        return legalPersonIdNumber;
    }

    /**
     * 设置法人证件号码
     *
     * @param legalPersonIdNumber 法人证件号码
     */
    public void setLegalPersonIdNumber(String legalPersonIdNumber) {
        this.legalPersonIdNumber = legalPersonIdNumber;
    }

    /**
     * 获取法人身份证正面照
     *
     * @return legal_person_id_card_front_photo - 法人身份证正面照
     */
    public String getLegalPersonIdCardFrontPhoto() {
        return legalPersonIdCardFrontPhoto;
    }

    /**
     * 设置法人身份证正面照
     *
     * @param legalPersonIdCardFrontPhoto 法人身份证正面照
     */
    public void setLegalPersonIdCardFrontPhoto(String legalPersonIdCardFrontPhoto) {
        this.legalPersonIdCardFrontPhoto = legalPersonIdCardFrontPhoto;
    }

    /**
     * 获取法人身份证反面照
     *
     * @return legal_person_id_card_back_photo - 法人身份证反面照
     */
    public String getLegalPersonIdCardBackPhoto() {
        return legalPersonIdCardBackPhoto;
    }

    /**
     * 设置法人身份证反面照
     *
     * @param legalPersonIdCardBackPhoto 法人身份证反面照
     */
    public void setLegalPersonIdCardBackPhoto(String legalPersonIdCardBackPhoto) {
        this.legalPersonIdCardBackPhoto = legalPersonIdCardBackPhoto;
    }

    /**
     * 获取营业执照注册号/个体户注册号
     *
     * @return legal_person_register_no - 营业执照注册号/个体户注册号
     */
    public String getLegalPersonRegisterNo() {
        return legalPersonRegisterNo;
    }

    /**
     * 设置营业执照注册号/个体户注册号
     *
     * @param legalPersonRegisterNo 营业执照注册号/个体户注册号
     */
    public void setLegalPersonRegisterNo(String legalPersonRegisterNo) {
        this.legalPersonRegisterNo = legalPersonRegisterNo;
    }

    /**
     * 获取营业执照照片
     *
     * @return business_license_photo - 营业执照照片
     */
    public String getBusinessLicensePhoto() {
        return businessLicensePhoto;
    }

    /**
     * 设置营业执照照片
     *
     * @param businessLicensePhoto 营业执照照片
     */
    public void setBusinessLicensePhoto(String businessLicensePhoto) {
        this.businessLicensePhoto = businessLicensePhoto;
    }

    /**
     * 获取经营内容
     *
     * @return business - 经营内容
     */
    public String getBusiness() {
        return business;
    }

    /**
     * 设置经营内容
     *
     * @param business 经营内容
     */
    public void setBusiness(String business) {
        this.business = business;
    }

    /**
     * 获取商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY
     *
     * @return currency - 商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY
     */
    public String getCurrency() {
        return currency;
    }

    /**
     * 设置商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY
     *
     * @param currency 商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY
     */
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    /**
     * 获取所有人姓名
     *
     * @return owner_name - 所有人姓名
     */
    public String getOwnerName() {
        return ownerName;
    }

    /**
     * 设置所有人姓名
     *
     * @param ownerName 所有人姓名
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * 获取所有人手机号
     *
     * @return owner_cellphone - 所有人手机号
     */
    public String getOwnerCellphone() {
        return ownerCellphone;
    }

    /**
     * 设置所有人手机号
     *
     * @param ownerCellphone 所有人手机号
     */
    public void setOwnerCellphone(String ownerCellphone) {
        this.ownerCellphone = ownerCellphone;
    }

    /**
     * 获取客服电话
     *
     * @return customer_phone - 客服电话
     */
    public String getCustomerPhone() {
        return customerPhone;
    }

    /**
     * 设置客服电话
     *
     * @param customerPhone 客服电话
     */
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    /**
     * 获取商户logo
     *
     * @return logo - 商户logo
     */
    public String getLogo() {
        return logo;
    }

    /**
     * 设置商户logo
     *
     * @param logo 商户logo
     */
    public void setLogo(String logo) {
        this.logo = logo;
    }

    /**
     * 获取卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败
     *
     * @return bank_account_verify_status - 卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败
     */
    public Integer getBankAccountVerifyStatus() {
        return bankAccountVerifyStatus;
    }

    /**
     * 设置卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败
     *
     * @param bankAccountVerifyStatus 卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败
     */
    public void setBankAccountVerifyStatus(Integer bankAccountVerifyStatus) {
        this.bankAccountVerifyStatus = bankAccountVerifyStatus;
    }

    /**
     * 获取外部商户号
     *
     * @return client_sn - 外部商户号
     */
    public String getClientSn() {
        return clientSn;
    }

    /**
     * 设置外部商户号
     *
     * @param clientSn 外部商户号
     */
    public void setClientSn(String clientSn) {
        this.clientSn = clientSn;
    }

    /**
     * 获取服务商UUID
     *
     * @return vendor_id - 服务商UUID
     */
    public String getVendorId() {
        return vendorId;
    }

    /**
     * 设置服务商UUID
     *
     * @param vendorId 服务商UUID
     */
    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    /**
     * 获取推广者UUID
     *
     * @return solicitor_id - 推广者UUID
     */
    public String getSolicitorId() {
        return solicitorId;
    }

    /**
     * 设置推广者UUID
     *
     * @param solicitorId 推广者UUID
     */
    public void setSolicitorId(String solicitorId) {
        this.solicitorId = solicitorId;
    }

    /**
     * 获取商户所属平台platform: 0: 收钱吧 1: 拉卡拉
     *
     * @return platform - 商户所属平台platform: 0: 收钱吧 1: 拉卡拉
     */
    public Integer getPlatform() {
        return platform;
    }

    /**
     * 设置商户所属平台platform: 0: 收钱吧 1: 拉卡拉
     *
     * @param platform 商户所属平台platform: 0: 收钱吧 1: 拉卡拉
     */
    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    /**
     * @return ctime
     */
    public Long getCtime() {
        return ctime;
    }

    /**
     * @param ctime
     */
    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    /**
     * @return mtime
     */
    public Long getMtime() {
        return mtime;
    }

    /**
     * @param mtime
     */
    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    /**
     * @return deleted
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * @param deleted
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * @return version
     */
    public Long getVersion() {
        return version;
    }

    /**
     * @param version
     */
    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 获取商户经营名称
     *
     * @return business_name - 商户经营名称
     */
    public String getBusinessName() {
        return businessName;
    }

    /**
     * 设置商户经营名称
     *
     * @param businessName 商户经营名称
     */
    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    /**
     * 获取联系人身份证号
     *
     * @return concat_identity - 联系人身份证号
     */
    public String getConcatIdentity() {
        return concatIdentity;
    }

    /**
     * 设置联系人身份证号
     *
     * @param concatIdentity 联系人身份证号
     */
    public void setConcatIdentity(String concatIdentity) {
        this.concatIdentity = concatIdentity;
    }

    /**
     * 获取个人0、个体1、组织2
     *
     * @return merchant_type - 个人0、个体1、组织2
     */
    public Integer getMerchantType() {
        return merchantType;
    }

    /**
     * 设置个人0、个体1、组织2
     *
     * @param merchantType 个人0、个体1、组织2
     */
    public void setMerchantType(Integer merchantType) {
        this.merchantType = merchantType;
    }

    /**
     * 获取认证状态 0未知  1未认证  2认证成功  3认证失败
     *
     * @return verify_status - 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    public Integer getVerify_status() {
        return verify_status;
    }

    /**
     * 设置认证状态 0未知  1未认证  2认证成功  3认证失败
     *
     * @param verify_status 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    public void setVerify_status(Integer verify_status) {
        this.verify_status = verify_status;
    }

    /**
     * 获取扩展字段
     *
     * @return extra - 扩展字段
     */
    public Map getExtra() {
        return extra;
    }

    /**
     * 设置扩展字段
     *
     * @param extra 扩展字段
     */
    public void setExtra(Map extra) {
        this.extra = extra;
    }
}