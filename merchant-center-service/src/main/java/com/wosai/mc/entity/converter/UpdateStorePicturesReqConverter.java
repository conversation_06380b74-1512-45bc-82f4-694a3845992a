package com.wosai.mc.entity.converter;

import com.wosai.mc.model.req.UpdateStorePicturesReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-18
 * @Description:
 */

@Mapper
public interface UpdateStorePicturesReqConverter {
    UpdateStorePicturesReqConverter INSTANCE = Mappers.getMapper(UpdateStorePicturesReqConverter.class);

    UpdateStorePicturesReq do2dto(StotreExtInfoAndPictures storeExtAndPictures);

}
