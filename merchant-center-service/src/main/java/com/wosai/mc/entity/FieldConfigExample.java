package com.wosai.mc.entity;

import java.util.ArrayList;
import java.util.List;

public class FieldConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public FieldConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTable_nameIsNull() {
            addCriterion("table_name is null");
            return (Criteria) this;
        }

        public Criteria andTable_nameIsNotNull() {
            addCriterion("table_name is not null");
            return (Criteria) this;
        }

        public Criteria andTable_nameEqualTo(String value) {
            addCriterion("table_name =", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameNotEqualTo(String value) {
            addCriterion("table_name <>", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameGreaterThan(String value) {
            addCriterion("table_name >", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameGreaterThanOrEqualTo(String value) {
            addCriterion("table_name >=", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameLessThan(String value) {
            addCriterion("table_name <", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameLessThanOrEqualTo(String value) {
            addCriterion("table_name <=", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameLike(String value) {
            addCriterion("table_name like", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameNotLike(String value) {
            addCriterion("table_name not like", value, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameIn(List<String> values) {
            addCriterion("table_name in", values, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameNotIn(List<String> values) {
            addCriterion("table_name not in", values, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameBetween(String value1, String value2) {
            addCriterion("table_name between", value1, value2, "table_name");
            return (Criteria) this;
        }

        public Criteria andTable_nameNotBetween(String value1, String value2) {
            addCriterion("table_name not between", value1, value2, "table_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameIsNull() {
            addCriterion("filed_name is null");
            return (Criteria) this;
        }

        public Criteria andFiled_nameIsNotNull() {
            addCriterion("filed_name is not null");
            return (Criteria) this;
        }

        public Criteria andFiled_nameEqualTo(String value) {
            addCriterion("filed_name =", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameNotEqualTo(String value) {
            addCriterion("filed_name <>", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameGreaterThan(String value) {
            addCriterion("filed_name >", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameGreaterThanOrEqualTo(String value) {
            addCriterion("filed_name >=", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameLessThan(String value) {
            addCriterion("filed_name <", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameLessThanOrEqualTo(String value) {
            addCriterion("filed_name <=", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameLike(String value) {
            addCriterion("filed_name like", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameNotLike(String value) {
            addCriterion("filed_name not like", value, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameIn(List<String> values) {
            addCriterion("filed_name in", values, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameNotIn(List<String> values) {
            addCriterion("filed_name not in", values, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameBetween(String value1, String value2) {
            addCriterion("filed_name between", value1, value2, "filed_name");
            return (Criteria) this;
        }

        public Criteria andFiled_nameNotBetween(String value1, String value2) {
            addCriterion("filed_name not between", value1, value2, "filed_name");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeIsNull() {
            addCriterion("filed_type is null");
            return (Criteria) this;
        }

        public Criteria andFiled_typeIsNotNull() {
            addCriterion("filed_type is not null");
            return (Criteria) this;
        }

        public Criteria andFiled_typeEqualTo(String value) {
            addCriterion("filed_type =", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeNotEqualTo(String value) {
            addCriterion("filed_type <>", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeGreaterThan(String value) {
            addCriterion("filed_type >", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeGreaterThanOrEqualTo(String value) {
            addCriterion("filed_type >=", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeLessThan(String value) {
            addCriterion("filed_type <", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeLessThanOrEqualTo(String value) {
            addCriterion("filed_type <=", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeLike(String value) {
            addCriterion("filed_type like", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeNotLike(String value) {
            addCriterion("filed_type not like", value, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeIn(List<String> values) {
            addCriterion("filed_type in", values, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeNotIn(List<String> values) {
            addCriterion("filed_type not in", values, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeBetween(String value1, String value2) {
            addCriterion("filed_type between", value1, value2, "filed_type");
            return (Criteria) this;
        }

        public Criteria andFiled_typeNotBetween(String value1, String value2) {
            addCriterion("filed_type not between", value1, value2, "filed_type");
            return (Criteria) this;
        }

        public Criteria andRegxIsNull() {
            addCriterion("regx is null");
            return (Criteria) this;
        }

        public Criteria andRegxIsNotNull() {
            addCriterion("regx is not null");
            return (Criteria) this;
        }

        public Criteria andRegxEqualTo(String value) {
            addCriterion("regx =", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxNotEqualTo(String value) {
            addCriterion("regx <>", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxGreaterThan(String value) {
            addCriterion("regx >", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxGreaterThanOrEqualTo(String value) {
            addCriterion("regx >=", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxLessThan(String value) {
            addCriterion("regx <", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxLessThanOrEqualTo(String value) {
            addCriterion("regx <=", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxLike(String value) {
            addCriterion("regx like", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxNotLike(String value) {
            addCriterion("regx not like", value, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxIn(List<String> values) {
            addCriterion("regx in", values, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxNotIn(List<String> values) {
            addCriterion("regx not in", values, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxBetween(String value1, String value2) {
            addCriterion("regx between", value1, value2, "regx");
            return (Criteria) this;
        }

        public Criteria andRegxNotBetween(String value1, String value2) {
            addCriterion("regx not between", value1, value2, "regx");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andExampleIsNull() {
            addCriterion("example is null");
            return (Criteria) this;
        }

        public Criteria andExampleIsNotNull() {
            addCriterion("example is not null");
            return (Criteria) this;
        }

        public Criteria andExampleEqualTo(String value) {
            addCriterion("example =", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleNotEqualTo(String value) {
            addCriterion("example <>", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleGreaterThan(String value) {
            addCriterion("example >", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleGreaterThanOrEqualTo(String value) {
            addCriterion("example >=", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleLessThan(String value) {
            addCriterion("example <", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleLessThanOrEqualTo(String value) {
            addCriterion("example <=", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleLike(String value) {
            addCriterion("example like", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleNotLike(String value) {
            addCriterion("example not like", value, "example");
            return (Criteria) this;
        }

        public Criteria andExampleIn(List<String> values) {
            addCriterion("example in", values, "example");
            return (Criteria) this;
        }

        public Criteria andExampleNotIn(List<String> values) {
            addCriterion("example not in", values, "example");
            return (Criteria) this;
        }

        public Criteria andExampleBetween(String value1, String value2) {
            addCriterion("example between", value1, value2, "example");
            return (Criteria) this;
        }

        public Criteria andExampleNotBetween(String value1, String value2) {
            addCriterion("example not between", value1, value2, "example");
            return (Criteria) this;
        }

        public Criteria andFront_nameIsNull() {
            addCriterion("front_name is null");
            return (Criteria) this;
        }

        public Criteria andFront_nameIsNotNull() {
            addCriterion("front_name is not null");
            return (Criteria) this;
        }

        public Criteria andFront_nameEqualTo(String value) {
            addCriterion("front_name =", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameNotEqualTo(String value) {
            addCriterion("front_name <>", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameGreaterThan(String value) {
            addCriterion("front_name >", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameGreaterThanOrEqualTo(String value) {
            addCriterion("front_name >=", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameLessThan(String value) {
            addCriterion("front_name <", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameLessThanOrEqualTo(String value) {
            addCriterion("front_name <=", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameLike(String value) {
            addCriterion("front_name like", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameNotLike(String value) {
            addCriterion("front_name not like", value, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameIn(List<String> values) {
            addCriterion("front_name in", values, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameNotIn(List<String> values) {
            addCriterion("front_name not in", values, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameBetween(String value1, String value2) {
            addCriterion("front_name between", value1, value2, "front_name");
            return (Criteria) this;
        }

        public Criteria andFront_nameNotBetween(String value1, String value2) {
            addCriterion("front_name not between", value1, value2, "front_name");
            return (Criteria) this;
        }

        public Criteria andFail_msgIsNull() {
            addCriterion("fail_msg is null");
            return (Criteria) this;
        }

        public Criteria andFail_msgIsNotNull() {
            addCriterion("fail_msg is not null");
            return (Criteria) this;
        }

        public Criteria andFail_msgEqualTo(String value) {
            addCriterion("fail_msg =", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgNotEqualTo(String value) {
            addCriterion("fail_msg <>", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgGreaterThan(String value) {
            addCriterion("fail_msg >", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgGreaterThanOrEqualTo(String value) {
            addCriterion("fail_msg >=", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgLessThan(String value) {
            addCriterion("fail_msg <", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgLessThanOrEqualTo(String value) {
            addCriterion("fail_msg <=", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgLike(String value) {
            addCriterion("fail_msg like", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgNotLike(String value) {
            addCriterion("fail_msg not like", value, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgIn(List<String> values) {
            addCriterion("fail_msg in", values, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgNotIn(List<String> values) {
            addCriterion("fail_msg not in", values, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgBetween(String value1, String value2) {
            addCriterion("fail_msg between", value1, value2, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andFail_msgNotBetween(String value1, String value2) {
            addCriterion("fail_msg not between", value1, value2, "fail_msg");
            return (Criteria) this;
        }

        public Criteria andPlace_holderIsNull() {
            addCriterion("place_holder is null");
            return (Criteria) this;
        }

        public Criteria andPlace_holderIsNotNull() {
            addCriterion("place_holder is not null");
            return (Criteria) this;
        }

        public Criteria andPlace_holderEqualTo(String value) {
            addCriterion("place_holder =", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderNotEqualTo(String value) {
            addCriterion("place_holder <>", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderGreaterThan(String value) {
            addCriterion("place_holder >", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderGreaterThanOrEqualTo(String value) {
            addCriterion("place_holder >=", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderLessThan(String value) {
            addCriterion("place_holder <", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderLessThanOrEqualTo(String value) {
            addCriterion("place_holder <=", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderLike(String value) {
            addCriterion("place_holder like", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderNotLike(String value) {
            addCriterion("place_holder not like", value, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderIn(List<String> values) {
            addCriterion("place_holder in", values, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderNotIn(List<String> values) {
            addCriterion("place_holder not in", values, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderBetween(String value1, String value2) {
            addCriterion("place_holder between", value1, value2, "place_holder");
            return (Criteria) this;
        }

        public Criteria andPlace_holderNotBetween(String value1, String value2) {
            addCriterion("place_holder not between", value1, value2, "place_holder");
            return (Criteria) this;
        }

        public Criteria andExplain_msgIsNull() {
            addCriterion("explain_msg is null");
            return (Criteria) this;
        }

        public Criteria andExplain_msgIsNotNull() {
            addCriterion("explain_msg is not null");
            return (Criteria) this;
        }

        public Criteria andExplain_msgEqualTo(String value) {
            addCriterion("explain_msg =", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgNotEqualTo(String value) {
            addCriterion("explain_msg <>", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgGreaterThan(String value) {
            addCriterion("explain_msg >", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgGreaterThanOrEqualTo(String value) {
            addCriterion("explain_msg >=", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgLessThan(String value) {
            addCriterion("explain_msg <", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgLessThanOrEqualTo(String value) {
            addCriterion("explain_msg <=", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgLike(String value) {
            addCriterion("explain_msg like", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgNotLike(String value) {
            addCriterion("explain_msg not like", value, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgIn(List<String> values) {
            addCriterion("explain_msg in", values, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgNotIn(List<String> values) {
            addCriterion("explain_msg not in", values, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgBetween(String value1, String value2) {
            addCriterion("explain_msg between", value1, value2, "explain_msg");
            return (Criteria) this;
        }

        public Criteria andExplain_msgNotBetween(String value1, String value2) {
            addCriterion("explain_msg not between", value1, value2, "explain_msg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}