package com.wosai.mc.entity.converter;

import com.wosai.mc.entity.Merchant;
import com.wosai.mc.model.req.VerifyMerchantReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-18
 * @Description:
 */

@Mapper
public interface VerifyMerchantConverter {
    VerifyMerchantConverter INSTANCE = Mappers.getMapper(VerifyMerchantConverter.class);

    @Mapping(source = "merchantId", target = "id")
    @Mapping(target = "verify_status", expression = "java(verifyStatusToInteger(verifyMerchantReq.getVerifyStatus()))")
    Merchant do2dto(VerifyMerchantReq verifyMerchantReq);

    default Integer verifyStatusToInteger(String verifyStatus) {
        return Integer.parseInt(verifyStatus);

    }
}
