package com.wosai.mc.entity.converter;

import com.wosai.app.dto.V2.CreateUcUserAccountReq;
import com.wosai.mc.model.req.AccountReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccountReqConverter {

    AccountReqConverter INSTANCE = Mappers.getMapper(AccountReqConverter.class);

    @Mapping(source = "identityType", target = "identity_type")
    @Mapping(source = "identifier", target = "cellphone")
    CreateUcUserAccountReq do2dto(AccountReq accountReq);



}