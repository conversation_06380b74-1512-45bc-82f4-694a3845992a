package com.wosai.mc.entity.converter;

import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.req.VerifyMerchantBusinessLicenseReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-18
 * @Description:
 */

@Mapper
public interface VerifyMerchantBusinessConverter {
    VerifyMerchantBusinessConverter INSTANCE = Mappers.getMapper(VerifyMerchantBusinessConverter.class);

    @Mapping(source = "merchantId", target = "merchant_id")
    @Mapping(target = "verify_status", expression = "java(verifyStatusToInteger(verifyMerchantBusinessLicenseReq.getVerifyStatus()))")
    MerchantBusinessLicenseInfo do2dto(VerifyMerchantBusinessLicenseReq verifyMerchantBusinessLicenseReq);

    default Integer verifyStatusToInteger(String verifyStatus) {
        return Integer.parseInt(verifyStatus);

    }
}
