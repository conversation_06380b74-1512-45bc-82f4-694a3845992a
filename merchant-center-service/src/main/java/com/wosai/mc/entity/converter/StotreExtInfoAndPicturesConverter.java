package com.wosai.mc.entity.converter;

import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: <PERSON><PERSON><PERSON>qiang
 * @Date: 2020-08-18
 * @Description:
 */

@Mapper
public interface StotreExtInfoAndPicturesConverter {
    StotreExtInfoAndPicturesConverter INSTANCE = Mappers.getMapper(StotreExtInfoAndPicturesConverter.class);

    StotreExtInfoAndPictures do2dto(StoreExtInfo stotreExtInfo);

}
