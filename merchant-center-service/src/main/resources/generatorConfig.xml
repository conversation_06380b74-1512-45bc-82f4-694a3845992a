<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="mysqlTables" targetRuntime="MyBatis3" defaultModelType="flat">

        <!--去除注释  -->
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="***************************************************************************"
                        userId="p_shanchao" password="lIN0Udf%gsqn#OzZ@!kQo_HPG)Xe"/>  <!-- 到 Dbmanger上获取自己的账号密码 -->

        <!-- 指定生成的类型为java类型，避免数据库中number等类型字段 -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 生成model模型，对应的包，存放位置可以指定具体的路径,如/ProjectName/src，也可以使用MAVEN来自动生成 -->
        <javaModelGenerator targetPackage="com.wosai.mc.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaModelGenerator>

        <!--对应的xml mapper文件  -->
        <sqlMapGenerator targetPackage="resources.mapper" targetProject="src/main">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 对应的dao接口 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.wosai.mc.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <table tableName="business_license_edit_draft" domainObjectName="BusinessLicenseEditDraft"
               enableCountByExample="false" enableUpdateByExample="true"
               enableDeleteByExample="true" enableSelectByExample="true"
               selectByExampleQueryId="true">
            <property name="useActualColumnNames" value="false"/>
        </table>
    </context>
</generatorConfiguration>
