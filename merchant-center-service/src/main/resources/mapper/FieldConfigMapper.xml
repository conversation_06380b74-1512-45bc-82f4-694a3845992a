<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.mc.mapper.FieldConfigMapper">
  <resultMap id="BaseResultMap" type="com.wosai.mc.model.FieldConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="table_name" />
    <result column="filed_name" jdbcType="VARCHAR" property="filed_name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="filed_type" jdbcType="VARCHAR" property="filed_type" />
    <result column="regx" jdbcType="VARCHAR" property="regx" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="example" jdbcType="VARCHAR" property="example" />
    <result column="front_name" jdbcType="VARCHAR" property="front_name" />
    <result column="fail_msg" jdbcType="VARCHAR" property="fail_msg" />
    <result column="place_holder" jdbcType="VARCHAR" property="place_holder" />
    <result column="explain_msg" jdbcType="VARCHAR" property="explain_msg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, table_name, filed_name, type, filed_type, regx, description, example, front_name, 
    fail_msg, place_holder, explain_msg
  </sql>
  <select id="selectByExample" parameterType="com.wosai.mc.entity.FieldConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from field_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from field_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from field_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.mc.model.FieldConfig">
    insert into field_config (id, table_name, filed_name, 
      type, filed_type, regx, 
      description, example, front_name, 
      fail_msg, place_holder, explain_msg
      )
    values (#{id,jdbcType=BIGINT}, #{table_name,jdbcType=VARCHAR}, #{filed_name,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{filed_type,jdbcType=VARCHAR}, #{regx,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{example,jdbcType=VARCHAR}, #{front_name,jdbcType=VARCHAR}, 
      #{fail_msg,jdbcType=VARCHAR}, #{place_holder,jdbcType=VARCHAR}, #{explain_msg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.mc.model.FieldConfig">
    insert into field_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="table_name != null">
        table_name,
      </if>
      <if test="filed_name != null">
        filed_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="filed_type != null">
        filed_type,
      </if>
      <if test="regx != null">
        regx,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="example != null">
        example,
      </if>
      <if test="front_name != null">
        front_name,
      </if>
      <if test="fail_msg != null">
        fail_msg,
      </if>
      <if test="place_holder != null">
        place_holder,
      </if>
      <if test="explain_msg != null">
        explain_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="table_name != null">
        #{table_name,jdbcType=VARCHAR},
      </if>
      <if test="filed_name != null">
        #{filed_name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="filed_type != null">
        #{filed_type,jdbcType=VARCHAR},
      </if>
      <if test="regx != null">
        #{regx,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="example != null">
        #{example,jdbcType=VARCHAR},
      </if>
      <if test="front_name != null">
        #{front_name,jdbcType=VARCHAR},
      </if>
      <if test="fail_msg != null">
        #{fail_msg,jdbcType=VARCHAR},
      </if>
      <if test="place_holder != null">
        #{place_holder,jdbcType=VARCHAR},
      </if>
      <if test="explain_msg != null">
        #{explain_msg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.mc.model.FieldConfig">
    update field_config
    <set>
      <if test="table_name != null">
        table_name = #{table_name,jdbcType=VARCHAR},
      </if>
      <if test="filed_name != null">
        filed_name = #{filed_name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="filed_type != null">
        filed_type = #{filed_type,jdbcType=VARCHAR},
      </if>
      <if test="regx != null">
        regx = #{regx,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="example != null">
        example = #{example,jdbcType=VARCHAR},
      </if>
      <if test="front_name != null">
        front_name = #{front_name,jdbcType=VARCHAR},
      </if>
      <if test="fail_msg != null">
        fail_msg = #{fail_msg,jdbcType=VARCHAR},
      </if>
      <if test="place_holder != null">
        place_holder = #{place_holder,jdbcType=VARCHAR},
      </if>
      <if test="explain_msg != null">
        explain_msg = #{explain_msg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.mc.model.FieldConfig">
    update field_config
    set table_name = #{table_name,jdbcType=VARCHAR},
      filed_name = #{filed_name,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      filed_type = #{filed_type,jdbcType=VARCHAR},
      regx = #{regx,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      example = #{example,jdbcType=VARCHAR},
      front_name = #{front_name,jdbcType=VARCHAR},
      fail_msg = #{fail_msg,jdbcType=VARCHAR},
      place_holder = #{place_holder,jdbcType=VARCHAR},
      explain_msg = #{explain_msg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>