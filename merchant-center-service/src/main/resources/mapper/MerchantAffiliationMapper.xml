<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.mc.mapper.MerchantAffiliationMapper">
  <resultMap id="BaseResultMap" type="com.wosai.mc.entity.MerchantAffiliation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_sn" jdbcType="VARCHAR" property="merchant_sn" />
    <result column="parent_merchant_sn" jdbcType="VARCHAR" property="parent_merchant_sn" />
    <result column="create_at" jdbcType="TIMESTAMP" property="create_at" />
    <result column="update_at" jdbcType="TIMESTAMP" property="update_at" />
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_sn, parent_merchant_sn, create_at, update_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_affiliation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByMerchantSn" resultType="com.wosai.mc.entity.MerchantAffiliation">
    select * from merchant_affiliation where merchant_sn = #{merchantSn} limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_affiliation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.mc.entity.MerchantAffiliation">
    insert into merchant_affiliation (id, merchant_sn, parent_merchant_sn, 
      create_at, update_at)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{parent_merchant_sn,jdbcType=VARCHAR}, 
      #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.mc.entity.MerchantAffiliation">
    insert into merchant_affiliation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchant_sn != null">
        merchant_sn,
      </if>
      <if test="parent_merchant_sn != null">
        parent_merchant_sn,
      </if>
      <if test="create_at != null">
        create_at,
      </if>
      <if test="update_at != null">
        update_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null">
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="parent_merchant_sn != null">
        #{parent_merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null">
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null">
        #{update_at,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.mc.entity.MerchantAffiliation">
    update merchant_affiliation
    <set>
      <if test="merchant_sn != null">
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="parent_merchant_sn != null">
        parent_merchant_sn = #{parent_merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null">
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null">
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.mc.entity.MerchantAffiliation">
    update merchant_affiliation
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      parent_merchant_sn = #{parent_merchant_sn,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>