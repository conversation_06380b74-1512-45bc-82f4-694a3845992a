<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.mc.mapper.BusinessLicenseEditDraftMapper">
  <resultMap id="BaseResultMap" type="com.wosai.mc.entity.BusinessLicenseEditDraft">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.mc.entity.BusinessLicenseEditDraft">
    <result column="draft" jdbcType="LONGVARCHAR" property="draft" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_sn, operator, mtime, ctime
  </sql>
  <sql id="Blob_Column_List">
    draft
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraftExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from business_license_edit_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraftExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from business_license_edit_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from business_license_edit_draft
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_license_edit_draft
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraftExample">
    delete from business_license_edit_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraft">
    insert into business_license_edit_draft (id, merchant_sn, operator, 
      mtime, ctime, draft
      )
    values (#{id,jdbcType=BIGINT}, #{merchantSn,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{mtime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{draft,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraft">
    insert into business_license_edit_draft
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchantSn != null">
        merchant_sn,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="draft != null">
        draft,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchantSn != null">
        #{merchantSn,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="draft != null">
        #{draft,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update business_license_edit_draft
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantSn != null">
        merchant_sn = #{record.merchantSn,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.draft != null">
        draft = #{record.draft,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update business_license_edit_draft
    set id = #{record.id,jdbcType=BIGINT},
      merchant_sn = #{record.merchantSn,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      draft = #{record.draft,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_license_edit_draft
    set id = #{record.id,jdbcType=BIGINT},
      merchant_sn = #{record.merchantSn,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraft">
    update business_license_edit_draft
    <set>
      <if test="merchantSn != null">
        merchant_sn = #{merchantSn,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="draft != null">
        draft = #{draft,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraft">
    update business_license_edit_draft
    set merchant_sn = #{merchantSn,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      draft = #{draft,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.mc.entity.BusinessLicenseEditDraft">
    update business_license_edit_draft
    set merchant_sn = #{merchantSn,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>