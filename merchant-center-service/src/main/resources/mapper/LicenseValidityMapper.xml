<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.mc.mapper.LicenseValidityMapper">
  <resultMap id="BaseResultMap" type="com.wosai.mc.entity.LicenseValidity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="VARCHAR" property="merchant_id" />
    <result column="diff" jdbcType="VARCHAR" property="diff" />
    <result column="pt" jdbcType="VARCHAR" property="pt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_id, diff, pt
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from license_validity
    where id = #{id,jdbcType=BIGINT}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from license_validity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.mc.entity.LicenseValidity">
    insert into license_validity (id, merchant_id, diff, 
      pt)
    values (#{id,jdbcType=BIGINT}, #{merchant_id,jdbcType=VARCHAR}, #{diff,jdbcType=VARCHAR}, 
      #{pt,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.mc.entity.LicenseValidity">
    insert into license_validity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchant_id != null">
        merchant_id,
      </if>
      <if test="diff != null">
        diff,
      </if>
      <if test="pt != null">
        pt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_id != null">
        #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="diff != null">
        #{diff,jdbcType=VARCHAR},
      </if>
      <if test="pt != null">
        #{pt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.mc.entity.LicenseValidity">
    update license_validity
    <set>
      <if test="merchant_id != null">
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="diff != null">
        diff = #{diff,jdbcType=VARCHAR},
      </if>
      <if test="pt != null">
        pt = #{pt,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.mc.entity.LicenseValidity">
    update license_validity
    set merchant_id = #{merchant_id,jdbcType=VARCHAR},
      diff = #{diff,jdbcType=VARCHAR},
      pt = #{pt,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPt" resultType="com.wosai.mc.entity.LicenseValidity">
    select * from license_validity where pt = #{pt}
  </select>
</mapper>