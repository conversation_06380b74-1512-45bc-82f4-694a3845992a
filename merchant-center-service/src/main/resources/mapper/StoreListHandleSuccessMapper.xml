<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.mc.mapper.StoreListHandleSuccessMapper">
  <resultMap id="BaseResultMap" type="com.wosai.mc.entity.StoreListHandleSuccess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="VARCHAR" property="merchant_id" />
    <result column="store_id" jdbcType="VARCHAR" property="store_id" />
    <result column="is_takeout_active" jdbcType="INTEGER" property="is_takeout_active" />
    <result column="choosed_name" jdbcType="INTEGER" property="choosed_name" />
    <result column="choosed_address" jdbcType="INTEGER" property="choosed_address" />
    <result column="is_merged_name_address" jdbcType="INTEGER" property="is_merged_name_address" />
    <result column="name_address_notice_time" jdbcType="TIMESTAMP" property="name_address_notice_time" />
    <result column="is_contract_name_phone_merged" jdbcType="INTEGER" property="is_contract_name_phone_merged" />
    <result column="contract_name_phone_notice_time" jdbcType="TIMESTAMP" property="contract_name_phone_notice_time" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="mtime" jdbcType="BIGINT" property="mtime" />
    <result column="urge_notice_10days_later" jdbcType="TIMESTAMP" property="urge_notice_10days_later" />
    <result column="urge_notice_20days_later" jdbcType="TIMESTAMP" property="urge_notice_20days_later" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.mc.entity.StoreListHandleSuccess">
    <result column="update_params_name_address" jdbcType="LONGVARCHAR" property="update_params_name_address" />
    <result column="original_name_address" jdbcType="LONGVARCHAR" property="original_name_address" />
    <result column="update_params_contract_name_phone" jdbcType="LONGVARCHAR" property="update_params_contract_name_phone" />
    <result column="original_contract_name_phone" jdbcType="LONGVARCHAR" property="original_contract_name_phone" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_id, store_id, is_takeout_active, choosed_name, choosed_address, is_merged_name_address, 
    name_address_notice_time, is_contract_name_phone_merged, contract_name_phone_notice_time, 
    ctime, mtime, urge_notice_10days_later, urge_notice_20days_later
  </sql>
  <sql id="Blob_Column_List">
    update_params_name_address, original_name_address, update_params_contract_name_phone, 
    original_contract_name_phone
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.wosai.mc.entity.StoreListHandleSuccessExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from store_list_handle_success
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.wosai.mc.entity.StoreListHandleSuccessExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from store_list_handle_success
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from store_list_handle_success
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_list_handle_success
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.mc.entity.StoreListHandleSuccess">
    insert into store_list_handle_success (id, merchant_id, store_id, 
      is_takeout_active, choosed_name, choosed_address, 
      is_merged_name_address, name_address_notice_time, 
      is_contract_name_phone_merged, contract_name_phone_notice_time, 
      ctime, mtime, urge_notice_10days_later, 
      urge_notice_20days_later, update_params_name_address, 
      original_name_address, update_params_contract_name_phone, 
      original_contract_name_phone)
    values (#{id,jdbcType=BIGINT}, #{merchant_id,jdbcType=VARCHAR}, #{store_id,jdbcType=VARCHAR}, 
      #{is_takeout_active,jdbcType=INTEGER}, #{choosed_name,jdbcType=INTEGER}, #{choosed_address,jdbcType=INTEGER}, 
      #{is_merged_name_address,jdbcType=INTEGER}, #{name_address_notice_time,jdbcType=TIMESTAMP}, 
      #{is_contract_name_phone_merged,jdbcType=INTEGER}, #{contract_name_phone_notice_time,jdbcType=TIMESTAMP}, 
      #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT}, #{urge_notice_10days_later,jdbcType=TIMESTAMP}, 
      #{urge_notice_20days_later,jdbcType=TIMESTAMP}, #{update_params_name_address,jdbcType=LONGVARCHAR}, 
      #{original_name_address,jdbcType=LONGVARCHAR}, #{update_params_contract_name_phone,jdbcType=LONGVARCHAR}, 
      #{original_contract_name_phone,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.mc.entity.StoreListHandleSuccess">
    insert into store_list_handle_success
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchant_id != null">
        merchant_id,
      </if>
      <if test="store_id != null">
        store_id,
      </if>
      <if test="is_takeout_active != null">
        is_takeout_active,
      </if>
      <if test="choosed_name != null">
        choosed_name,
      </if>
      <if test="choosed_address != null">
        choosed_address,
      </if>
      <if test="is_merged_name_address != null">
        is_merged_name_address,
      </if>
      <if test="name_address_notice_time != null">
        name_address_notice_time,
      </if>
      <if test="is_contract_name_phone_merged != null">
        is_contract_name_phone_merged,
      </if>
      <if test="contract_name_phone_notice_time != null">
        contract_name_phone_notice_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="urge_notice_10days_later != null">
        urge_notice_10days_later,
      </if>
      <if test="urge_notice_20days_later != null">
        urge_notice_20days_later,
      </if>
      <if test="update_params_name_address != null">
        update_params_name_address,
      </if>
      <if test="original_name_address != null">
        original_name_address,
      </if>
      <if test="update_params_contract_name_phone != null">
        update_params_contract_name_phone,
      </if>
      <if test="original_contract_name_phone != null">
        original_contract_name_phone,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_id != null">
        #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="store_id != null">
        #{store_id,jdbcType=VARCHAR},
      </if>
      <if test="is_takeout_active != null">
        #{is_takeout_active,jdbcType=INTEGER},
      </if>
      <if test="choosed_name != null">
        #{choosed_name,jdbcType=INTEGER},
      </if>
      <if test="choosed_address != null">
        #{choosed_address,jdbcType=INTEGER},
      </if>
      <if test="is_merged_name_address != null">
        #{is_merged_name_address,jdbcType=INTEGER},
      </if>
      <if test="name_address_notice_time != null">
        #{name_address_notice_time,jdbcType=TIMESTAMP},
      </if>
      <if test="is_contract_name_phone_merged != null">
        #{is_contract_name_phone_merged,jdbcType=INTEGER},
      </if>
      <if test="contract_name_phone_notice_time != null">
        #{contract_name_phone_notice_time,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="urge_notice_10days_later != null">
        #{urge_notice_10days_later,jdbcType=TIMESTAMP},
      </if>
      <if test="urge_notice_20days_later != null">
        #{urge_notice_20days_later,jdbcType=TIMESTAMP},
      </if>
      <if test="update_params_name_address != null">
        #{update_params_name_address,jdbcType=LONGVARCHAR},
      </if>
      <if test="original_name_address != null">
        #{original_name_address,jdbcType=LONGVARCHAR},
      </if>
      <if test="update_params_contract_name_phone != null">
        #{update_params_contract_name_phone,jdbcType=LONGVARCHAR},
      </if>
      <if test="original_contract_name_phone != null">
        #{original_contract_name_phone,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.mc.entity.StoreListHandleSuccess">
    update store_list_handle_success
    <set>
      <if test="merchant_id != null">
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="store_id != null">
        store_id = #{store_id,jdbcType=VARCHAR},
      </if>
      <if test="is_takeout_active != null">
        is_takeout_active = #{is_takeout_active,jdbcType=INTEGER},
      </if>
      <if test="choosed_name != null">
        choosed_name = #{choosed_name,jdbcType=INTEGER},
      </if>
      <if test="choosed_address != null">
        choosed_address = #{choosed_address,jdbcType=INTEGER},
      </if>
      <if test="is_merged_name_address != null">
        is_merged_name_address = #{is_merged_name_address,jdbcType=INTEGER},
      </if>
      <if test="name_address_notice_time != null">
        name_address_notice_time = #{name_address_notice_time,jdbcType=TIMESTAMP},
      </if>
      <if test="is_contract_name_phone_merged != null">
        is_contract_name_phone_merged = #{is_contract_name_phone_merged,jdbcType=INTEGER},
      </if>
      <if test="contract_name_phone_notice_time != null">
        contract_name_phone_notice_time = #{contract_name_phone_notice_time,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="urge_notice_10days_later != null">
        urge_notice_10days_later = #{urge_notice_10days_later,jdbcType=TIMESTAMP},
      </if>
      <if test="urge_notice_20days_later != null">
        urge_notice_20days_later = #{urge_notice_20days_later,jdbcType=TIMESTAMP},
      </if>
      <if test="update_params_name_address != null">
        update_params_name_address = #{update_params_name_address,jdbcType=LONGVARCHAR},
      </if>
      <if test="original_name_address != null">
        original_name_address = #{original_name_address,jdbcType=LONGVARCHAR},
      </if>
      <if test="update_params_contract_name_phone != null">
        update_params_contract_name_phone = #{update_params_contract_name_phone,jdbcType=LONGVARCHAR},
      </if>
      <if test="original_contract_name_phone != null">
        original_contract_name_phone = #{original_contract_name_phone,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.mc.entity.StoreListHandleSuccess">
    update store_list_handle_success
    set merchant_id = #{merchant_id,jdbcType=VARCHAR},
      store_id = #{store_id,jdbcType=VARCHAR},
      is_takeout_active = #{is_takeout_active,jdbcType=INTEGER},
      choosed_name = #{choosed_name,jdbcType=INTEGER},
      choosed_address = #{choosed_address,jdbcType=INTEGER},
      is_merged_name_address = #{is_merged_name_address,jdbcType=INTEGER},
      name_address_notice_time = #{name_address_notice_time,jdbcType=TIMESTAMP},
      is_contract_name_phone_merged = #{is_contract_name_phone_merged,jdbcType=INTEGER},
      contract_name_phone_notice_time = #{contract_name_phone_notice_time,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      urge_notice_10days_later = #{urge_notice_10days_later,jdbcType=TIMESTAMP},
      urge_notice_20days_later = #{urge_notice_20days_later,jdbcType=TIMESTAMP},
      update_params_name_address = #{update_params_name_address,jdbcType=LONGVARCHAR},
      original_name_address = #{original_name_address,jdbcType=LONGVARCHAR},
      update_params_contract_name_phone = #{update_params_contract_name_phone,jdbcType=LONGVARCHAR},
      original_contract_name_phone = #{original_contract_name_phone,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.mc.entity.StoreListHandleSuccess">
    update store_list_handle_success
    set merchant_id = #{merchant_id,jdbcType=VARCHAR},
      store_id = #{store_id,jdbcType=VARCHAR},
      is_takeout_active = #{is_takeout_active,jdbcType=INTEGER},
      choosed_name = #{choosed_name,jdbcType=INTEGER},
      choosed_address = #{choosed_address,jdbcType=INTEGER},
      is_merged_name_address = #{is_merged_name_address,jdbcType=INTEGER},
      name_address_notice_time = #{name_address_notice_time,jdbcType=TIMESTAMP},
      is_contract_name_phone_merged = #{is_contract_name_phone_merged,jdbcType=INTEGER},
      contract_name_phone_notice_time = #{contract_name_phone_notice_time,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      urge_notice_10days_later = #{urge_notice_10days_later,jdbcType=TIMESTAMP},
      urge_notice_20days_later = #{urge_notice_20days_later,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>