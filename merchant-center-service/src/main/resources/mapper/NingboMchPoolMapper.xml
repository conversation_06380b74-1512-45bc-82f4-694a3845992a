<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.mc.mapper.NingboMchPoolMapper">
  <resultMap id="BaseResultMap" type="com.wosai.mc.entity.NingboMchPool">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="street_address" jdbcType="VARCHAR" property="street_address" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="latitude" jdbcType="VARCHAR" property="latitude" />
  </resultMap>
  <sql id="Base_Column_List">
    id, province, city, district, street_address, longitude, latitude
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ningbo_mch_pool
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ningbo_mch_pool
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.mc.entity.NingboMchPool">
    insert into ningbo_mch_pool (id, province, city, 
      district, street_address, longitude, 
      latitude)
    values (#{id,jdbcType=BIGINT}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{district,jdbcType=VARCHAR}, #{street_address,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, 
      #{latitude,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.mc.entity.NingboMchPool">
    insert into ningbo_mch_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="district != null">
        district,
      </if>
      <if test="street_address != null">
        street_address,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="street_address != null">
        #{street_address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.mc.entity.NingboMchPool">
    update ningbo_mch_pool
    <set>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        district = #{district,jdbcType=VARCHAR},
      </if>
      <if test="street_address != null">
        street_address = #{street_address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.mc.entity.NingboMchPool">
    update ningbo_mch_pool
    set province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      district = #{district,jdbcType=VARCHAR},
      street_address = #{street_address,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>