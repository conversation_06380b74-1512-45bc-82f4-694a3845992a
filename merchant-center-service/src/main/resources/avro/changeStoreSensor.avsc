{"namespace": "com.wosai.mc.avro", "type": "record", "name": "ChangeStoreSensor", "fields": [{"name": "merchant_id", "type": ["string", "null"]}, {"name": "merchant_sn", "type": ["string", "null"]}, {"name": "store_id", "type": ["string", "null"]}, {"name": "store_sn", "type": ["string", "null"]}, {"name": "mtime", "type": ["long", "null"]}, {"name": "operator", "type": ["string", "null"]}, {"name": "platform", "type": ["string", "null"]}, {"name": "name", "type": ["string", "null"]}, {"name": "industry", "type": ["string", "null"]}, {"name": "status", "type": ["int", "null"]}, {"name": "rank", "type": ["int", "null"]}, {"name": "longitude", "type": ["string", "null"]}, {"name": "latitude", "type": ["string", "null"]}, {"name": "province", "type": ["string", "null"]}, {"name": "city", "type": ["string", "null"]}, {"name": "district", "type": ["string", "null"]}, {"name": "street_address", "type": ["string", "null"]}, {"name": "contact_name", "type": ["string", "null"]}, {"name": "contract_cellphone", "type": ["string", "null"]}, {"name": "contact_phone", "type": ["string", "null"]}, {"name": "contact_email", "type": ["string", "null"]}, {"name": "operation_contents", "type": ["string", "null"]}, {"name": "type", "type": ["int", "null"]}, {"name": "open_account_way", "type": ["int", "null"]}, {"name": "brand_photo", "type": ["boolean", "null"]}, {"name": "indoor_material", "type": ["boolean", "null"]}, {"name": "outdoor_material", "type": ["boolean", "null"]}, {"name": "product_price", "type": ["boolean", "null"]}, {"name": "audit_picture", "type": ["boolean", "null"]}, {"name": "video", "type": ["string", "null"]}, {"name": "business_hour", "type": ["string", "null"]}, {"name": "store_area", "type": ["string", "null"]}, {"name": "room_count", "type": ["string", "null"]}, {"name": "table_count", "type": ["string", "null"]}, {"name": "average_consumption_time", "type": ["string", "null"]}, {"name": "around_type", "type": ["string", "null"]}]}