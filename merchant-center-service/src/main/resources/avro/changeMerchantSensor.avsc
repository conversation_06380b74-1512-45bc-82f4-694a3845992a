{"namespace": "com.wosai.mc.avro", "type": "record", "name": "ChangeMerchantSensor", "fields": [{"name": "merchant_id", "type": ["string", "null"]}, {"name": "merchant_sn", "type": ["string", "null"]}, {"name": "mtime", "type": ["long", "null"]}, {"name": "operator", "type": ["string", "null"]}, {"name": "platform", "type": ["string", "null"]}, {"name": "name", "type": ["string", "null"]}, {"name": "alias", "type": ["string", "null"]}, {"name": "industry", "type": ["string", "null"]}, {"name": "status", "type": ["int", "null"]}, {"name": "rank", "type": ["int", "null"]}, {"name": "withdraw_mode", "type": ["int", "null"]}, {"name": "longitude", "type": ["string", "null"]}, {"name": "latitude", "type": ["string", "null"]}, {"name": "country", "type": ["string", "null"]}, {"name": "province", "type": ["string", "null"]}, {"name": "city", "type": ["string", "null"]}, {"name": "district", "type": ["string", "null"]}, {"name": "street_address", "type": ["string", "null"]}, {"name": "contact_name", "type": ["string", "null"]}, {"name": "contract_cellphone", "type": ["string", "null"]}, {"name": "contact_phone", "type": ["string", "null"]}, {"name": "contact_email", "type": ["string", "null"]}, {"name": "concat_id_card_front_photo", "type": ["boolean", "null"]}, {"name": "legal_person_type", "type": ["int", "null"]}, {"name": "legal_person_register_no", "type": ["string", "null"]}, {"name": "business_license_photo", "type": ["boolean", "null"]}, {"name": "business", "type": ["string", "null"]}, {"name": "currency", "type": ["string", "null"]}, {"name": "owner_name", "type": ["string", "null"]}, {"name": "owner_cellphone", "type": ["string", "null"]}, {"name": "customer_phone", "type": ["string", "null"]}, {"name": "logo", "type": ["string", "null"]}, {"name": "business_name", "type": ["string", "null"]}, {"name": "concat_identity", "type": ["string", "null"]}, {"name": "merchant_type", "type": ["int", "null"]}, {"name": "type", "type": ["int", "null"]}, {"name": "photo", "type": ["boolean", "null"]}, {"name": "number", "type": ["string", "null"]}, {"name": "license_name", "type": ["string", "null"]}, {"name": "validity", "type": ["string", "null"]}, {"name": "address", "type": ["string", "null"]}, {"name": "registered_legal_person_name", "type": ["string", "null"]}, {"name": "letter_of_authorization", "type": ["boolean", "null"]}, {"name": "trade_license", "type": ["boolean", "null"]}, {"name": "legal_person_name", "type": ["string", "null"]}, {"name": "legal_person_id_type", "type": ["int", "null"]}, {"name": "legal_person_id_number", "type": ["string", "null"]}, {"name": "legal_person_id_card_front_photo", "type": ["boolean", "null"]}, {"name": "legal_person_id_card_back_photo", "type": ["boolean", "null"]}, {"name": "legal_person_id_card_address", "type": ["string", "null"]}, {"name": "legal_person_id_card_issuing_authority", "type": ["string", "null"]}]}