spring:
  application:
    name : merchant-center-service
  kafka:
    ali:
      producer:
        bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
        group-id: merchant-center
        registry-servers: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
      consumer:
        bootstrap-servers: aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
        group-id: merchant-center
        registry-servers: http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
    old:
      producer:
        bootstrap-servers: conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092 ,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
        group-id: merchant-center
        registry-servers: http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081
      consumer:
        bootstrap-servers: conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092 ,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
        group-id: merchant-center
        registry-servers: http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081

  datasource:
    url: pk-merchant-center-upay_bank-1083
    driverClassName: com.mysql.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      connection-test-query: select 1 from dual
  redis:
    host: r-bp1pveni08zbz66jld.redis.rds.aliyuncs.com
    port: 6379
    database: 36
    password: CMCIQ0w3EwRa09SE
    timeout: 1000s  # 数据库连接超时时间，2.0 中该参数的类型为Duration，这里在配置的时候需要指明单位
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
      shutdown-timeout: 100 # 关闭超时时间
#port
server:
  port : 8080

jsonrpc:
  core-business: http://app-core-business
  merchant-user: http://merchant-user-service
  merchant-audit: http://merchant-audit-service
  app-backend: http://app-backend-service
  sales-system-poi: http://sales-system-poi
  video-service: http://video-service
  bank-info: http://bank-info-service
  crow-service: http://crow-server.internal.shouqianba.com
  merchant-bank : http://merchant-bank-service
  aop-gateway: http://aop-gateway
  sp-workflow-service: http://sp-workflow-service
  merchant-contract-job: http://merchant-contract-job
  crm-customer-relation: http://crm-customer-relation
  risk-disposal: http://risk-disposal
  merchant-contract-activity: http://merchant-contract-activity
  business-logstash: http://business-logstash
  core-crypto: http://core-crypto
  merchant-business-open: http://merchant-business-open
  shouqianba-tools-service: http://shouqianba-tools-service
  clearance-service: http://clearance-service.internal.shouqianba.com
  merchant-contract-access: http://merchant-contract-access
  trade-manage-service: http://trade-manage-service
  merchant-api: http://merchant
  withdraw-service: http://withdraw-service.internal.shouqianba.com

mybatis:
  mapper-locations: classpath:mapper/*.xml
  config-location: classpath:mybatis-config.xml
  type-handlers-package: com.wosai.mc.config.mybatisConfig


sms:
  url: http://sms-gateway/sms/send
  readTimeout: 5000
  connectTimeout: 5000
  # 验证码过期时间，单位分钟
  expireTime: 30

sensor:
  topic:
    createMerchant: events.upay-core.merchant-center.create-merchant
    createStore: events.upay-core.merchant-center.create-store
    changeMerchant: events.upay-core.merchant-center.change-merchant
    changeStore: events.upay-core.merchant-center.change-store

tag:
  id: e4dd18c2-958b-4e07-9698-cf6ecd292147

# 证件或证照过期的code等
merchant-center:
  dev_code: DH9TBQWFSSBU
license:
  near_expire:
    notice: OG8BYK6DDMNT
    message: UVDH2GP1IUIP
  already_expire:
    notice: N57UUDUQJWFC
    message: Y1XQMQGBHKGY
  invite:
    dev_code: D3ZWFFMUFAEM
    template_code: TUNOPC8EDKGP
    msp_template_code: XTQBJWX47WCE
holder:
  near_expire:
    notice: N6HTJOYSBVRC
    message: MXGVBBELHN3Q
  already_expire:
    notice: U9SJSOC4USGV
    message: QC9QMYZXYXEX
  audit_template_id: 1997

businessopen:
  appid:
    takeaway: 9f119cbf-a761-4be5-a0db-d66ad53993c0
    order: 28cdfd71-8f64-40ce-9b41-3d3ded3c6ef2

#加解密配置
core-crypto:
  access_id: 9c6c8735-bf58-4286-be63-483ff8f91c91
  access_secret: 09a0e62f97e14578a721f789fa3e3cfa
#阿里云oss身份标识
aliyun:
  oss:
    endpoint: http://oss-cn-hangzhou.aliyuncs.com
    group-name: cua
com:
  wosai:
    oss:
      internal: true
#火山
datacenter:
  appid:
    bmerchant: 10000026
    store: 10000036

crm_app_id:
  #支付业务
  pay: 728d74a2-103a-4e67-937f-0baa3216865c
dbb_app_id: 4cf2cb6e-90ca-4c67-bef2-8838c0a6eafb
consumer:
  init: true
merge_store:
  app_code: D3ZWFFMUFAEM
  need_merge_template: TLOX2EL9HF16
  not_over_template: ITTKR8MEXWXZ
  over_template: METGLNCCNUG5
  bot_url: https://open.feishu.cn/open-apis/bot/v2/hook/6ea6029e-e3d7-4ab2-ba32-dcf5acd36ea6

crm:
  indirect:
    customer_relation: 5PPoi00qcj

path:
  app:
    licenceV2: https://upay-info-app.shouqianba.com/license?token=:token
