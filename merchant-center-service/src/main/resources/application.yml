spring:
  application:
    name: merchant-center-service
  kafka:
    ali:
      producer:
        bootstrap-servers: aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
        group-id: merchant-center
        registry-servers: http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
      consumer:
        bootstrap-servers: aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
        group-id: merchant-center
        registry-servers: http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
    old:
      producer:
        bootstrap-servers: **************:9092,**************:9092,**************:9092
        group-id: merchant-center
        registry-servers: http://**************:8081,http://**************:8081,http://**************:8081
      consumer:
        bootstrap-servers: **************:9092,**************:9092,**************:9092
        group-id: merchant-center
        registry-servers: http://**************:8081,http://**************:8081,http://**************:8081
  datasource:
    url: tk-merchant-center-upay_bank-2906
    driverClassName: com.mysql.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 5
      connection-test-query: select 1 from dual
  redis:
    host: r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    database: 4
    password: roFXzHwXPY3RnI%5
    timeout: 1000s  # 数据库连接超时时间，2.0 中该参数的类型为Duration，这里在配置的时候需要指明单位
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
      shutdown-timeout: 100 # 关闭超时时间
server:
  port: 8080

jsonrpc:
  core-business: http://core-business.beta.iwosai.com
  merchant-user: http://merchant-user-service.beta.iwosai.com
  merchant-audit: http://merchant-audit-service.beta.iwosai.com
  app-backend: http://app-backend-service.beta.iwosai.com
  sales-system-poi: http://sales-system-poi.beta.iwosai.com
  video-service: http://video-service.beta.iwosai.com
  bank-info: http://bank-info-service.beta.iwosai.com
  crow-service: http://192.168.101.149:18081
  merchant-bank: http://merchant-bank-service.beta.iwosai.com
  aop-gateway: http://aop-gateway.beta.iwosai.com
  sp-workflow-service: http://sp-workflow-service.beta.iwosai.com
  merchant-contract-job: http://merchant-contract-job.beta.iwosai.com
  crm-customer-relation: http://crm-customer-relation.beta.iwosai.com
  risk-disposal: http://risk-disposal.beta.iwosai.com
  merchant-contract-activity: http://merchant-contract-activity.beta.iwosai.com
  business-logstash: http://business-logstash.beta.iwosai.com
  core-crypto: http://core-crypto.beta.iwosai.com
  merchant-business-open: http://merchant-business-open.beta.iwosai.com
  shouqianba-tools-service: http://shouqianba-tools-service.beta.iwosai.com
  clearance-service: http://clearance-service.beta.iwosai.com
  merchant-contract-access: http://merchant-contract-access.beta.iwosai.com
  trade-manage-service: http://trade-manage-service.beta.iwosai.com
  merchant-api: http://merchant.beta.iwosai.com
  withdraw-service: http://shouqianba-withdraw-service.beta.iwosai.com

mybatis:
  mapper-locations: classpath:mapper/*.xml
  config-location: classpath:mybatis-config.xml
  type-handlers-package: com.wosai.mc.config.mybatisConfig


sms:
  url: http://sms-gateway.beta.iwosai.com/sms/send
  readTimeout: 5000
  connectTimeout: 5000
  # 验证码过期时间，单位分钟
  expireTime: 30

sensor:
  topic:
    createMerchant: events.upay-core.merchant-center.create-merchant
    createStore: events.upay-core.merchant-center.create-store
    changeMerchant: events.upay-core.merchant-center.change-merchant
    changeStore: events.upay-core.merchant-center.change-store

# apollo
app:
  id: merchant-center
  name: merchant-center
  group: cua
  # 使用的 Apollo 的项目（应用）编号,等同于 classPath:/META_INF/app.properties
apollo:
  bootstrap:
    enabled: true
    # will inject 'application' and 'TEST1.apollo' namespaces in bootstrap phase
    namespaces: application

tag:
  id: f85eef13-e897-4f49-b258-ed9c9dbde011

# 证件或证照过期的code等
merchant-center:
  dev_code: DH9TBQWFSSBU
license:
  near_expire:
    notice: OG8BYK6DDMNT
    message: ADAADADADADA
  already_expire:
    notice: N57UUDUQJWFC
    message: Y1XQMQGBHKGY
  invite:
    dev_code: D3ZWFFMUFAEM
    template_code: TUNOPC8EDKGP
    msp_template_code: XTQBJWX47WCE
holder:
  near_expire:
    notice: N6HTJOYSBVRC
    message: MXGVBBELHN3Q
  already_expire:
    notice: U9SJSOC4USGV
    message: QC9QMYZXYXEX
  audit_template_id: 24132
com:
  wosai:
    oss:
      group: cua
      imgBucket: private-wosai-images
      imgBaseUrl: https://private-images.shouqianba.com
      staticBucket: private-wosai-statics
      staticBaseUrl: https://private-resource.shouqianba.com
      internal: false
businessopen:
  appid:
    takeaway: 3765750f-4ad9-4b80-a6ab-c5562d712a38
    order: b5e9ecc5-525c-439a-bdac-fc5b8e4878f4

#加解密配置
core-crypto:
  access_id: 8172747b-00d1-4148-8fcc-c1d1fc3c8d98
  access_secret: b45b50cdd2ad48c2a7b96626f980a523
#阿里云oss身份标识
aliyun:
  oss:
    endpoint: http://oss-cn-hangzhou.aliyuncs.com
    group-name: cua
#火山
datacenter:
  appid:
    bmerchant: 10000038
    store: 10000035

crm_app_id:
  #间联扫码业务
  pay: 6a50e156-7222-41a9-99e1-43d87a0dfc9a
dbb_app_id: cc4c8018-6707-411c-9d54-2ac11acbb9d8
consumer:
  init: false
merge_store:
  app_code: D3ZWFFMUFAEM
  need_merge_template: TLOX2EL9HF16
  not_over_template: ITTKR8MEXWXZ
  over_template: METGLNCCNUG5
  bot_url: https://open.feishu.cn/open-apis/bot/v2/hook/56300c43-072f-47d4-a23f-9d6bc19378a6

crm:
  indirect:
    customer_relation: merchantorg

path:
  app:
    licenceV2: https://upay-info-app.iwosai.com/license?token=:token
