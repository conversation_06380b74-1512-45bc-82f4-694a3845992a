package com.wosai.mc.utils;

import com.wosai.oss.OssUrlEncrypt;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 15:26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OssTest {

    @Autowired
    private OssUrlEncrypt ossUrlEncrypt;

    @Test
    public void test() throws UnsupportedEncodingException {
        String  sourceUrl = "https://private-images.shouqianba.com/33/73a119a6cfd44c84c87dbfb34a6b51d0d8bf41.jpg";
        String finalUrl = ossUrlEncrypt.encryptUrl(sourceUrl);
        System.err.println(finalUrl);
        Assert.assertTrue(finalUrl.contains("security-token"));
    }
}