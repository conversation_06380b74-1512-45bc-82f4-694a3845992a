package com.wosai.mc.utils;

import com.wosai.upay.core.exception.CoreInvalidParameterException;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

class EffectiveTimeUtilTest {

    @Test
    public void checkoutEffectiveTime_EmptyEndDate_ThrowsException() {
        assertThrows(CoreInvalidParameterException.class, () -> {
            EffectiveTimeUtil.checkoutEffectiveTime("");
        });
    }

    @Test
    public void checkoutEffectiveTime_ShortEndDate_ThrowsException() {
        assertThrows(CoreInvalidParameterException.class, () -> {
            EffectiveTimeUtil.checkoutEffectiveTime("202301");
        });
    }

    @Test
    public void checkoutEffectiveTime_WrongDate_ThrowsException() {
        assertThrows(CoreInvalidParameterException.class, () -> {
            EffectiveTimeUtil.checkoutEffectiveTime("20500230");
        });
    }

    @Test
    public void checkoutEffectiveTime_InvalidDate_ThrowsException() {
        assertThrows(CoreInvalidParameterException.class, () -> {
            EffectiveTimeUtil.checkoutEffectiveTime("20230230");
        });
    }

    @Test
    public void checkoutEffectiveTime_Within7Days_ThrowsException() {
        // 假设当前日期是2023-01-01
        assertThrows(CoreInvalidParameterException.class, () -> {
            EffectiveTimeUtil.checkoutEffectiveTime("20230108");
        });
    }

    @Test
    public void checkoutEffectiveTime_ValidDateNoException() {
        LocalDate localDate = LocalDate.now().plusYears(1);
        // 假设当前日期是2023-01-01
        assertDoesNotThrow(() -> {
            EffectiveTimeUtil.checkoutEffectiveTime(localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        });
    }

    @Test
    public void testSameMonthAndDayDifferentYear() {
        assertTrue(EffectiveTimeUtil.isDateValid("20230101", "20240101"));
    }

    @Test
    public void testLeapDayStartAndEndFebruary28() {
        assertTrue(EffectiveTimeUtil.isDateValid("20200229", "20210228"));
    }

    @Test
    public void testLeapDayStartAndEndMarch2() {
        assertTrue(EffectiveTimeUtil.isDateValid("20200229", "20210302"));
    }

    @Test
    public void testLeapDayStartAndEndMarch3() {
        assertFalse(EffectiveTimeUtil.isDateValid("20200229", "20210303"));
    }

    @Test
    public void testLeapDayEndAndStartFebruary28() {
        assertTrue(EffectiveTimeUtil.isDateValid("20210228", "20200229"));
    }

    @Test
    public void testBothLeapDays() {
        assertTrue(EffectiveTimeUtil.isDateValid("20200229", "20240229"));
    }

    @Test
    public void testDifferentMonthSameDay() {
        assertFalse(EffectiveTimeUtil.isDateValid("20230405", "20240406"));
    }

    @Test
    public void testLeapDayStartAndFebruary27() {
        assertTrue(EffectiveTimeUtil.isDateValid("20240229", "20340227"));
    }

    @Test
    public void testNonLeapDaySameMonthAndDay() {
        assertTrue(EffectiveTimeUtil.isDateValid("20210228", "20220228"));
    }

    @Test
    public void testLeapDayStartAndMarch1() {
        assertTrue(EffectiveTimeUtil.isDateValid("20200229", "20210301"));
    }

    @Test
    public void testLeapDayStartAndApril1() {
        assertFalse(EffectiveTimeUtil.isDateValid("20200229", "20210401"));
    }

}