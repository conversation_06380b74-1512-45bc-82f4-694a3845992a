package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.wosai.app.service.VideoService;
import com.wosai.mc.BaseTest;
import com.wosai.mc.biz.SensorSendBiz;
import com.wosai.mc.biz.StoreExtBiz;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.req.StoreExtReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import org.junit.*;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

/**
 * StoreExtServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Aug 31, 2020</pre>
 */
public class StoreExtServiceImplTest extends BaseTest {


    @Autowired

    StoreExtService storeExtService;

    @MockBean
    StoreExtBiz storeExtBiz;

    @BeforeClass
    public static void beforeClass() throws Exception {
    }


    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    @AfterClass
    public static void afterClass() throws Exception {
    }

    /**
     * Method: updateStoreExt(StoreExtReq storeExtReq)
     */
    @Test
    public void testUpdateStoreExt() throws Exception {
        StoreExtReq storeExtReq = new StoreExtReq();
        storeExtReq.setStoreId("d7a161f6-3dd9-42b9-8fb9-dc58d0d738ef4");
        storeExtReq.setAroundType("aroundTypetest");
        Assert.assertEquals(1, storeExtService.updateStoreExt(storeExtReq, "store_ext"));
    }

    /**
     * Method: findStoreExt(String id, String devCode)
     */
    @Test
    public void testFindStoreExt() throws Exception {
        Mockito.doReturn(new StoreExtInfo().setId(3l)).when(storeExtBiz).findStoreExt("3","store_ext");
        StoreExtInfo store_ext = storeExtService.findStoreExt("3", "store_ext");
        Assert.assertNotNull(store_ext);
    }

    @Test
    public void testFindStoreExtByStoreId() throws Exception {
        Mockito.doReturn(new StoreExtInfo().setStoreId("1ae18872-bdf9-4598-8fbb-b78a6ebd243c")).when(storeExtBiz).findStoreExtByStoreId("1ae18872-bdf9-4598-8fbb-b78a6ebd243c", "store_ext");
        StoreExtInfo store_ext = storeExtService.findStoreExtByStoreId("1ae18872-bdf9-4598-8fbb-b78a6ebd243c", "store_ext");
        Assert.assertNotNull(store_ext);
    }

    @Test
    public void testFindStoreExtAndPicturesByStoreId() throws Exception {
//        StotreExtInfoAndPictures stotreExtInfoAndPictures = storeExtService.findStoreExtAndPicturesByStoreId("ccdba07e-b4f4-483a-9e66-98c54a40e170", "store");
        Mockito.doReturn(new StotreExtInfoAndPictures().setStoreId("1ae18872-bdf9-4598-8fbb-b78a6ebd243c")).when(storeExtBiz).findStoreExtAndPicturesByStoreId("1ae18872-bdf9-4598-8fbb-b78a6ebd243c", null);
        StotreExtInfoAndPictures stotreExtInfoAndPictures = storeExtService.findStoreExtAndPicturesByStoreId("1ae18872-bdf9-4598-8fbb-b78a6ebd243c", null);

        Assert.assertNotNull(stotreExtInfoAndPictures);
    }


}
