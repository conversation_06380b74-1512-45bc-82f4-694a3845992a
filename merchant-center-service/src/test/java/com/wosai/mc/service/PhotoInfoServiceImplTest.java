package com.wosai.mc.service;

import com.wosai.mc.BaseTest;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.utils.MyBeanUtil;
import org.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.UUID;

/**
 * PhotoInfoServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Aug 31, 2020</pre>
 */
public class PhotoInfoServiceImplTest extends BaseTest {

    @Autowired
    PhotoInfoService photoInfoService;

    @Autowired
    private com.wosai.upay.core.service.PhotoInfoService coreBPhotoInfoService;

    @Test
    public void getPhotoInfoByIdAndDevCode() {
        PhotoInfo photoInfo1 = null;
        try {
            // 创建的照片是没有devCode的，两个查询结果一样
            photoInfo1 = new PhotoInfo().setId(UUID.randomUUID().toString()).setUrl("www.baidu.com").setDev_code(null);
            Map photo = MyBeanUtil.toMap(photoInfo1);
            coreBPhotoInfoService.createPhotoinfo(photo);

            PhotoInfo photoInfo01 = photoInfoService.getPhotoInfoByIdAndDevcode(photoInfo1.getId(), null);
            PhotoInfo photoInfo02 = photoInfoService.getPhotoInfoByIdAndDevcode(photoInfo1.getId(), "abc");
            Assert.assertEquals(photoInfo01.getUrl(), photoInfo02.getUrl());

            // 创建一个有dev
            PhotoInfo result = photoInfoService.getPhotoInfoByIdAndDevcode("notExist", null);
            Assert.assertNull(result);

        } finally {
            if (photoInfo1 != null) {
                coreBPhotoInfoService.deletePhotoinfo(photoInfo1.getId());
            }
        }
    }

    /**
     * 传入一个id和dev_code不存在的照片，需要去创建了
     */
    @Test
    public void updatePhotoInfo01() {
        PhotoInfo photoInfo1 = null;
        String newPhotoInfoId = null;
        try {
            // 创建一个dev_code为null的照片
            photoInfo1 = new PhotoInfo().setId(UUID.randomUUID().toString()).setUrl("www.baidu.com").setDev_code(null);
            Map photo = MyBeanUtil.toMap(photoInfo1);
            coreBPhotoInfoService.createPhotoinfo(photo);

            // 传入一个dev_code不为null的，查不到会去创建新照片
            PhotoInfo photoInfo = new PhotoInfo().setId(photoInfo1.getId()).setUrl("www.shouqianba.com").setDev_code("devCode");
            newPhotoInfoId = photoInfoService.updatePhotoInfo(photoInfo);
            PhotoInfo result = photoInfoService.getPhotoInfoByIdAndDevcode(newPhotoInfoId, photoInfo.getDev_code());
            Assert.assertEquals(photoInfo.getUrl(), result.getUrl());

            // 更新result的照片 dev_code对应的照片数据存在，不会创建新的
            result.setUrl("www.abc.com");
            String updatePhotoInfoId = photoInfoService.updatePhotoInfo(result);
            Assert.assertEquals(result.getId(), updatePhotoInfoId);

        } finally {
            if (photoInfo1 != null) {
                coreBPhotoInfoService.deletePhotoinfo(photoInfo1.getId());
            }
            if (newPhotoInfoId != null) {
                coreBPhotoInfoService.deletePhotoinfo(newPhotoInfoId);
            }
        }

    }




} 
