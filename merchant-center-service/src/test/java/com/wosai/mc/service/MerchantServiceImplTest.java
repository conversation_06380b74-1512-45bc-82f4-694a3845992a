package com.wosai.mc.service;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.dto.multi.resp.NaturalPersonResp;
import com.wosai.app.service.MultiMerchantService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.mc.BaseTest;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.biz.*;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.req.*;
import com.wosai.mc.model.resp.BindAccountResp;
import com.wosai.mc.model.resp.CreateMerchantResp;
import com.wosai.mc.model.resp.MerchantAndUcUserInfo;
import com.wosai.mc.remote.IMerchantService;
import com.wosai.mc.utils.SmsBiz;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.PhotoInfoService;
import com.wosai.upay.core.service.StoreExtService;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;

/**
 * 全部mock吧，在biz里面再去创建
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Aug 18, 2020</pre>
 */
public class MerchantServiceImplTest extends BaseTest {

    @InjectMocks
    private MerchantServiceImpl merchantService;

    @Mock
    private com.wosai.upay.core.service.MerchantService coreBmerchantService;

    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Mock
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Mock
    private UcUserAccountService ucUserAccountService;

    @Mock
    private SmsBiz sendSms;

    @Mock
    private McPreBiz mcPreBiz;

    @Mock
    private com.wosai.upay.core.service.StoreService coreBstoreService;

    @Mock
    private MerchantAuditService auditService;

    @Mock
    private TagIngestService tagIngestService;

    @Mock
    private StoreService mcStoreService;

    @Mock
    private IMerchantService iMerchantService;

    @Mock
    private com.wosai.upay.core.service.McPreService corBmcPreService;

    @Mock
    private PhotoInfoService photoInfoService;

    @Mock
    private StoreExtService storeExtService;

    @Mock
    private com.wosai.upay.core.service.StoreService storeService;

    @Mock
    private MultiMerchantService multiMerchantService;

    @Mock
    private SensorSendBiz sensorSendBiz;

    @Mock
    private MerchantBiz merchantBiz;

    @Mock
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;

    @Mock
    private MerchantUserBiz merchantUserBiz;

    @Mock
    private StoreBiz storeBiz;

    @Mock
    private CommonApolloConfigs commonApolloConfigs;

    /**
     * 成功创建 新用户
     */
    @Test
    public void createMerchantComplete01() throws Exception {
        MerchantComplete merchantComplete = new MerchantComplete();
        merchantComplete.setLicense(new CreateMerchantBusinessLicenseReq().setMerchantId("merchantId"));
        merchantComplete.setMerchant(new CreateMerchantReq().setName("name").setLongitude("123.123").setLatitude("40.123"));
        merchantComplete.setAccount(new AccountReq().setIdentityType(1).setIdentifier("***********"));
        Mockito.doReturn(new MerchantInfo().setId("merchantId")).when(merchantBiz).createMerchant(merchantComplete.getMerchant());
        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setUc_user_id("ucUserId"))).when(merchantUserServiceV2).createMerchantUserNoCheckBlackList(any());
        CreateMerchantResp result = merchantService.createMerchantComplete(merchantComplete);
        Assert.assertEquals("merchantId", result.getMerchantId());
    }

    /**
     * 成功创建 已有用户
     */
    @Test
    public void createMerchantComplete02() throws Exception {
        MerchantComplete merchantComplete = new MerchantComplete();
        merchantComplete.setLicense(new CreateMerchantBusinessLicenseReq().setMerchantId("merchantId"));
        merchantComplete.setMerchant(new CreateMerchantReq().setName("name").setLongitude("123.123").setLatitude("40.123"));
        merchantComplete.setAccount(new AccountReq().setIdentityType(1).setIdentifier("***********").setUcUserId("ucUserId"));

        Mockito.doReturn(new MerchantInfo().setId("merchantId")).when(merchantBiz).createMerchant(merchantComplete.getMerchant());
        Mockito.doReturn(new MerchantBusinessLicenseInfo()).when(merchantBusinessLicenseService).createMerchantBusinessLicense(any());
        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setUc_user_id("ucUserId"))).when(merchantUserServiceV2).createMerchantUserSimple(any());
        CreateMerchantResp result = merchantService.createMerchantComplete(merchantComplete);
        Assert.assertEquals("merchantId", result.getMerchantId());
    }

//    /**
//     * 创建商户失败的异常
//     */
//    @Test
//    public void createMerchantCompleteException() {
//        MerchantComplete merchantComplete = new MerchantComplete();
//        merchantComplete.setLicense(new CreateMerchantBusinessLicenseReq().setMerchantId("merchantId").setLegalPersonIdNumber("123456199001011234"));
//        merchantComplete.setMerchant(new CreateMerchantReq().setName("name").setLongitude("123.123").setLatitude("40.123"));
//        merchantComplete.setAccount(new AccountReq().setIdentityType(1).setIdentifier("***********").setUcUserId("ucUserId"));
//
//        Mockito.doReturn(new MerchantInfo().setId("merchantId")).when(merchantBiz).createMerchant(merchantComplete.getMerchant());
//        Mockito.doReturn(new MerchantBusinessLicenseInfo()).when(merchantBusinessLicenseService).createMerchantBusinessLicense(any());
//        Mockito.doReturn(null).when(merchantUserBiz).createSuperAdmin(any(), any(), any());
//        Assertions.assertThrows(Exception.class, () -> merchantService.createMerchantComplete(merchantComplete));
//    }

    @Test
    public void createMerchantAndStore() {
        CreateMerchantAndStoreReq createMerchantAndStoreReq = new CreateMerchantAndStoreReq();
        createMerchantAndStoreReq.setLicense(new CreateMerchantBusinessLicenseReq().setMerchantId("merchantId"));
        createMerchantAndStoreReq.setMerchant(new CreateMerchantReq().setName("name").setLongitude("123.123").setLatitude("40.123"));
        createMerchantAndStoreReq.setAccount(new AccountReq().setIdentityType(1).setIdentifier("***********"));
        CreateStoreReq createStoreReq = new CreateStoreReq();
        createStoreReq.setLatitude("111.111").setLongitude("111.111");
        PhotoInfo photoInfo = new PhotoInfo().setUrl("http://www.baidu.com");
        createStoreReq.setAuditPicture(photoInfo).setBrandPhoto(photoInfo).setIndoorMaterialPhoto(photoInfo).setOutdoorMaterialPhoto(photoInfo)
                .setOtherPhoto(Arrays.asList(photoInfo)).setProductPrice(photoInfo);
        createMerchantAndStoreReq.setStore(createStoreReq);

        Mockito.doReturn(new MerchantInfo().setId("merchantId")).when(merchantBiz).createMerchant(createMerchantAndStoreReq.getMerchant());
        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setUc_user_id("ucUserId"))).when(merchantUserServiceV2).createMerchantUserNoCheckBlackList(any());
        Mockito.doReturn(new HashMap<>()).when(coreBstoreService).createStoreForMerchantCenter(any());
        CreateMerchantResp result = merchantService.createMerchantAndStore(createMerchantAndStoreReq);
        Assert.assertEquals("merchantId", result.getMerchantId());
    }

//    @Test
//    public void createMerchantAndStoreException() {
//        CreateMerchantAndStoreReq createMerchantAndStoreReq = new CreateMerchantAndStoreReq();
//        createMerchantAndStoreReq.setLicense(new CreateMerchantBusinessLicenseReq().setMerchantId("merchantId"));
//        createMerchantAndStoreReq.setMerchant(new CreateMerchantReq().setName("name").setLongitude("123.123").setLatitude("40.123"));
//        createMerchantAndStoreReq.setAccount(new AccountReq().setIdentityType(1).setIdentifier("***********"));
//        CreateStoreReq createStoreReq = new CreateStoreReq();
//        createStoreReq.setLatitude("111.111").setLongitude("111.111");
//        PhotoInfo photoInfo = new PhotoInfo().setUrl("http://www.baidu.com");
//        createStoreReq.setAuditPicture(photoInfo).setBrandPhoto(photoInfo).setIndoorMaterialPhoto(photoInfo).setOutdoorMaterialPhoto(photoInfo)
//                .setOtherPhoto(Arrays.asList(photoInfo)).setProductPrice(photoInfo);
//        createMerchantAndStoreReq.setStore(createStoreReq);
//
//        Mockito.doReturn(new MerchantInfo().setId("merchantId")).when(merchantBiz).createMerchant(createMerchantAndStoreReq.getMerchant());
//        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setUc_user_id("ucUserId"))).when(merchantUserServiceV2).createMerchantUserNoCheckBlackList(any());
//        Mockito.doReturn(new MerchantBusinessLicenseInfo()).when(merchantBusinessLicenseService).createMerchantBusinessLicense(any());
//        // 创建门店失败，制造异常
//        Mockito.doReturn(null).when(storeBiz).createStore(any());
//        Assertions.assertThrows(Exception.class, () -> merchantService.createMerchantAndStore(createMerchantAndStoreReq));
//
//    }

    @Test
    public void updateMerchantComplete() {
        UpdateMerchantComplete merchantComplete = new UpdateMerchantComplete();
        merchantComplete.setMerchant(new UpdateMerchantReq().setId("merchantId"));
        merchantComplete.setLicense(new UpdateMerchantBusinessLicenseReq().setMerchantId("merchantId").setType(2));
        Mockito.doReturn(new HashMap<>()).when(coreBmerchantService).getMerchant("merchantId");
        CreateMerchantResp result = merchantService.updateMerchantComplete(merchantComplete);
        Assert.assertNotNull(result);
    }

    @Test
    public void updateMerchantAndStore() {
        UpdateMerchantAndStoreReq updateMerchantAndStoreReq = new UpdateMerchantAndStoreReq();
        updateMerchantAndStoreReq.setMerchant(new UpdateMerchantReq().setId("merchantId"));
        updateMerchantAndStoreReq.setLicense(new UpdateMerchantBusinessLicenseReq().setMerchantId("merchantId").setType(2));

        Mockito.doReturn(new HashMap<>()).when(coreBmerchantService).getMerchant("merchantId");
        CreateMerchantResp result = merchantService.updateMerchantAndStore(updateMerchantAndStoreReq);
        Assert.assertNotNull(result);
    }

    @Test
    public void createMerchant() {
        CreateMerchantReq createMerchantReq = new CreateMerchantReq();
        createMerchantReq.setId("merchantId").setLongitude("123.123").setLatitude("40.123");
        Mockito.doReturn(new MerchantInfo().setId("merchantId")).when(merchantBiz).createMerchant(createMerchantReq);
        MerchantInfo result = merchantService.createMerchant(createMerchantReq);
        Assert.assertNotNull(result);
    }

    @Test
    public void updateMerchant() {
        UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq();
        updateMerchantReq.setId("merchantId").setLongitude("123.123").setLatitude("40.123");
        int result = merchantService.updateMerchant(updateMerchantReq, "devCode");
        Assert.assertEquals(1, result);
    }

    @Test
    public void getMerchantById() {
        MerchantInfo result = merchantService.getMerchantById("merchantId", "devCode");
        Assert.assertNull(result);
    }

    @Test
    public void getLatestMerchantById() {
        Map merchant = new HashMap<>();
        merchant.put("id", "merchantId");
        Mockito.doReturn(merchant).when(coreBmerchantService).getMerchant("merchantId");
        Mockito.doReturn(merchant).when(mcPreBiz).mergeMcPre("merchantId", merchant, TableNameEnum.MERCHANT.getTableName());
        MerchantInfo merchantId = merchantService.getLatestMerchantById("merchantId");
        Assert.assertEquals("merchantId", merchantId.getId());
    }

    @Test
    public void getMerchantBySn() {
        MerchantInfo result = merchantService.getMerchantBySn("merchantSn", "devCode");
        Assert.assertNull(result);
    }

    @Test
    public void deleteMerchantByMerchantId() {
        Mockito.doReturn(1).when(coreBmerchantService).deleteMerchantByMerchantId("merchantId");
        int result = merchantService.deleteMerchantByMerchantId("merchantId");
        Assert.assertEquals(1, result);
    }

    @Test
    public void moveStoreImageByMerchantId() {
        //先构建数据
        Map auditExtInfo = new HashMap();
        auditExtInfo.put("audit_photo","audit_url");
        auditExtInfo.put("extra", (JSON.parseObject("{\n" +
                "            \"photo_poi_info\": {\n" +
                "                \"outdoor_material_photo_poi\": {\n" +
                "                    \"photo\": \"url\",\n" +
                "                    \"province\": \"江苏省\",\n" +
                "                    \"city\": \"苏州市\",\n" +
                "                    \"district\": \"苏州工业园区\",\n" +
                "                    \"address\": \"江苏省苏州市苏州工业园区苏州工业园区直属镇科技产业园\",\n" +
                "                    \"user_id\": \"87b07982-d15c-4971-a913-78b974edef6e\"\n" +
                "                },\n" +
                "                \"indoor_material_photo_poi\": {\n" +
                "                    \"photo\": \"url\",\n" +
                "                    \"province\": \"江苏省\",\n" +
                "                    \"city\": \"苏州市\",\n" +
                "                    \"district\": \"苏州工业园区\",\n" +
                "                    \"address\": \"江苏省苏州市苏州工业园区苏州工业园区直属镇科技产业园\",\n" +
                "                    \"user_id\": \"87b07982-d15c-4971-a913-78b974edef6e\"\n" +
                "                },\n" +
                "                \"brand_photo_poi\": {\n" +
                "                    \"photo\": \"url\",\n" +
                "                    \"province\": \"江苏省\",\n" +
                "                    \"city\": \"苏州市\",\n" +
                "                    \"district\": \"苏州工业园区\",\n" +
                "                    \"address\": \"江苏省苏州市苏州工业园区苏州工业园区直属镇科技产业园\",\n" +
                "                    \"user_id\": \"87b07982-d15c-4971-a913-78b974edef6e\"\n" +
                "                },\n" +
                "\n" +
                "                \"product_price_poi\": {\n" +
                "                    \"photo\": \"url\",\n" +
                "                    \"province\": \"江苏省\",\n" +
                "                    \"city\": \"苏州市\",\n" +
                "                    \"district\": \"苏州工业园区\",\n" +
                "                    \"address\": \"江苏省苏州市苏州工业园区苏州工业园区直属镇科技产业园\",\n" +
                "                    \"user_id\": \"87b07982-d15c-4971-a913-78b974edef6e\"\n" +
                "                },\n" +
                "\"other_photo\":[\n" +
                "                    {\n" +
                "                    \"photo\": \"url\",\n" +
                "                    \"province\": \"江苏省\",\n" +
                "                    \"city\": \"苏州市\",\n" +
                "                    \"district\": \"苏州工业园区\",\n" +
                "                    \"address\": \"江苏省苏州市苏州工业园区苏州工业园区直属镇科技产业园\",\n" +
                "                    \"user_id\": \"87b07982-d15c-4971-a913-78b974edef6e\"\n" +
                "                }\n" +
                "]\n" +
                "            }\n" +
                "        }", Map.class)));
        Map map = new HashMap(1);
        map.put("id", "storeId");
        ListResult listResult = new ListResult(1, Arrays.asList(map));
        Mockito.doReturn(listResult).when(coreBstoreService).getStoreListByMerchantId(any(), any(), any());
        Mockito.doReturn(auditExtInfo).when(auditService).getAuditByMerchantId("merchantId");
        int result = merchantService.moveStoreImageByMerchantId("merchantId");
        Assert.assertEquals(result, 1);
    }


    @Test
    public void copyMcInfoToMerchantByMerchantId() {
        Map map = new HashMap();
        map.put("id", "merchantId");
        map.put("name", "单元测试");
        map.put("mcId",1);
        Mockito.doReturn(map).when(mcPreBiz).getMcPreDataWithMcId("merchantId", "devCode", TableNameEnum.MERCHANT.getTableName());
        Mockito.doReturn(Arrays.asList("id", "sn")).when(commonApolloConfigs).getMerchantFieldFilter();
        int result = merchantService.copyMcInfoToMerchantByMerchantId("merchantId", "devCode");
        Assert.assertEquals(1, result);
    }

    @Test
    public void copyMcInfoToOriginalByMerchantId() {
        int result = merchantService.copyMcInfoToOriginalByMerchantId("merchantId", "devCode");
        Assert.assertEquals(1, result);
    }

    @Test
    public void verifyMerchant() {
        Map merchant = new HashMap<>();
        merchant.put("id", "merchantId");
        Mockito.doReturn(merchant).when(coreBmerchantService).getMerchant("merchantId");
        Mockito.doReturn(merchant).when(mcPreBiz).mergeMcPre("merchantId", merchant, TableNameEnum.MERCHANT.getTableName());
        VerifyMerchantReq merchantReq = new VerifyMerchantReq();
        merchantReq.setMerchantId("merchantId").setVerifyStatus("2").setVerifyParams(Arrays.asList("name", "address"));
        int result = merchantService.verifyMerchant(merchantReq);
        Assert.assertEquals(1, result);
    }

    @Test
    public void getMerchantAndUserInfo() {
        // 1 没有查询到营业执照
        GetMerchantAndLicenseReq req = new GetMerchantAndLicenseReq().setNumber("9200000000001");
        Mockito.doReturn(null).when(merchantBusinessLicenseBiz).getLicenseByNumberOrLegalPersonIdNumber(any());
        List<MerchantAndUcUserInfo> result = merchantService.getMerchantAndUserInfo(req);
        Assert.assertTrue(result.isEmpty());

        // 2 没有查询到正常的商户
        Map license1 = new HashMap();
        license1.put("merchant_id", "id1");
        Map license2 = new HashMap();
        license2.put("merchant_id", "id2");
        List<Map> licenses = Arrays.asList(license1, license2);
        Mockito.doReturn(licenses).when(merchantBusinessLicenseBiz).getLicenseByNumberOrLegalPersonIdNumber(any());
        Mockito.doReturn(new ListResult()).when(coreBmerchantService).findMerchants(any(), any());
        result = merchantService.getMerchantAndUserInfo(req);
        Assert.assertTrue(result.isEmpty());

        // 3 没有老板账号
        Map merchant1 = new HashMap();
        merchant1.put("id", "id1");
        Map merchant2 = new HashMap();
        merchant2.put("id", "id2");
        List<Map> merchants = Arrays.asList(merchant1, merchant2);
        Mockito.doReturn(new ListResult(2, merchants)).when(coreBmerchantService).findMerchants(any(), any());
        Mockito.doReturn(null).when(merchantUserServiceV2).getMerchantUser(any());
        result = merchantService.getMerchantAndUserInfo(req);
        Assert.assertTrue(result.isEmpty());

        // 4 有老板账号
        UcMerchantUserInfo user1 = new UcMerchantUserInfo();
        user1.setMerchant_id("id1").setUcUserInfo(new UcUserInfo());
        UcMerchantUserInfo user2 = new UcMerchantUserInfo();
        user2.setMerchant_id("id2").setUcUserInfo(new UcUserInfo());
        List<UcMerchantUserInfo> merchantUsers = Arrays.asList(user1, user2);
        Mockito.doReturn(merchantUsers).when(merchantUserServiceV2).getMerchantUser(any());
        result = merchantService.getMerchantAndUserInfo(req);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void getBindAccountByIdentity() {
        String identity = "123456199001011234";
        // 一直能查到一个实名的
        Map<String, BindAccountResp> authResult = new HashMap<>();
        BindAccountResp bindAccountResp = new BindAccountResp();
        bindAccountResp.setCellphone("**********");
        bindAccountResp.setUc_user_id("ucUserId1");
        authResult.put("ucUserId1", bindAccountResp);
        Mockito.doReturn(authResult).when(merchantUserBiz).getAuthBindAccountByIdentity(identity);

        // 1 没有查询到营业执照
        Mockito.doReturn(null).when(merchantBusinessLicenseBiz).getLicenseByNumberOrLegalPersonIdNumber(any());
        List<BindAccountResp> result = merchantService.getBindAccountByIdentity(identity);
        Assert.assertEquals(1, result.size());

        // 2 查到了营业执照，但是有一个是组织类型的
        Map license1 = new HashMap();
        license1.put("merchant_id", "id1");
        license1.put("type", 1);
        Map license2 = new HashMap();
        license2.put("merchant_id", "id2");
        license2.put("type", 11);
        List<Map> licenses = Arrays.asList(license1, license2);
        Mockito.doReturn(licenses).when(merchantBusinessLicenseBiz).getLicenseByNumberOrLegalPersonIdNumber(any());
        result = merchantService.getBindAccountByIdentity(identity);
        Assert.assertEquals(1, result.size());

        // 3 查到了营业执照，并且都是小微或个体类型，但是没有查到正常的商户
        license2.put("type", 0);
        Mockito.doReturn(new ListResult()).when(coreBmerchantService).findMerchants(any(), any());
        result = merchantService.getBindAccountByIdentity(identity);
        Assert.assertEquals(1, result.size());

        // 4 没有查到老板账号
        Map merchant1 = new HashMap();
        merchant1.put("id", "id1");
        Map merchant2 = new HashMap();
        merchant2.put("id", "id2");
        List<Map> merchants = Arrays.asList(merchant1, merchant2);
        Mockito.doReturn(new ListResult(2, merchants)).when(coreBmerchantService).findMerchants(any(), any());
        Mockito.doReturn(null).when(merchantUserServiceV2).getMerchantUser(any());
        result = merchantService.getBindAccountByIdentity(identity);
        Assert.assertEquals(1, result.size());

        // 4 有老板账号,ucUserId1这个用户又实名了又是老板
        UcMerchantUserInfo user1 = new UcMerchantUserInfo();
        user1.setMerchant_id("id1").setUcUserInfo(new UcUserInfo().setUc_user_id("ucUserId1"));
        UcMerchantUserInfo user2 = new UcMerchantUserInfo();
        user2.setMerchant_id("id2").setUcUserInfo(new UcUserInfo().setUc_user_id("ucUserId2"));
        List<UcMerchantUserInfo> merchantUsers = Arrays.asList(user1, user2);
        Mockito.doReturn(merchantUsers).when(merchantUserServiceV2).getMerchantUser(any());
        result = merchantService.getBindAccountByIdentity(identity);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void getMerchantInfoByCellphone() {
        String cellphone = "***********";
        List<UcMerchantUserSimpleInfo> list = new ArrayList<>();
        // 没有查到商户用户
        Mockito.doReturn(list).when(merchantUserServiceV2).getSimpleInfoByCellphone(cellphone);
        List<MerchantInfo> result = merchantService.getMerchantInfoByCellphone(cellphone);
        Assert.assertEquals(0, result.size());

        // 查到的商户用户不是超级管理员
        list.add(new UcMerchantUserSimpleInfo().setRole("admin"));
        result = merchantService.getMerchantInfoByCellphone(cellphone);
        Assert.assertEquals(0, result.size());

        list.add(new UcMerchantUserSimpleInfo().setRole("super_admin").setMerchant_id("merchantId"));
        Mockito.doReturn(new MerchantInfo()).when(merchantBiz).getMerchantInfoById("merchantId", null);
        result = merchantService.getMerchantInfoByCellphone(cellphone);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void allowBindAccount() {
        // 1 账号已经实名，而且证件号和实名信息的证件号一致 允许绑定
        String cellphone = "***********";
        String identity = "123456199001011234";
        Mockito.doReturn(new NaturalPersonResp().setAuth_status(1).setIdentity_no(identity))
                .when(multiMerchantService).getRealNameInfoByCellphone(any());
        boolean result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertTrue(result);

        // 2  账号未实名，或者没有实名信息
        Mockito.doReturn(null).when(multiMerchantService).getRealNameInfoByCellphone(any());

        // 2.1 手机号没有查到商户用户 允许绑定
        List<UcMerchantUserSimpleInfo> list = new ArrayList<>();
        Mockito.doReturn(list).when(merchantUserServiceV2).getSimpleInfoByCellphone(cellphone);
        result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertTrue(result);

        // 2.2 手机号查到了商户用户，但是没有超级管理员 允许绑定
        list.add(new UcMerchantUserSimpleInfo().setRole("admin").setMerchant_id("adminId"));
        result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertTrue(result);

        // 2.3 手机号查到了商户用户，有超级管理员，但是没有主体信息  允许绑定
        list.add(new UcMerchantUserSimpleInfo().setRole("super_admin").setMerchant_id("superAdminId"));
        result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertTrue(result);

        // 2.4 手机号查到了商户用户，有超级管理员，有主体信息 但是类型是组织类型 不允许绑定
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setType(11).setLegal_person_id_number("123")).when(merchantBusinessLicenseService).getLatestMerchantBusinessLicenseByMerchantId("superAdminId");
        result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertFalse(result);

        // 2.5 手机号查到了商户用户，有超级管理员，有主体信息 但是主体信息不一致 不允许绑定
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setType(1).setLegal_person_id_number("123")).when(merchantBusinessLicenseService).getLatestMerchantBusinessLicenseByMerchantId("superAdminId");
        result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertFalse(result);

        // 2.6 手机号查到了商户用户，有超级管理员，有主体信息 主体信息一致 允许绑定
        Mockito.doReturn(new MerchantBusinessLicenseInfo().setType(1).setLegal_person_id_number(identity)).when(merchantBusinessLicenseService).getLatestMerchantBusinessLicenseByMerchantId("superAdminId");
        result = merchantService.allowBindAccount(null, cellphone, identity);
        Assert.assertTrue(result);

    }
} 
