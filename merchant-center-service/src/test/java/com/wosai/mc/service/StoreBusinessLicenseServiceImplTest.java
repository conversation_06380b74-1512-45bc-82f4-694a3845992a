package com.wosai.mc.service;

import com.wosai.mc.BaseTest;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.biz.McPreBiz;
import com.wosai.mc.biz.StoreBusinessLicenseBiz;
import com.wosai.mc.model.License;
import com.wosai.mc.model.StoreBusinessLicenseInfo;
import com.wosai.mc.model.req.*;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * MerchantBusinessLicenseServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Aug 17, 2020</pre>
 */
public class StoreBusinessLicenseServiceImplTest extends BaseTest {
    @Autowired
    private StoreBusinessLicenseService storeBusinessLicenseService;
    private String store_id = "000010246a1f-39a8-58b4-30d4-e8fa0db0";
    private String merchant_id = "dd616f9bc5d2-4f0b-cc14-1414-cb1962f8";

    @Test
    public void createStoreBusinessLicense() {
        CreateStoreBusinessLicenseReq createStoreBusinessLicense = new CreateStoreBusinessLicenseReq();
        createStoreBusinessLicense.setStoreId(store_id)
                .setMerchantId(merchant_id)
                .setTrade_license_list(Arrays.asList(new CreateLicenseWithBusinessReq().setLicensePhoto("0")));

        try {
            storeBusinessLicenseService.createStoreBusinessLicense(createStoreBusinessLicense);
        } catch (Exception e) {
            if (e.getMessage().contains("重复")) {
                return;
            }
        }

        StoreBusinessLicenseInfo storeBusinessLicenseInfo = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id, null);
        List<License> licenses = storeBusinessLicenseInfo.getTrade_license_list();
        Assert.assertEquals(1, licenses.size());
        Assert.assertEquals(licenses.get(0).getLicense_photo(), "0");

    }

    @Test
    public void updateStoreBusinessLicense() {
        String string = UUID.randomUUID().toString();
        UpdateStoreBusinessLicenseReq req = new UpdateStoreBusinessLicenseReq().setStoreId(store_id).setAddress(string);
        storeBusinessLicenseService.updateStoreBusinessLicense(req, "dev");

        StoreBusinessLicenseInfo info = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id, null);

        Assert.assertNotEquals(string, info.getAddress());

        StoreBusinessLicenseInfo mc_info = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id, "dev");

        Assert.assertEquals(string, mc_info.getAddress());

        storeBusinessLicenseService.copyMcInfoToStoreBusinessLicenseByStoreId(store_id, "dev");

        info = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id, null);

        Assert.assertEquals(string, info.getAddress());

    }


}
