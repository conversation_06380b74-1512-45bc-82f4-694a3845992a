package com.wosai.mc.service;

import com.wosai.mc.BaseTest;
import com.wosai.mc.apolloBeans.CommonApolloConfigs;
import com.wosai.mc.biz.McPreBiz;
import com.wosai.mc.biz.MerchantBusinessLicenseBiz;
import com.wosai.mc.biz.SensorSendBiz;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.UpdateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.VerifyMerchantBusinessLicenseReq;
import com.wosai.mc.model.resp.FieldStatusResp;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * MerchantBusinessLicenseServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Aug 17, 2020</pre>
 */
public class MerchantBusinessLicenseServiceImplTest extends BaseTest {

    @InjectMocks
    private MerchantBusinessLicenseServiceImpl merchantBusinessLicenseService;

    @Mock
    private com.wosai.upay.core.service.MerchantBusinessLicenseService coreMerchantBusinessLicenseService;

    @Mock
    private com.wosai.upay.core.service.McPreService mcPreService;
    @Mock
    private McPreBiz mcPreBiz;
    @Mock
    private com.wosai.upay.core.service.McPreService corBmcPreService;
    @Mock
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;
    @Mock
    private  SensorSendBiz sensorSendBiz;
    @Mock
    private CommonApolloConfigs commonApolloConfigs;

    @Test
    public void createMerchantBusinessLicense() {
        CreateMerchantBusinessLicenseReq createMerchantBusinessLicenseReq = new CreateMerchantBusinessLicenseReq();
        createMerchantBusinessLicenseReq.setMerchantId("merchantId");
        Map map = new HashMap<>();
        map.put("merchant_id", createMerchantBusinessLicenseReq.getMerchantId());
        Mockito.doReturn(map).when(coreMerchantBusinessLicenseService).getBusinessLicenseByMerchantId(createMerchantBusinessLicenseReq.getMerchantId());

        MerchantBusinessLicenseInfo result = merchantBusinessLicenseService.createMerchantBusinessLicense(createMerchantBusinessLicenseReq);
        Assert.assertEquals(result.getMerchant_id(), createMerchantBusinessLicenseReq.getMerchantId());
    }

    @Test
    public void updateMerchantBusinessLicense() {
        UpdateMerchantBusinessLicenseReq req = new UpdateMerchantBusinessLicenseReq();
        req.setMerchantId("merchantId");
        int i = merchantBusinessLicenseService.updateMerchantBusinessLicense(req, null);
        Assert.assertEquals(1, i);
    }

    @Test
    public void deleteMerchantBusinessLicenseById() {
        merchantBusinessLicenseService.deleteMerchantBusinessLicenseById("123");
    }

    @Test
    public void getMerchantBusinessLicenseByMerchantId() {
        MerchantBusinessLicenseInfo result = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId("123", null);
        Assert.assertNull(result);
    }

    @Test
    public void getLatestMerchantBusinessLicenseByMerchantId() {
        String merchantId = "merchant_id";
        Map<String, Object> originalLicense = new HashMap<>();
        originalLicense.put("merchant_id", merchantId);
        originalLicense.put("photo", "www.baidu.com");

        Map<String, Object> newLicense = new HashMap<>();
        newLicense.put("merchant_id", merchantId);
        newLicense.put("photo", "www.shouqianba.com");

        Mockito.doReturn(originalLicense).when(coreMerchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(newLicense).when(mcPreBiz).mergeMcPre(merchantId, originalLicense, TableNameEnum.MERCHANT_BUSINESS_LICENSE.getTableName());

        MerchantBusinessLicenseInfo result = merchantBusinessLicenseService.getLatestMerchantBusinessLicenseByMerchantId(merchantId);
        Assert.assertEquals("www.shouqianba.com", result.getPhoto());

    }

    @Test
    public void verifyMerchantBusinessLicense() {
        String merchantId = "merchant_id";
        MerchantBusinessLicenseInfo licenseInfo = new MerchantBusinessLicenseInfo();
        licenseInfo.setMerchant_id(merchantId);
        Map<String, Object> extra = new HashMap<>();
        extra.put("1", Lists.newArrayList("photo", "type"));
        extra.put("3", Lists.newArrayList("legal_person_id_number", "number"));
        extra.put("black", true);
        licenseInfo.setExtra(extra);
        Mockito.doReturn(licenseInfo).when(merchantBusinessLicenseBiz).getMerchantBusinessLicenseByMerchantId(merchantId, null, true);

        VerifyMerchantBusinessLicenseReq req = new VerifyMerchantBusinessLicenseReq();
        req.setVerifyStatus("2").setVerifyParams(Lists.newArrayList("type", "number")).setMerchantId(merchantId);
        int result = merchantBusinessLicenseService.verifyMerchantBusinessLicense(req);
        Assert.assertEquals(1, result);
    }

    @Test
    public void getMerchantBusinessLicenseVerifyFieldStatus() {
        String merchantId = "merchant_id";
        Map<String, Object> license = new HashMap<>();
        Map<String, Object> extra = new HashMap<>();
        extra.put("1", Arrays.asList("photo", "type"));
        extra.put("3", Arrays.asList("legal_person_id_number", "number"));
        extra.put("black", true);
        license.put("extra", extra);
        Mockito.doReturn(license).when(coreMerchantBusinessLicenseService).getBusinessLicenseByMerchantId(merchantId);
        Mockito.doReturn(Arrays.asList("number", "type")).when(commonApolloConfigs).getLicenseBlackField();

        FieldStatusResp result = merchantBusinessLicenseService.getMerchantBusinessLicenseVerifyFieldStatus(merchantId);
        Assert.assertFalse(result.getVerifying().isEmpty());
        Assert.assertFalse(result.getVerifyFailed().isEmpty());
        Assert.assertFalse(result.getBlackField().isEmpty());
    }

    @Test
    public void copyMcInfoToMerchantBusinessLicenseByMerchantId() {
        String merchantId = "merchantId";
        String devCode = "devCode";
        Map data = new HashMap();
        data.put("merchant_id", merchantId);
        data.put("mcId", 1);
        data.put("photo", "www.shouqianba.com");

        Mockito.doReturn(data).when(mcPreBiz).getMcPreDataWithMcId(merchantId, devCode, TableNameEnum.MERCHANT_BUSINESS_LICENSE.getTableName());
        int result = merchantBusinessLicenseService.copyMcInfoToMerchantBusinessLicenseByMerchantId(merchantId, devCode);
        Assert.assertEquals(1, result);
    }


} 
