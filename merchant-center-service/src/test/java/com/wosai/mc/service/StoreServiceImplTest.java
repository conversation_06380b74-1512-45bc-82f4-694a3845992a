package com.wosai.mc.service;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.wosai.mc.BaseTest;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.StoreFilterConfig;
import com.wosai.mc.model.req.*;
import com.wosai.mc.model.resp.StoreAllInfo;
import com.wosai.mc.utils.CommonUtils;
import com.wosai.upay.common.bean.ListResult;
import org.apache.commons.collections.MapUtils;
import org.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.UUID;

/**
 * StoreServiceImpl Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Aug 18, 2020</pre>
 */
public class StoreServiceImplTest extends BaseTest {
    @Autowired
    StoreService storeService;


    @BeforeClass
    public static void beforeClass() throws Exception {
    }


    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    @AfterClass
    public static void afterClass() throws Exception {
    }

    /**
     * Method: createStore(CreateStoreReq req)
     */
    @Test
    public void testCreateStore() throws Exception {
        CreateStoreReq req = new CreateStoreReq();
        String id = UUID.randomUUID().toString();
        System.out.println(id);
        req.setId(id);
        PhotoInfo photoInfo = new PhotoInfo();
        photoInfo.setLatitude("latitudenew");
        photoInfo.setLongitude("longitudenew");
        photoInfo.setUrl("urlnew");
        photoInfo.setAddress("address");
        photoInfo.setCity("city");
        photoInfo.setDistrict("district");
        photoInfo.setProvince("province");
        photoInfo.setTime(System.nanoTime());
        photoInfo.setUser_id(UUID.randomUUID().toString());
        photoInfo.setSystem_version("systemVersion");
        photoInfo.setSystem("system");
        req.setAuditPicture(photoInfo);
        req.setOtherPhoto(Lists.newArrayList(photoInfo, photoInfo));
        req.setBrandPhoto(photoInfo);
        req.setMerchantId("**********");
        req.setCity("citynew");
        req.setName("*************");
        req.setProvince("2222222new");
        req.setCity("3333333333new");
        req.setAverageConsumptionTime("average_consumption_timenew");
        req.setOpenAccountWay(1);
        req.setSolicitorId("solicitor_id");
        req.setVendorId("vendorId");
        System.out.println(storeService.createStore(req));
//        Assert.assertNotNull(storeService.createStore(req));
    }

    /**
     * Method: getStoreById(String storeId)
     */
    @Test
    public void testGetStoreById() throws Exception {
        System.out.println(storeService.getStoreById("068cde0c-c494-461e-8f4a-f671ca35a08c", null));
        Assert.assertNotNull(storeService.getStoreById("068cde0c-c494-461e-8f4a-f671ca35a08c", null));
//        Assert.assertNotNull(storeService.getStoreById(null));

//TODO: Test goes here... 
    }

    /**
     * Method: getStoreBySn(String storeSn)
     */
    @Test
    public void testGetStoreBySn() throws Exception {
        Assert.assertNotNull(storeService.getStoreBySn("*****************", null));
//        Assert.assertNotNull(storeService.getStoreBySn(null));
//TODO: Test goes here...
    }

    /**
     * Method: getStoreByClientSn(String merchantId, String clientSn)
     */
    @Test
    public void testGetStoreByClientSn() throws Exception {
        System.out.println(storeService.getStoreByClientSn("**********", "clientSn2"));
        Assert.assertNotNull(storeService.getStoreByClientSn("**********", "clientSn2"));
//        Assert.assertNotNull(storeService.getStoreByClientSn("", ""));

//TODO: Test goes here... 
    }


    @Test
    public void testVerifyStore() throws Exception {
//        VerifyStoreReq req = new VerifyStoreReq();
//        req.setVerifyStatus("1");
//        req.setStoreId("6dfa9849-e533-409f-8004-244870884d1a");
//        for (int i = 1; i < 4; i++) {
//            req.setVerifyParams(Lists.newArrayList("" + i + "" + i));
//            req.setVerifyStatus(i + "");
//            Assert.assertEquals(1, storeService.verifyStore(req));
//        }
    }

    @Test
    public void testGetStoreByIdAndDevCode() throws Exception {
//        com.wosai.mc.model.StoreInfo store = storeService.getStoreById("127b98ab-a9ca-4444-a4ff-95f15284d282", null);
        com.wosai.mc.model.StoreInfo store = storeService.getStoreById("faa52a4a-8c01-48b8-bdd5-9ae833730134", "store");
        System.out.println(store);
    }

    @Test
    public void testGetLatestStoreById() throws Exception {
        com.wosai.mc.model.StoreInfo store = storeService.getLatestStoreById("faa52a4a-8c01-48b8-bdd5-9ae833730134");
        System.out.println(store);
    }

    @Test
    public void testGetStoreAllInfoById() throws Exception {
        StoreAllInfo allStoreAllInfoById = storeService.getStoreAllInfoById("faa52a4a-8c01-48b8-bdd5-9ae833730134");
        Assert.assertNotNull(allStoreAllInfoById);
        System.out.println(JSON.toJSON(allStoreAllInfoById));
    }

    @Test
    public void testCopyMcInfoToStoreByMerchantId() throws Exception {
        int i = storeService.copyMcInfoToStoreByMerchantId("**********", "store");
        Assert.assertEquals(1, i);
    }

    @Test
    public void testUpdateStore() throws Exception {
        UpdateStoreReq updateStoreReq = new UpdateStoreReq();
        updateStoreReq.setId("ccdba07e-b4f4-483a-9e66-98c54a40e170");
        updateStoreReq.setContactName("average_consumptionNEW4");
        updateStoreReq.setDevCode("store");
//        updateStoreReq.setLatitude("1");
        PhotoInfo photoInfo = new PhotoInfo();
        photoInfo.setLatitude("latitudenew5");
        photoInfo.setLongitude("longitudenew5");
        photoInfo.setUrl("testUpdateStorePicturesByStoreId");
        photoInfo.setAddress("address");
        photoInfo.setCity("city");
        photoInfo.setDistrict("district");
        photoInfo.setProvince("province");
        photoInfo.setTime(System.nanoTime());
        photoInfo.setUser_id(UUID.randomUUID().toString());
        photoInfo.setSystem_version("systemVersion");
        photoInfo.setSystem("system");
        photoInfo.setPosition("position");
        photoInfo.setLocation_from("locationNew4");
        updateStoreReq.setAuditPicture(photoInfo);
        updateStoreReq.setBrandPhoto(photoInfo);
        updateStoreReq.setIndoorMaterialPhoto(photoInfo);
        updateStoreReq.setOutdoorMaterialPhoto(photoInfo);
        updateStoreReq.setProductPrice(photoInfo);
        updateStoreReq.setOtherPhoto(Lists.newArrayList(photoInfo, photoInfo));
        Assert.assertNotNull(storeService.updateStore(updateStoreReq));
        com.wosai.mc.model.StoreInfo store1 = storeService.getStoreById("ccdba07e-b4f4-483a-9e66-98c54a40e170", "store");
        System.out.println(store1);
    }

    @Test
    public void testUpdateStorePicturesByStoreId() throws Exception {
        UpdateStorePicturesReq updateStorePicturesReq = new UpdateStorePicturesReq();
        updateStorePicturesReq.setStoreId("4fd95e54-7992-47e7-bb4d-2bc0692dc5b8");
        PhotoInfo photoInfo = new PhotoInfo();
        photoInfo.setLatitude("test");
        photoInfo.setLongitude("test");
        photoInfo.setUrl("test");
        photoInfo.setAddress("test");
        photoInfo.setCity("test");
        photoInfo.setDistrict("test");
        photoInfo.setProvince("test");
        photoInfo.setTime(System.nanoTime());
        photoInfo.setUser_id(UUID.randomUUID().toString());
        photoInfo.setSystem_version("test");
        photoInfo.setSystem("test");
        photoInfo.setPosition("test");
        photoInfo.setLocation_from("test");
        updateStorePicturesReq.setAuditPicture(photoInfo);
        updateStorePicturesReq.setBrandPhoto(photoInfo);
        updateStorePicturesReq.setIndoorMaterialPhoto(photoInfo);
        updateStorePicturesReq.setOutdoorMaterialPhoto(photoInfo);
        updateStorePicturesReq.setProductPrice(photoInfo);
        updateStorePicturesReq.setOtherPhoto(Lists.newArrayList(photoInfo, photoInfo));
        Integer res = storeService.updateStorePicturesByStoreId(updateStorePicturesReq, "store");
        System.out.println(JSON.toJSONString(res));
        Assert.assertNotNull(res);
    }

    @Test
    public void getStoreListByMerchantUserId() {
        String merchantUserId = "358964a1-cc5d-4caa-ba89-8f11da343ab7";

        StoreFilterConfig config = storeService.queryStoreFilterConfig(new QueryStoreFilterConfigReq().setMerchant_user_id(merchantUserId));

        ListResult list = storeService.getStoreListByMerchantUserId(new GetStoreListByMerchantUserIdReq().setMerchant_user_id(merchantUserId).setManual_order(true));
        //总数1
        long total1 = list.getTotal();

        //更新配置
        String storeId1 = MapUtils.getString(list.getRecords().get(list.getRecords().size() - 1), "id");
        String storeId2 = MapUtils.getString(list.getRecords().get(list.getRecords().size() - 2), "id");
        StoreFilterConfig configCopy = CommonUtils.beanToTargetObj(config, StoreFilterConfig.class);
        configCopy.setManual(Arrays.asList(storeId1, storeId2));
        storeService.createOrUpdateStoreFilterConfig(new CreateOrUpdateStoreFilterConfigReq().setConfig(configCopy).setMerchant_user_id(merchantUserId));


        list = storeService.getStoreListByMerchantUserId(new GetStoreListByMerchantUserIdReq().setMerchant_user_id(merchantUserId).setManual_order(true));
        //总数2
        long total2 = list.getTotal();

        Assert.assertEquals(total1, total2);

        String storeId1Updated = MapUtils.getString(list.getRecords().get(0), "id");
        String storeId2Updated = MapUtils.getString(list.getRecords().get(1), "id");

        Assert.assertEquals(storeId1, storeId1Updated);
        Assert.assertEquals(storeId2, storeId2Updated);

        //还原配置
        storeService.createOrUpdateStoreFilterConfig(new CreateOrUpdateStoreFilterConfigReq().setConfig(config).setMerchant_user_id(merchantUserId));

    }

} 
