package com.wosai.mc.biz;

import com.google.protobuf.Any;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.dto.multi.resp.NaturalPersonResp;
import com.wosai.app.service.MultiMerchantService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.mc.BaseTest;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.UpdateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.UpdateMerchantComplete;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.model.resp.BindAccountResp;
import com.wosai.mc.remote.IMerchantService;
import com.wosai.mc.utils.SmsBiz;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2021/6/7
 */
public class MerchantUserBizTest extends BaseTest {

    @InjectMocks
    private MerchantUserBiz merchantUserBiz;

    @Mock
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Mock
    private IMerchantService iMerchantService;

    @Mock
    private UcUserAccountService ucUserAccountService;

    @Mock
    private MultiMerchantService multiMerchantService;

    @Mock
    private TagIngestService tagIngestService;

    @Mock
    private MerchantBusinessLicenseBiz licenseBiz;

    @Mock
    private SmsBiz smsBiz;

    @Test
    public void checkUcUserId01() {
        // 只有手机号 没有商户用户
        AccountReq accountReq = new AccountReq();
        accountReq.setIdentifier("***********");
        Mockito.doReturn(null).when(merchantUserServiceV2).getSimpleInfoByCellphone(accountReq.getIdentifier());
        merchantUserBiz.checkUcUserId(accountReq);

        // 只有手机号 有商户用户
        Mockito.doReturn(Collections.singletonList(new UcMerchantUserSimpleInfo())).when(merchantUserServiceV2).getSimpleInfoByCellphone(accountReq.getIdentifier());
        CommonInvalidParameterException exception = Assertions.assertThrows(CommonInvalidParameterException.class, () -> merchantUserBiz.checkUcUserId(accountReq));
        Assert.assertEquals(exception.getMessage(), "账号已存在");
    }

    @Test
    public void checkUcUserId02() {
        // 有用户id 并且用户存在
        AccountReq accountReq = new AccountReq();
        accountReq.setUcUserId("ucUserId");
        Mockito.doReturn(new UcUserInfo()).when(ucUserAccountService).getUcUserById(accountReq.getUcUserId());
        merchantUserBiz.checkUcUserId(accountReq);

        // 有用户id 并且用户不存在
        Mockito.doReturn(null).when(ucUserAccountService).getUcUserById(accountReq.getUcUserId());
        CommonInvalidParameterException exception = Assertions.assertThrows(CommonInvalidParameterException.class, () -> merchantUserBiz.checkUcUserId(accountReq));
        Assert.assertEquals(exception.getMessage(), "账号对应的用户不存在");
    }

    @Test
    public void createOrUpdateAccount() {
        UpdateMerchantComplete complete = new UpdateMerchantComplete();
        UpdateMerchantBusinessLicenseReq licenseInfo = new UpdateMerchantBusinessLicenseReq();
        licenseInfo.setType(11);
        licenseInfo.setLegalPersonIdNumber("123456199001011234");
        licenseInfo.setLegalPersonIdType(1);
        complete.setMerchant(new UpdateMerchantReq().setId("merchantId"));
        complete.setLicense(licenseInfo);

        // 查询到老板账号信息
        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setCellphone("***********").setUc_user_id("ucUserId"))).when(merchantUserServiceV2).getSuperAdminByMerchantId("merchantId");

        // 1. 旧版app创建实名信息
        Mockito.doReturn(new NaturalPersonResp()).when(multiMerchantService).createRealNameInfo(any());
        merchantUserBiz.createOrUpdateAccount(complete);

        // 2. 手机号不变，新的证件号是123456199001011234   旧的证件号是123456199001011235
        // 2.1 账号对应的实名信息不存在 而且是负责人类型  去创建实名信息
        AccountReq accountReq = new AccountReq();
        accountReq.setIdentifier("***********");
        accountReq.setIdNumber("123456199001011234");
        accountReq.setIdType(1);

        complete.setAccount(accountReq);

        Mockito.doReturn(null).when(multiMerchantService).getRealNameInfoByUcUserId(any());
        merchantUserBiz.createOrUpdateAccount(complete);

        // 2.2 账号对应的实名信息存在，证件号相同  不做操作
        Mockito.doReturn(new NaturalPersonResp().setIdentity_no("123456199001011234")).when(multiMerchantService).getRealNameInfoByUcUserId(any());
        merchantUserBiz.createOrUpdateAccount(complete);

        // 2.3 账号对应的实名信息存在，证件号不同  重新创建和绑定
        Mockito.doReturn(new NaturalPersonResp().setIdentity_no("123456199001011235")).when(multiMerchantService).getRealNameInfoByUcUserId(any());
        merchantUserBiz.createOrUpdateAccount(complete);
    }

    @Test
    public void createOrUpdateAccount02() {
        UpdateMerchantComplete complete = new UpdateMerchantComplete();
        Mockito.doReturn(new NaturalPersonResp()).when(multiMerchantService).createRealNameInfo(any());

        // 2. 手机号改变，新的手机号是***********   旧的手机号是***********
        // 2.1 存在ucUserId 实名信息不存在
        AccountReq accountReq = new AccountReq();
        accountReq.setIdentifier("***********");
        accountReq.setIdNumber("123456199001011234");
        accountReq.setIdType(1);
        accountReq.setUcUserId("ucUserId");
        UpdateMerchantBusinessLicenseReq licenseInfo = new UpdateMerchantBusinessLicenseReq();
        licenseInfo.setType(1);
        licenseInfo.setLegalPersonIdType(1);
        licenseInfo.setLegalPersonIdNumber("123456199001011234");

        complete.setMerchant(new UpdateMerchantReq().setId("merchantId"));
        complete.setAccount(accountReq);
        complete.setLicense(licenseInfo);

        Mockito.doReturn(new UcUserInfo()).when(ucUserAccountService).getUcUserById(accountReq.getUcUserId());
        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setCellphone("***********").setUc_user_id("ucUserId"))).when(merchantUserServiceV2).getSuperAdminByMerchantId("merchantId");
        Mockito.doReturn(null).when(multiMerchantService).getRealNameInfoByUcUserId(any());
        merchantUserBiz.createOrUpdateAccount(complete);

        // 2.2 存在ucUserId 实名信息存在
        Mockito.doReturn(new NaturalPersonResp().setIdentity_no("123456199001011235")).when(multiMerchantService).getRealNameInfoByIdentityNo(any());
        merchantUserBiz.createOrUpdateAccount(complete);
    }

    @Test
    public void createOrUpdateAccount03() {
        UpdateMerchantComplete complete = new UpdateMerchantComplete();
        Mockito.doReturn(new NaturalPersonResp()).when(multiMerchantService).createRealNameInfo(any());

        // 3. 手机号改变，新的手机号是***********   旧的手机号是*********** 不存在ucUserId
        // 3.1 不存在ucUserId 实名信息不存在 并且该账号不是多商户，只有自己一个商户 走changeCellphone
        AccountReq accountReq = new AccountReq();
        accountReq.setIdentifier("***********");
        UpdateMerchantBusinessLicenseReq licenseInfo = new UpdateMerchantBusinessLicenseReq();
        licenseInfo.setType(11);
        licenseInfo.setLegalPersonIdType(1);
        licenseInfo.setLegalPersonIdNumber("123456199001011234");

        complete.setMerchant(new UpdateMerchantReq().setId("merchantId"));
        complete.setAccount(accountReq);
        complete.setLicense(licenseInfo);

        Mockito.doReturn(new UcUserInfo()).when(ucUserAccountService).getUcUserByCellphone(accountReq.getIdentifier());
        Mockito.doReturn(new UcUserInfo()).when(ucUserAccountService).createUcUser(any());
        Mockito.doReturn(new UcMerchantUserInfo().setUcUserInfo(new UcUserInfo().setCellphone("***********").setUc_user_id("ucUserId"))).when(merchantUserServiceV2).getSuperAdminByMerchantId("merchantId");
        Mockito.doReturn(null).when(multiMerchantService).getRealNameInfoByUcUserId(any());
        Mockito.doReturn(Collections.singletonList(new UcMerchantUserSimpleInfo().setMerchant_id("merchantId"))).when(merchantUserServiceV2).getSimpleInfoByUcUserId("ucUserId");
        merchantUserBiz.createOrUpdateAccount(complete);

        // 2.2 不存在ucUserId 实名信息存在, 而且账号关联了多商户
        Mockito.doReturn(Arrays.asList(new UcMerchantUserSimpleInfo().setMerchant_id("merchantId"), new UcMerchantUserSimpleInfo().setMerchant_id("merchantId2")))
                .when(merchantUserServiceV2).getSimpleInfoByUcUserId("ucUserId");
        Mockito.doReturn(new NaturalPersonResp().setIdentity_no("123456199001011235")).when(multiMerchantService).getRealNameInfoByIdentityNo(any());
        merchantUserBiz.createOrUpdateAccount(complete);
    }

    @Test
    public void getAuthBindAccountByIdentity() {
        String identity = "123456199001011234";
        // 1 没有实名信息
        Map<String, BindAccountResp> result = merchantUserBiz.getAuthBindAccountByIdentity(identity);
        Assert.assertTrue(result.isEmpty());

        // 2 有实名信息但是状态是未实名
        NaturalPersonResp naturalPersonResp = new NaturalPersonResp().setAuth_status(0);
        Mockito.doReturn(naturalPersonResp).when(multiMerchantService).getRealNameInfoByIdentityNo(any());
        result = merchantUserBiz.getAuthBindAccountByIdentity(identity);
        Assert.assertTrue(result.isEmpty());

        // 3 有实名信息 但是没有实名信息对应的用户信息
        naturalPersonResp.setAuth_status(1).setNatural_person_id("naturalPersonId");
        result = merchantUserBiz.getAuthBindAccountByIdentity(identity);
        Assert.assertTrue(result.isEmpty());

        // 4 有实名信息对应的用户信息
        Mockito.doReturn(Collections.singletonList(new UcUserInfo().setUc_user_id("ucUserId"))).when(ucUserAccountService).queryUcUserByNaturalPersonId(naturalPersonResp.getNatural_person_id());
        result = merchantUserBiz.getAuthBindAccountByIdentity(identity);
        Assert.assertEquals(1, result.size());
    }

}
