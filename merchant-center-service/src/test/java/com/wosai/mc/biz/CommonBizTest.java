package com.wosai.mc.biz;

import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.service.ContractStatusService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@RunWith(MockitoJUnitRunner.class)
public class CommonBizTest {

    @InjectMocks
    private CommonBiz commonBiz;

    @Mock
    private MerchantService merchantService;

    @Mock
    private ContractStatusService contractStatusService;

    @Mock
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Mock
    private TradeConfigService tradeConfigService;

    /**
     * 商户状态不正常
     */
    @Test
    public void baseJudge01() {
        String merchantId = "merchantId";
        Map merchant = CollectionUtil.hashMap("id", merchantId, "status", Merchant.STATUS_CLOSED);
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantId(merchantId);

        // 状态不是正常的
        Map<String, Object> result = commonBiz.baseJudge(merchantId);
        assertNull(result);
    }

    /**
     * 状态正常 但进件没有通过
     */
    @Test
    public void baseJudge02() {
        String merchantId = "merchantId";
        Map merchant = CollectionUtil.hashMap("id", merchantId, "sn", "merchantSn", "status", Merchant.STATUS_ENABLED);
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantId(merchantId);

        // 进件没有通过
        Mockito.doReturn(new ContractStatus().setStatus(ContractStatus.STATUS_PENDING)).when(contractStatusService).selectByMerchantSn("merchantSn");
        Map<String, Object> result = commonBiz.baseJudge(merchantId);
        assertNull(result);
    }

    /**
     * 状态正常 进件通过 但没有老板帐号信息
     */
    @Test
    public void baseJudge03() {
        String merchantId = "merchantId";
        Map merchant = CollectionUtil.hashMap("id", merchantId, "sn", "merchantSn", "status", Merchant.STATUS_ENABLED);
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(new ContractStatus().setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusService).selectByMerchantSn("merchantSn");

        // 没有老板信息
        Map<String, Object> result = commonBiz.baseJudge(merchantId);
        assertNull(result);
    }

    /**
     * 状态正常 进件通过 有老板帐号信息 没有间连的通道
     */
    @Test
    public void baseJudge04() {
        String merchantId = "merchantId";
        Map merchant = CollectionUtil.hashMap("id", merchantId, "sn", "merchantSn", "status", Merchant.STATUS_ENABLED);
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(new ContractStatus().setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusService).selectByMerchantSn("merchantSn");

        // 但是没有间连的通道
        Mockito.doReturn(new UcMerchantUserSimpleInfo()).when(merchantUserServiceV2).getSuperAdminSimpleInfoByMerchantId(merchantId);
        Mockito.doReturn(Arrays.asList(CollectionUtil.hashMap("payway", 2, MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED, MerchantConfig.B2C_FORMAL, true), CollectionUtil.hashMap("payway", 3, MerchantConfig.B2C_FORMAL, true)))
                .when(tradeConfigService).getMerchantConfigsByMerchantId(merchantId);
        Map<String, Object> result = commonBiz.baseJudge(merchantId);
        assertNull(result);
    }

    /**
     * 状态正常 进件通过 有老板帐号信息 有间连的通道
     */
    @Test
    public void baseJudge05() {
        String merchantId = "merchantId";
        Map merchant = CollectionUtil.hashMap("id", merchantId, "sn", "merchantSn", "status", Merchant.STATUS_ENABLED);
        Mockito.doReturn(merchant).when(merchantService).getMerchantByMerchantId(merchantId);
        Mockito.doReturn(new ContractStatus().setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusService).selectByMerchantSn("merchantSn");

        // 但是没有间连的通道
        Mockito.doReturn(new UcMerchantUserSimpleInfo()).when(merchantUserServiceV2).getSuperAdminSimpleInfoByMerchantId(merchantId);
        Mockito.doReturn(Arrays.asList(CollectionUtil.hashMap("payway", 2, MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED, MerchantConfig.B2C_FORMAL, true, MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED, MerchantConfig.C2B_FORMAL, false), CollectionUtil.hashMap("payway", 3, MerchantConfig.B2C_FORMAL, true)))
                .when(tradeConfigService).getMerchantConfigsByMerchantId(merchantId);
        Map<String, Object> result = commonBiz.baseJudge(merchantId);
        assertNotNull(result);
    }

}