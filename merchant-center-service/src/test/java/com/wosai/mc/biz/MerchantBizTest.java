package com.wosai.mc.biz;

import com.wosai.mc.BaseTest;
import com.wosai.mc.constants.TableNameEnum;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.upay.core.service.McPreService;
import com.wosai.upay.core.service.MerchantService;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class MerchantBizTest extends BaseTest {

    @Autowired
    private MerchantBiz merchantBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private McPreBiz mcPreBiz;

    @Autowired
    private McPreService mcPreService;

    @Test
    public void createMerchant() {
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setName("单元测试").setLongitude("123.123").setLatitude("40.123").setContactName("张三").setStreetAddress("上海市中江路100弄");
        MerchantInfo merchantInfo = merchantBiz.createMerchant(merchantReq);
        Assert.assertNotNull(merchantInfo);
        MerchantInfo merchantInfoResult = merchantBiz.getMerchantInfoById(merchantInfo.getId(), "devCode");
        Assert.assertEquals(merchantReq.getName(), merchantInfoResult.getName());
        //把数据删除
        merchantService.deleteMerchantByMerchantId(merchantInfo.getId());
    }

    @Test
    public void updateMerchant() {
        //先去创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setName("单元测试").setLongitude("123.123").setLatitude("40.123").setContactName("张三").setStreetAddress("上海市中江路100弄");
        MerchantInfo merchantInfo = merchantBiz.createMerchant(merchantReq);
        Assert.assertNotNull(merchantInfo);
        UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq();
        updateMerchantReq.setId(merchantInfo.getId()).setName("更新后的名字");
        merchantBiz.updateMerchant(updateMerchantReq, "devCode", null);

        MerchantInfo merchantInfoResult = merchantBiz.getMerchantInfoById(merchantInfo.getId(), "devCode");
        Assert.assertEquals(updateMerchantReq.getName(), merchantInfoResult.getName());
        merchantService.deleteMerchantByMerchantId(merchantInfo.getId());
        Map result = mcPreBiz.getMcPreDataWithMcId(merchantInfo.getId(), "devCode", TableNameEnum.MERCHANT.getTableName());
        Assert.assertTrue(MapUtils.isNotEmpty(result));
        mcPreService.deleteMcPre((int) result.get("mcId"));
    }

    @Test
    public void getMerchantById() {
    }

    @Test
    public void getMerchantBySn() {
    }
}