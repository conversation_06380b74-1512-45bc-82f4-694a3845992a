package com.wosai.mc.biz;

import com.wosai.app.service.VideoService;
import com.wosai.mc.BaseTest;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.req.StoreExtReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.PhotoInfoService;
import com.wosai.upay.core.service.StoreExtService;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

public class StoreExtBizTest extends BaseTest {
    @Autowired
    private StoreExtBiz storeExtBiz;
    @Mock
    private StoreExtService storeExtService;

    @Mock
    private McPreBiz mcPreBiz;

    @Mock
    private PhotoInfoService photoInfoService;

    @Mock
    private VideoService videoService;
    @Before
    public void before () throws  Exception{}
    @After
    public void  after() throws  Exception{}

    @Test
    public void updateStoreExtTest(){
        StoreExtReq storeExtReq = new StoreExtReq().setId(26l).setTableCount(4).setStoreId("0d111cd0-e4a5-4b10-b40c-1f4f057066af")
                .setBrandPhotoId("999cfae3-7795-4682-829f-a7ecd066500a");
        Mockito.doReturn(new HashMap<>()).when(storeExtService).findStoreExt(storeExtReq.getId().toString());
        Mockito.doReturn(new HashMap<>()).when(storeExtService).findStoreExtByStoreId(storeExtReq.getStoreId());
        storeExtBiz.updateStoreExt(storeExtReq,null);
        Assert.assertEquals(storeExtReq.getBrandPhotoId(),storeExtBiz.findStoreExt(storeExtReq.getId().toString(),null).getBrandPhotoId());
    }

    @Test
    public void findStoreExtTest(){
        StoreExtInfo storeExt = storeExtBiz.findStoreExt("18230", null);
        Assert.assertNotNull(storeExt);
    }

    @Test
    public void findStoreExtByStoreIdTest(){
        StoreExtInfo storeExtByStoreId = storeExtBiz.findStoreExtByStoreId("56259cde-9a82-443c-9918-94f8867a0bc6", null);
        Assert.assertNotNull(storeExtByStoreId);
    }

    @Test
    public void findLastStoreExtByStoreIdTest(){
        StoreExtInfo lastStoreExtByStoreId = storeExtBiz.findLastStoreExtByStoreId("************************************");
        Assert.assertNotNull(lastStoreExtByStoreId);
    }

    @Test
    public void findStoreExtAndPicturesByStoreIdTest(){
     Mockito.doReturn(new HashMap<>()).when(storeExtService).findStoreExtByStoreId("53b02423-2a57-4bc5-98df-c81c361dce34");
        StotreExtInfoAndPictures store = storeExtBiz.findStoreExtAndPicturesByStoreId("53b02423-2a57-4bc5-98df-c81c361dce34", null);
        Assert.assertEquals("53b02423-2a57-4bc5-98df-c81c361dce34",store.getStoreId());
    }

    @Test
    public void findLastStoreExtAndPicturesByStoreIdTest(){
        StotreExtInfoAndPictures store = storeExtBiz.findLastStoreExtAndPicturesByStoreId("************************************");
        Assert.assertEquals("************************************",store.getStoreId());
    }
}
