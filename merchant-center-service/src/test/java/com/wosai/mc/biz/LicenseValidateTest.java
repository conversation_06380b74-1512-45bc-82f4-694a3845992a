package com.wosai.mc.biz;

import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.BaseTest;
import com.wosai.mc.biz.validator.BusinessLicenseUpgradeValidator;
import com.wosai.mc.entity.bo.MicroUpgradeCheckResultBO;
import com.wosai.mc.service.MerchantService;
import com.wosai.sales.merchant.business.service.common.CommonAppConfigService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Objects;

public class LicenseValidateTest extends BaseTest {

    @Autowired
    private BusinessLicenseUpgradeValidator businessLicenseUpgradeValidator;

    @Test
    public void validateTest() {
        MicroUpgradeCheckResultBO result = businessLicenseUpgradeValidator.checkMicroEnableUpgrade("aa37195f-306d-45f2-9ecb-0c1f2ec4d229");
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Autowired
    private CommonAppInfoService commonAppInfoService;

    @Autowired
    private CommonAppConfigService commonAppConfigService;


    @Resource
    private LicenseAuditBiz licenseAuditBiz;

    @Test
    public void testEnableSubmitLicenseFromCrmApp() {
        licenseAuditBiz.enableSubmitLicenseFromCrmApp("0d32fc4c-d4ae-4b8c-bbbe-f855e63a5834");
    }

}