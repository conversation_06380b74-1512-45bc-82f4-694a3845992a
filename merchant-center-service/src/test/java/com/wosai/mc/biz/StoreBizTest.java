package com.wosai.mc.biz;

import com.wosai.mc.BaseTest;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.upay.core.service.PhotoInfoService;
import com.wosai.upay.core.service.StoreExtService;
import com.wosai.upay.core.service.StoreService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

public class StoreBizTest extends BaseTest {
    @Autowired
    private StoreBiz storeBiz;
    @Mock
    private StoreExtService storeExtService;

    @Mock
    private PhotoInfoService photoInfoService;

    @Mock
    private com.wosai.mc.service.PhotoInfoService mcPhotoInfoService;

    @Mock
    private StoreExtBiz storeExtBiz;

    @Mock
    private StoreService storeService;

    @Mock
    private McPreBiz mcPreBiz;

    @Before
    public void before(){}
    @After
    public void after(){}

    @Test
    public void updateStorePicturesByStoreIdTest(){
        Mockito.doReturn(new HashMap<>()).when(storeExtService).findStoreExtByStoreId("6772a3f6-8eaf-46e3-a078-fb51e97a76bd");
        Mockito.doReturn(new StoreExtInfo().setStoreId("6772a3f6-8eaf-46e3-a078-fb51e97a76bd")).when(storeExtBiz).findStoreExt("18080",null);

    }
}
