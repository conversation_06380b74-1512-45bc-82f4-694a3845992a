package com.wosai.mc.biz;

import com.wosai.mc.BaseTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
public class RedisLockTest extends BaseTest {

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Test
    public void lock() {
        try {
            redisLock.lock("merchant-center-test", "merchant-center", 60);
            String result = redisTemplate.opsForValue().get("merchant-center-test");
            Assert.assertEquals(result, "merchant-center");
            boolean unlock = redisLock.unlock("merchant-center-test", "merchant-center");
            Assert.assertTrue(unlock);
            result = redisTemplate.opsForValue().get("merchant-center-test");
            Assert.assertNull(result);
        } catch (Exception e) {
            System.out.println("redis测试错误" + e);
        }
    }
}
