package com.wosai.mc.biz;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mc.BaseTest;
import com.wosai.mc.avro.ChangeMerchantSensor;
import com.wosai.mc.avro.ChangeStoreSensor;
import com.wosai.mc.avro.CreateMerchantSensor;
import com.wosai.mc.avro.CreateStoreSensor;
import com.wosai.mc.model.*;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.StoreExtService;
import com.wosai.mc.service.StoreService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;

public class SensorSendBizTest extends BaseTest {

    @Autowired
    private SensorSendBiz sensorSendBiz;

    @MockBean
    private KafkaTemplate<String, Object> kafkaTemplate;

    @MockBean
    private StoreService storeService;

    @MockBean
    private StoreExtService storeExtService;

    @MockBean
    private MerchantBiz merchantBiz;

    @MockBean
    private MerchantBusinessLicenseBiz merchantBusinessLicenseBiz;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${sensor.topic.createMerchant}")
    private String createMerchantTopic;

    @Value("${sensor.topic.createStore}")
    private String createStoreTopic;

    @Value("${sensor.topic.changeMerchant}")
    private String changeMerchantTopic;

    @Value("${sensor.topic.changeStore}")
    private String changeStoreTopic;

    @Test
    public void sendCreateMerchantMessage() {
        ReflectionTestUtils.invokeMethod(sensorSendBiz, "sendCreateMerchantMessage", new MerchantInfo());
    }

    @Test
    public void buildCreateMerchantMessage() {
        MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setCtime(System.currentTimeMillis()).setId("merchantId").setSn("merchantSn").setName("merchantName");
        CreateMerchantSensor result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "buildCreateMerchantMessage", merchantInfo);
        Assert.assertEquals(result.getMerchantId(), merchantInfo.getId());
    }

    @Test
    public void sendCreateStoreMessage() {
        Mockito.doReturn(new MerchantInfo()).when(merchantBiz).getMerchantInfoById("merchantId", null);
        ReflectionTestUtils.invokeMethod(sensorSendBiz, "sendCreateStoreMessage", new StoreInfo().setMerchant_id("merchantId"));
    }

    @Test
    public void sendCreateStoreMessageException() {
        ReflectionTestUtils.invokeMethod(sensorSendBiz, "sendCreateStoreMessage", new StoreInfo());
    }

    @Test
    public void buildCreateStoreMessage() {
        StoreInfo storeInfo = new StoreInfo();
        storeInfo.setMerchant_id("merchantId").setId("storeId").setSn("storeSn");
        MerchantInfo merchantInfo = new MerchantInfo().setSn("merchantSn");
        Mockito.doReturn(merchantInfo).when(merchantBiz).getMerchantInfoById(storeInfo.getMerchant_id(), null);
        CreateStoreSensor result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "buildCreateStoreMessage", storeInfo);
        Assert.assertEquals(storeInfo.getId(), result.getStoreId());
    }

    @Test
    public void sendChangeMerchantMessage() {
        MerchantInfo oldMerchant = new MerchantInfo();
        oldMerchant.setName("oldName").setCity("上海市").setId("merchantId");
        MerchantBusinessLicenseInfo oldLicense = new MerchantBusinessLicenseInfo();
        oldLicense.setName("oldLicenseName").setPhoto("http://www.baidu.com?expire");
        MerchantInfo newMerchant = new MerchantInfo();
        newMerchant.setName("newName").setCity("上海市").setId("merchantId");
        MerchantBusinessLicenseInfo newLicense = new MerchantBusinessLicenseInfo();
        newLicense.setName("newLicenseName").setPhoto("http://www.hao123.com?expire");
        Mockito.doReturn(newMerchant).when(merchantBiz).getMerchantInfoById(oldMerchant.getId(), null);
        Mockito.doReturn(newLicense).when(merchantBusinessLicenseBiz).getMerchantBusinessLicenseByMerchantId(oldMerchant.getId(), null, false);
        ReflectionTestUtils.invokeMethod(sensorSendBiz, "sendChangeMerchantMessage", oldMerchant, oldLicense, null, "操作人", "crm_app");
    }

    @Test
    public void buildChangeMerchantMessage() {
        MerchantInfo oldMerchant = new MerchantInfo();
        oldMerchant.setName("oldName").setCity("上海市").setId("merchantId");
        MerchantBusinessLicenseInfo oldLicense = new MerchantBusinessLicenseInfo();
        oldLicense.setName("oldLicenseName").setPhoto("http://www.baidu.com?expire").setMerchant_id(oldMerchant.getId());
        MerchantInfo newMerchant = new MerchantInfo();
        newMerchant.setName("newName").setCity("上海市").setId("merchantId");
        MerchantBusinessLicenseInfo newLicense = new MerchantBusinessLicenseInfo();
        newLicense.setName("newLicenseName").setPhoto("http://www.hao123.com?expire").setMerchant_id(oldMerchant.getId());
        Mockito.doReturn(newMerchant).when(merchantBiz).getMerchantInfoById(oldMerchant.getId(), null);
        Mockito.doReturn(newLicense).when(merchantBusinessLicenseBiz).getMerchantBusinessLicenseByMerchantId(oldLicense.getMerchant_id(), null, false);
        ChangeMerchantSensor result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "buildChangeMerchantMessage", oldMerchant, oldLicense, null, "操作人", "crm_app");
        Assert.assertTrue(result.getPhoto());
        Assert.assertEquals(result.getLicenseName(), newLicense.getName());
    }

    @Test
    public void sendChangeStoreMessageException() {
        ReflectionTestUtils.invokeMethod(sensorSendBiz, "sendChangeStore", null, null, null,null, "操作人", "crm_app");
    }

    @Test
    public void buildChangeStoreMessage() {
        StoreInfo oldStore = new StoreInfo();
        oldStore.setName("oldName").setProvince("上海市").setId("storeId").setMerchant_id("merchantId");
        StoreExtInfo oldStoreExt = new StoreExtInfo();
        oldStoreExt.setBusinessHour("8:00-18:00").setAroundType("学校").setStoreId(oldStore.getId());
        StotreExtInfoAndPictures oldPictures = new StotreExtInfoAndPictures();
        oldPictures.setBrandPhoto(new PhotoInfo().setUrl("http://www.baidu.com?expire=asfas"))
                .setIndoorMaterialPhoto(new PhotoInfo().setUrl("http://www.shouqianba.com"))
                .setOutdoorMaterialPhoto(new PhotoInfo().setUrl("http://www.shouqianba.com"))
                .setAuditPicture(new PhotoInfo().setUrl("http://hao123.com?123123123"))
                .setProductPrice(new PhotoInfo().setUrl("http://images/sdfa/123.png?321"))
                .setStoreId(oldStore.getId());
        StoreInfo newStore = new StoreInfo();
        newStore.setName("newName").setProvince("上海市").setId("storeId").setMerchant_id("merchantId");
        StoreExtInfo newStoreExt = new StoreExtInfo();
        newStoreExt.setBusinessHour("8:00-20:00").setAroundType("医院").setStoreId(newStore.getId());
        StotreExtInfoAndPictures newPictures = new StotreExtInfoAndPictures();
        newPictures.setBrandPhoto(new PhotoInfo().setUrl("http://www.123.com?expire=asfas"))
                .setIndoorMaterialPhoto(new PhotoInfo().setUrl("http://www.shouqianba.com"))
                .setOutdoorMaterialPhoto(new PhotoInfo().setUrl("http://www.shouqianba.com"))
                .setAuditPicture(new PhotoInfo().setUrl("http://www.wosai.com?1233"))
                .setProductPrice(new PhotoInfo().setUrl("http://private-images/1231/123.jpg?123"))
                .setStoreId(newStore.getId());
        Mockito.doReturn(newStore).when(storeService).getStoreById(oldStore.getId(), null);
        Mockito.doReturn(newStoreExt).when(storeExtService).findStoreExtByStoreId(oldStore.getId(), null);
        Mockito.doReturn(newPictures).when(storeExtService).findStoreExtAndPicturesByStoreId(oldStore.getId(), null);
        Mockito.doReturn(new MerchantInfo().setSn("merchantSn")).when(merchantBiz).getMerchantInfoById(oldStore.getMerchant_id(), null);
        ChangeStoreSensor result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "buildChangeStoreMessage", oldStore, oldStoreExt, oldPictures, null, "操作人", "crm_app");
        Assert.assertEquals(newStoreExt.getAroundType(), result.getAroundType());
    }

}