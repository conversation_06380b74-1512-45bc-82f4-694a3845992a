package com.wosai.mc.biz;

import com.wosai.mc.BaseTest;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.sales.model.gaoDe.PoiDetail;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;


public class PoiBizTest extends BaseTest {

    @Autowired
    private PoiBiz poiBiz;


    @Test
    public void checkAndFillPoi() {
        CreateMerchantReq req = new CreateMerchantReq();
        req.setDistrictCode("310112");
        req.setStreetAddress("上海市闵行区陈行公路2168号临港浦江智慧广场7号楼");
        poiBiz.checkAndFillPoi(req);
        Assert.assertNotNull(req.getProvince());
        Assert.assertNotNull(req.getLongitude());
        Assert.assertNotNull(req.getLatitude());
    }

    @Test
    public void isPointInChina() {
        Assert.assertTrue(poiBiz.isPointInChina("121.474000", "31.230001"));
        Assert.assertFalse(poiBiz.isPointInChina("", "31.230001"));
        Assert.assertFalse(poiBiz.isPointInChina("121.474000", ""));
        Assert.assertFalse(poiBiz.isPointInChina("12", "12"));
        Assert.assertFalse(poiBiz.isPointInChina("12", "xx"));
    }

    @Test
    public void getPoiDetailByCompleteAddress() {
        Optional<PoiDetail> poiDetailOptional = poiBiz.getPoiDetailByCompleteAddress("上海市", "上海市", "", "上海市闵行区陈行公路2168号临港浦江智慧广场7号楼");
        Assert.assertTrue(poiDetailOptional.isPresent());
    }
}