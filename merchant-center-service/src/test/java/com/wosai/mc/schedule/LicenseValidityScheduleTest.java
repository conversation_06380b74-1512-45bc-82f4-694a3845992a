package com.wosai.mc.schedule;

import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.DbBaseTest;
import com.wosai.mc.biz.CommonBiz;
import com.wosai.mc.biz.RedisLock;
import com.wosai.mc.entity.BankHolderIdValidity;
import com.wosai.mc.entity.LicenseLegalPersonIdValidity;
import com.wosai.mc.entity.LicenseValidity;
import com.wosai.mc.mapper.BankHolderIdValidityMapper;
import com.wosai.mc.mapper.LicenseLegalPersonIdValidityMapper;
import com.wosai.mc.mapper.LicenseValidityMapper;
import com.wosai.mc.utils.SmsBiz;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.merchant.audit.api.model.MerchantAudit;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

public class LicenseValidityScheduleTest extends DbBaseTest {

    @InjectMocks
    private LicenseValiditySchedule licenseValiditySchedule;

    @Mock
    private MerchantBusinessLicenseService licenseService;

    @Mock
    private UcUserAccountService ucUserAccountService;

    @Mock
    private ClientSideNoticeService clientSideNoticeService;

    @Mock
    private ClientSideSmsService clientSideSmsService;

    @Mock
    private RedisLock redisLock;

    @Mock
    private CommonBiz commonBiz;

    @Autowired
    private LicenseValidityMapper licenseValidityMapper;

    @Autowired
    private LicenseLegalPersonIdValidityMapper idValidityMapper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(licenseValiditySchedule, "licenseValidityMapper", licenseValidityMapper);
        ReflectionTestUtils.setField(licenseValiditySchedule, "idValidityMapper", idValidityMapper);
        Mockito.doReturn(new UcUserInfo()).when(ucUserAccountService).getUcUserById(anyString());
    }

    @Test
    public void businessLicenseValidity() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate today = LocalDate.now();

        String merchantId = "businessLicenseValidityMerchantId";
        LicenseValidity licenseValidity = new LicenseValidity();
        licenseValidity.setPt(getPt());
        licenseValidity.setMerchant_id(merchantId);
        licenseValidityMapper.insertSelective(licenseValidity);
        // mock一下
        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("sn", "sn", "id", "id"), "merchantUserInfo", new UcMerchantUserSimpleInfo().setUc_user_id("uc_user_id")))
                .when(commonBiz).baseJudge(merchantId);
        Map<String, Object> licenseInfo = new HashMap<>(1);
        Mockito.doReturn(licenseInfo).when(licenseService).getBusinessLicenseByMerchantId(merchantId);
        // 1 设置日期为已经过期
        licenseInfo.put(MerchantBusinessLicence.VALIDITY,"20011010-20211010");
        licenseValiditySchedule.businessLicenseValidity();

        // 2 设置日期为今天
        licenseInfo.put(MerchantBusinessLicence.VALIDITY, "20011010-" + today.format(formatter));
        licenseValiditySchedule.businessLicenseValidity();
        // 3 设置日志为今天 + 7天
        licenseInfo.put(MerchantBusinessLicence.VALIDITY, "20011010-" + today.plusDays(7).format(formatter));
        licenseValiditySchedule.businessLicenseValidity();
        // 4 设置日期为今天 + 60天
        licenseInfo.put(MerchantBusinessLicence.VALIDITY, "20011010-" + today.plusDays(60).format(formatter));
        licenseValiditySchedule.businessLicenseValidity();

        // 4 设置日期为今天 + 100天
        licenseInfo.put(MerchantBusinessLicence.VALIDITY, "20011010-" + today.plusDays(100).format(formatter));
        licenseValiditySchedule.businessLicenseValidity();
    }

    @Test
    public void legalPersonIdValidity() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate today = LocalDate.now();

        String merchantId = "legalPersonIdValidityMerchantId";
        LicenseLegalPersonIdValidity idValidity = new LicenseLegalPersonIdValidity();
        idValidity.setPt(getPt());
        idValidity.setMerchant_id(merchantId);
        idValidityMapper.insertSelective(idValidity);
        // mock一下
        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("sn", "sn", "id", "id"), "merchantUserInfo", new UcMerchantUserSimpleInfo().setUc_user_id("uc_user_id")))
                .when(commonBiz).baseJudge(merchantId);
        Map<String, Object> licenseInfo = new HashMap<>(1);
        Mockito.doReturn(licenseInfo).when(licenseService).getBusinessLicenseByMerchantId(merchantId);
        // 1 设置日期为已经过期
        licenseInfo.put(MerchantBusinessLicence.ID_VALIDITY,"20011010-20211010");
        licenseValiditySchedule.legalPersonIdValidity();

        // 2 设置日期为今天
        licenseInfo.put(MerchantBusinessLicence.ID_VALIDITY, "20011010-" + today.format(formatter));
        licenseValiditySchedule.legalPersonIdValidity();
        // 3 设置日志为今天 + 7天
        licenseInfo.put(MerchantBusinessLicence.ID_VALIDITY, "20011010-" + today.plusDays(7).format(formatter));
        licenseValiditySchedule.legalPersonIdValidity();
        // 4 设置日期为今天 + 60天
        licenseInfo.put(MerchantBusinessLicence.ID_VALIDITY, "20011010-" + today.plusDays(60).format(formatter));
        licenseValiditySchedule.legalPersonIdValidity();

        // 4 设置日期为今天 + 100天
        licenseInfo.put(MerchantBusinessLicence.ID_VALIDITY, "20011010-" + today.plusDays(100).format(formatter));
        licenseValiditySchedule.businessLicenseValidity();
    }

    private String getPt(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate today = LocalDate.now();
        return today.format(formatter);
    }
}