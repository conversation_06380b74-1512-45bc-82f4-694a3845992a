package com.wosai.mc.schedule;

import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.DbBaseTest;
import com.wosai.mc.biz.CommonBiz;
import com.wosai.mc.biz.RedisLock;
import com.wosai.mc.mapper.BankHolderIdValidityMapper;
import com.wosai.mc.entity.BankHolderIdValidity;
import com.wosai.mc.utils.SmsBiz;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.merchant.audit.api.model.MerchantAudit;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.mockito.ArgumentMatchers.*;

public class BankHolderValidityScheduleTest extends DbBaseTest {

    @InjectMocks
    private BankHolderValiditySchedule bankHolderValiditySchedule;

    @Mock
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Mock
    private UcUserAccountService ucUserAccountService;

    @Mock
    private ClientSideNoticeService clientSideNoticeService;

    @Mock
    private ClientSideSmsService clientSideSmsService;

    @Mock
    private RedisLock redisLock;

    @Mock
    private CommonBiz commonBiz;

    @Autowired
    private BankHolderIdValidityMapper validityMapper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(bankHolderValiditySchedule, "validityMapper", validityMapper);
        Mockito.doReturn(new UcUserInfo()).when(ucUserAccountService).getUcUserById(anyString());
    }

    @Test
    public void bankHolderValidity() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate today = LocalDate.now();

        String merchantId = "bankHolderIdValidityMerchantId";
        BankHolderIdValidity bankHolderIdValidity = new BankHolderIdValidity();
        bankHolderIdValidity.setPt(getPt());
        bankHolderIdValidity.setMerchant_id(merchantId);
        validityMapper.insertSelective(bankHolderIdValidity);
        // mock一下
        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(CollectionUtil.hashMap("merchant", CollectionUtil.hashMap("sn", "sn", "id", "id"), "merchantUserInfo", new UcMerchantUserSimpleInfo().setUc_user_id("uc_user_id")))
                .when(commonBiz).baseJudge(merchantId);
        MerchantBankAccount merchantBankAccount = new MerchantBankAccount();
        Mockito.doReturn(merchantBankAccount).when(bankBusinessLicenseService).getMerchantBankAccountByMerchantId(merchantId);
        // 1 设置日期为已经过期
        merchantBankAccount.setId_validity("********-********");
        bankHolderValiditySchedule.bankHolderValidity();

        // 2 设置日期为今天
        merchantBankAccount.setId_validity("********-" + today.format(formatter));
        bankHolderValiditySchedule.bankHolderValidity();
        // 3 设置日志为今天 + 7天
        merchantBankAccount.setId_validity("********-" + today.plusDays(7).format(formatter));
        bankHolderValiditySchedule.bankHolderValidity();
        // 4 设置日期为今天 + 60天
        merchantBankAccount.setId_validity("********-" + today.plusDays(60).format(formatter));
        bankHolderValiditySchedule.bankHolderValidity();
        // 4 设置日期为今天 + 100天
        merchantBankAccount.setId_validity("********-" + today.plusDays(100).format(formatter));
        bankHolderValiditySchedule.bankHolderValidity();
    }

    private String getPt(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate today = LocalDate.now();
        return today.format(formatter);
    }
}