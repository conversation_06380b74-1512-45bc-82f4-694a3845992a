spring:
  application:
    name : merchant-center-service
  kafka:
    producer:
      bootstrap-servers: **************:9092,**************:9092,**************:9092
      group-id: merchant-center
      registry-servers: http://**************:8081,http://**************:8081,http://**************:8081
    consumer:
      bootstrap-servers: **************:9092,**************:9092,**************:9092
      group-id: merchant-center
      registry-servers: http://**************:8081,http://**************:8081,http://**************:8081
  datasource:
    url: jdbc:h2:mem:db;MODE=MySQL;TRACE_LEVEL_SYSTEM_OUT=2
    driver-class-name: org.h2.Driver
    username: sa
    password: sa
  redis:
    host: r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    database: 4
    password: roFXzHwXPY3RnI%5
    timeout: 1000s  # 数据库连接超时时间，2.0 中该参数的类型为Duration，这里在配置的时候需要指明单位
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
      shutdown-timeout: 100 # 关闭超时时间
server:
  port : 8080

jsonrpc:
  core-business: http://core-business.beta.iwosai.com
  merchant-user: http://merchant-user-service.beta.iwosai.com
  merchant-audit: http://merchant-audit-service.beta.iwosai.com
  app-backend: http://app-backend-service.beta.iwosai.com
  sales-system-poi: http://sales-system-poi.beta.iwosai.com
  video-service: http://video-service.beta.iwosai.com
  bank-info: http://bank-info-service.beta.iwosai.com
  crow-service: http://192.168.101.149:18081
  merchant-bank: http://merchant-bank-service.beta.iwosai.com
  crm-customer-relation: http://crm-customer-relation.beta.iwosai.com
mybatis:
  mapper-locations: classpath:mapper/*.xml
  config-location: classpath:mybatis-config.xml


sms:
  url: http://sms-gateway.beta.iwosai.com/sms/send
  readTimeout: 5000
  connectTimeout: 5000
  # 验证码过期时间，单位分钟
  expireTime: 30


license:
  invite:
    dev_code: D3ZWFFMUFAEM
    template_code: TUNOPC8EDKGP
    msp_template_code: XTQBJWX47WCE

sensor:
  topic:
    createMerchant: events.upay-core.merchant-center.create-merchant
    createStore: events.upay-core.merchant-center.create-store
    changeMerchant: events.upay-core.merchant-center.change-merchant
    changeStore: events.upay-core.merchant-center.change-store

# apollo
app:
  id: merchant-center # 使用的 Apollo 的项目（应用）编号,等同于 classPath:/META_INF/app.properties
apollo:
  bootstrap:
    enabled: true
    # will inject 'application' and 'TEST1.apollo' namespaces in bootstrap phase
    namespaces: application

tag:
  id: f85eef13-e897-4f49-b258-ed9c9dbde011

crm:
  indirect:
    customer_relation: merchantorg


path:
  app:
    licenceV2: https://upay-info-app.iwosai.com/license?token=:token