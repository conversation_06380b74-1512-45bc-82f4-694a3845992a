SET MODE MYSQL;
CREATE TABLE IF NOT EXISTS license_validity
(
    id          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    merchant_id varchar(40),
    diff        varchar(10),
    pt          varchar(10),
    PRIMARY KEY (id),
    KEY idx_pt0 (pt)
);
CREATE TABLE IF NOT EXISTS license_legal_person_id_validity
(
    id          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    merchant_id varchar(40),
    diff        varchar(10),
    pt          varchar(10),
    PRIMARY KEY (id),
    KEY idx_pt1 (pt)
);
CREATE TABLE IF NOT EXISTS bank_holder_id_validity
(
    id          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    merchant_id varchar(40),
    diff        varchar(10),
    pt          varchar(10),
    PRIMARY KEY (id),
    KEY idx_pt2 (pt)
);