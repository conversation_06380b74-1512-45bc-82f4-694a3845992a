package com.wosai.mc.service.crm;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.dto.req.*;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import java.util.Map;

/**
 * crm-商户服务
 *
 * <AUTHOR>
 * @date 2025/1/15 09:34
 */
@JsonRpcService(value = "rpc/crm/merchant")
@Validated
public interface CrmMerchantService {

    MerchantInfo getMerchantInfoByMerchantId(ApiRequestParam<MerchantIdReqDTO, Map<String, Object>> applyReqDTO);
}
