package com.wosai.mc.service.common;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.app.LegalIdTypeModel;
import com.wosai.mc.model.app.LicenseTypeModel;
import com.wosai.mc.model.app.TradeLicenseTypeModel;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Validated
@JsonRpcService("/rpc/common/enum")
public interface CommonEnumService {

    /**
     * 查询营业执照类型枚举
     * @return 营业执照类型枚举
     */
    Map<Integer, LicenseTypeModel>  queryLicenseTypes();

    /**
     * 查询法人证件类型枚举
     * @return 法人证件类型枚举
     */
    Map<Integer, LegalIdTypeModel> queryLegalIdTypes();

    /**
     * 查询许可证类型枚举
     * @return 许可证类型枚举
     */
    Map<Integer, TradeLicenseTypeModel> queryTradeLicenseTypes();
}
