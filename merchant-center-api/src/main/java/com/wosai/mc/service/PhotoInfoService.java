package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.PhotoInfo;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@JsonRpcService(value = "rpc/photoinfo")
@Validated
public interface PhotoInfoService {

    /**
     * 根据id查询图片
     * 1、先查mc_pre表，为了兼容线上数据
     * 2、根据id和dev_code去查照片
     * 3、第一次创建的照片没有dev_code，所以若没有更新根据2查不到数据，要根据id去查一遍
     * @param id
     * @return
     */
    PhotoInfo getPhotoInfoByIdAndDevcode(@NotBlank(message = "id不能为空") String id, String devCode);

    /**
     * 更新图片
     *
     * @param photoInfo
     * @return
     */
    String updatePhotoInfo(@Valid PhotoInfo photoInfo);

}
