package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.McListResult;
import com.wosai.mc.model.app.ManagePassTokenReq;
import com.wosai.mc.model.app.req.ChangeDataReq;
import com.wosai.mc.model.app.req.StoreBasicInfoQueryReq;
import com.wosai.mc.model.app.req.StoreIdQueryReq;
import com.wosai.mc.model.app.req.StoreListQueryReq;
import com.wosai.mc.model.app.resp.MergeStoreDataResp;
import com.wosai.mc.model.app.resp.StoreBasicInfoQueryResp;
import com.wosai.mc.model.app.resp.StoreListQueryResp;
import com.wosai.mc.model.app.resp.StorePhotosQueryResp;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@JsonRpcService(value = "rpc/app/store")
@Validated
public interface AppStoreService {

    /**
     * 查询门店列表  app中店铺管理中的店铺列表
     *
     * @param storeListQueryReq 查询入参
     * @return 分页查询结果
     */
    McListResult<StoreListQueryResp> queryStoreList(@Valid StoreListQueryReq storeListQueryReq);

    /**
     * 查询门店基本信息
     *
     * @param storeBasicInfoQueryReq 查询入参
     * @return 门店基本信息
     */
    StoreBasicInfoQueryResp queryStoreBasicInfo(@Valid StoreBasicInfoQueryReq storeBasicInfoQueryReq);


    /**
     * @return 返回首家门店的基本信息
     */
    StoreBasicInfoQueryResp queryOldestStoreBasicInfo(@Valid ManagePassTokenReq managePassTokenReq);

    /**
     * 查询门店照片
     *
     * @param storeIdQueryReq 查询入参
     * @return 门店照片信息
     */
    StorePhotosQueryResp queryStorePhotos(@Valid StoreIdQueryReq storeIdQueryReq);

    /**
     * 判断店铺是否开通了外卖或点单
     *
     * @param storeIdQueryReq 查询入参
     * @return true开通  false未开通
     */
    boolean checkStoreOpenedTakeawayOrOrder(@Valid StoreIdQueryReq storeIdQueryReq);


    /**
     * @param req
     * @return
     */
    MergeStoreDataResp getNeedMergeStoreList(@Valid StoreListQueryReq req);


    /**
     * @param changeDataReq
     */
    void changeData(@Valid ChangeDataReq changeDataReq);


}
