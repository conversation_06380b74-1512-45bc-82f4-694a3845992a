package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.StoreBusinessLicenseInfo;
import com.wosai.mc.model.req.CreateStoreBusinessLicenseReq;
import com.wosai.mc.model.req.UpdateStoreBusinessLicenseReq;
import com.wosai.mc.model.req.VerifyStoreBusinessLicenseReq;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@JsonRpcService(value = "rpc/store_business_license")
@Validated
public interface StoreBusinessLicenseService {
    /**
     * 创建门店营业执照(支持同时传入许可证信息)
     */
    StoreBusinessLicenseInfo createStoreBusinessLicense(@Valid CreateStoreBusinessLicenseReq req);

    /**
     * 更新门店营业执照,传devCode为更新到mc_pre表，不传更新到原表
     * store_id 和 id 必须有其中之一,才可以确定更新哪个门店营业执照  都有时以id为准,且无法对store_id更新
     */
    int updateStoreBusinessLicense(@Valid UpdateStoreBusinessLicenseReq req, String devCode);

    /**
     * 根据id删除营业执照(包括了许可证)
     */
    int deleteStoreBusinessLicenseById(@NotBlank(message = "ID不可为空") String id);

    /**
     * 根据门店id删除营业执照(包括了许可证)
     */
    int deleteStoreBusinessLicenseByStoreId(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     * 根据storeId和devcode查询营业执照,查询原表devcode传null
     * 如果查询中间表,但中间表没有数据,返回原表数据
     */
    StoreBusinessLicenseInfo getStoreBusinessLicenseByStoreId(@NotBlank(message = "storeId不可为空") String storeId, String devCode);

    /**
     * 根据门店id迁移中间表信息到门店营业执照表
     */
    int copyMcInfoToStoreBusinessLicenseByStoreId(@NotBlank(message = "门店id不能为空") String storeId, @NotBlank(message = "devcode不能为空") String devCode);

    /**
     * 认证门店营业执照
     */
    int verifyStoreBusinessLicense(@Valid VerifyStoreBusinessLicenseReq req);


    int storeBusinessLicenseVerifySuccess(@NotBlank(message = "门店ID不能为空") String storeId);



}
