package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.StoreExtInfo;
import com.wosai.mc.model.req.StoreExtReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/storeExt")
@Validated
public interface StoreExtService {
    /**
     * 更新门店环境,传devCode为更新到mc_pre表，不传更新到原表
     *
     * @param storeExtReq
     * @return
     */
    int updateStoreExt(@Valid StoreExtReq storeExtReq, String devCode);

    /**
     * 根据id和devcode查询门店环境,查询原表devcode传null
     *
     * @param id
     * @return
     */
    StoreExtInfo findStoreExt(@NotBlank(message = "id不能为空") String id, String devCode);

    /**
     * 根据门店id和devcode查询门店环境,查询原表devcode传null
     *
     * @param storeId
     * @return
     */
    StoreExtInfo findStoreExtByStoreId(@NotBlank(message = "storeId不能为空") String storeId, String devCode);

    /**
     * 根据门店id获取最新的门店环境信息
     *
     * @param storeId 门店id
     * @return 门店环境信息
     */
    StoreExtInfo findLastStoreExtByStoreId(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     * 根据storeId和devcode查询门店环境和各种图片,查询原表devcode传null，图片不进mc_pre表不需要查询mc_pre
     *
     * @param storeId
     * @return
     */
    StotreExtInfoAndPictures findStoreExtAndPicturesByStoreId(@NotBlank(message = "storeId不能为空") String storeId, String devCode);

    /**
     * 根据storeId查询最新的门店环境和各种图片，供crm使用
     * 逻辑：查询最新的storeExt数据，根据storeExt里面的图片id获取图片
     *
     * @param storeId
     * @return
     */
    StotreExtInfoAndPictures findLastStoreExtAndPicturesByStoreId(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     * 查询商户第一家门店的信息
     * 如果商户下没有门店，返回null
     * 如果商户第一家门店没有storeId，返回null
     *
     * @param merchantId
     * @return
     */
    StotreExtInfoAndPictures findLastStoreExtAndPicturesByMerchantId(@NotBlank(message = "merchantId不能为空") String merchantId);

    /**
     * 根据门店id批量获取门头照
     * @param storeIds 门店id集合
     * @return 格式为 {门店id : 门头照url} 的map集合
     */
    Map<String,String> getBrandOnlyScenePhotoBatchByStoreIds(List<String> storeIds);


}
