package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-09-20
 */
@JsonRpcService(value = "rpc/support")
public interface SupportService {

    /**
     * 解密
     *
     * @param dataMapList
     * @param fields
     * @return
     */
    List<Map<String, Object>> decrypt(List<Map<String, Object>> dataMapList, List<String> fields);

    /**
     * 解密
     *
     * @param texts
     * @return
     */
    List<String> decryptList(List<String> texts);

    /**
     * 加密
     *
     * @param texts
     * @return
     */
    List<String> encryptList(List<String> texts);

    /**
     * 控制洗数据运行
     * @param emptyMap
     */
    void pollConfig(Map emptyMap);
}
