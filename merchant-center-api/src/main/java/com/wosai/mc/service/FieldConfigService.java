package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.FieldConfig;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

@Validated
@JsonRpcService("/rpc/field")
public interface FieldConfigService {

    List<FieldConfig> queryFieldConfigs(@NotNull(message = "查询的配置表不可为空") List<String> tableNames);
}
