package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.req.CreateLicenseReq;
import com.wosai.mc.model.req.UpdateLicenseReq;
import com.wosai.mc.model.req.VerifyLicenseReq;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@JsonRpcService(value = "rpc/license")
@Validated
public interface LicenseService {

    int saveLicense(@Valid CreateLicenseReq license);

    /**
     * 根据 门店营业执照Id  获取该营业执照下的所有的许可证信息
     * 该接口如查中间表,但没数据时,返回原表数据
     */
    List<Map> getLicenseByBusinessLicenseId(@NotBlank(message = "营业执照id不能为空") String businessLicenseId, String dev_code);


    /**@param businessId 许可证对应的营业执照id
     *     注意:该接口会删除老数据
     * dev_code不为空更新到中间表,  否则更新到原表
     */
    int updateLicense(@Valid List<UpdateLicenseReq> reqs, String businessId, String dev_code);

    /**
     * 根据id删除许可证
     */
    int deleteLicenseById(@NotBlank(message = "id不能为空") String id);

    /**
     * 根据门店营业执照id,删除名下的所有许可证
     */
    int deleteAllLicenseByBusinessLicenseId(@NotBlank(message = "id不能为空") String businessLicenseId);

    /**
     * 认证营业执照
     * id  和  营业执照id 有一即可
     * id不为空时,代表只对一个具体许可证更新 此时营业执照id失效
     * <p>
     * 营业执照id不为空时,代表对名下所有许可证更新 ,有id时,该字段失效
     */
    int verifyLicense(@Valid VerifyLicenseReq req);

    /**
     * 合并中间表数据到原表
     */
    int copyMcInfoToLicenseByBusinessLicenseId(@NotBlank(message = "营业执照id不能为空") String businessLicenseId, @NotBlank(message = "devcode不能为空") String devCode);

}
