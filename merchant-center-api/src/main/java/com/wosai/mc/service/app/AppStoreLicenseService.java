package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.CommonResultResp;
import com.wosai.mc.model.CommonStatusResp;
import com.wosai.mc.model.app.req.*;
import com.wosai.mc.model.app.resp.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@JsonRpcService("/rpc/app/storeLicense")
@Validated
public interface AppStoreLicenseService {

    /**
     * 查询门店营业执照认证状态
     *
     * @param storeIdQueryReq
     * @return
     */
    StoreLicenseAuditStatusQueryResp queryStoreLicenseAuditStatus(@Valid StoreIdQueryReq storeIdQueryReq);

    /**
     * 判断是否展示门店营业执照栏位
     *
     * @param storeIdQueryReq 请求参数
     * @return true展示  false不展示
     */
    CommonResultResp shouldDisplayStoreLicense(@Valid StoreIdQueryReq storeIdQueryReq);

    /**
     * 判断是否允许复用商户层级营业执照
     *
     * @param storeIdQueryReq 请求参数
     * @return true允许复用 false不允许复用
     */
    CommonResultResp allowReuseMerchantLicense(@Valid StoreIdQueryReq storeIdQueryReq);

    /**
     * 查询门店营业执照审核结果
     *
     * @param storeIdQueryReq 请求参数
     * @return 门店营业执照审核结果
     */
    CommonStatusResp queryStoreLicenseAuditResult(@Valid StoreIdQueryReq storeIdQueryReq);

    /**
     * 查询门店营业执照对应的草稿信息
     * @param req 请求参数
     * @return 草稿信息
     */
    Map<String, Object> queryStoreLicenseDraft(@Valid StoreIdQueryReq req);

    /**
     * 取消门店营业证照审核申请
     *
     * @param storeIdOpReq 请求参数
     * @return 取消结果
     */
    CommonResultResp cancelStoreLicenseAudit(@Valid StoreIdOpReq storeIdOpReq);

    /**
     * 保存草稿信息
     *
     * @param req 请求参数
     * @return 0保存校验失败  1保存成功
     */
    CommonResultResp saveStoreLicenseDraft(@Valid StoreLicenseDraftSaveReq req);

    /**
     * 校验草稿信息, 返回草稿校验状态 0待填写 1已填写 2已认证
     * @param req 请求参数
     * @return 各个信息的校验结果
     */
    StoreLicenseDraftCheckResp checkStoreLicenseDraft(@Valid StoreIdOpReq req);

    /**
     * 提交营业证照审核
     * @param req 请求参数
     * @return true提交成功  false提交失败
     */
    CommonResultResp submitStoreLicenseAudit(@Valid StoreIdOpReq req);


    /**
     * 门店是否复用商户营业执照
     * @param req 请求参数
     *
     */
    CommonResultResp whetherReuseMerchantLicense(@Valid MerchantLicenseReuseReq req);

    /**
     * 查询验证通过的门店营业证照信息
     * @param req 请求参数
     * @return 证照信息
     */
    VerifiedStoreLicenseQueryResp queryVerifiedStoreLicense(@Valid VerifiedLicenseInfoQueryReq req);

    /**
     * 查询认证通过的门店营业证照法人信息
     * @param req 请求参数
     * @return 法人信息
     */
    VerifiedStoreLicenseIdCardQueryResp queryVerifiedStoreLicenseIdCard(@Valid VerifiedLicenseInfoQueryReq req);

    /**
     * 查询认证通过的门店营业执照许可证信息
     * @param req 请求参数
     * @return 许可证信息
     */
    List<VerifiedStoreTradeLicenseQueryResp> queryVerifiedStoreTradeLicenses(@Valid VerifiedLicenseInfoQueryReq req);

    /**
     * 申请营业执照变更
     * @param req 请求参数
     */
    void applyChangeStoreLicense(@Valid StoreLicenseChangeApplyReq req);

    /**
     * 提交营业执照变更
     * @param req 请求参数
     * @return true成功  false失败
     */
    CommonResultResp submitStoreLicenseChangeApply(@Valid StoreIdOpReq req);

    /**
     * 判断是否是变更申请
     * @param req 请求参数
     * @return true是变更申请  false不是
     */
    boolean checkIsStoreLicenseChangeApply(@Valid StoreIdQueryReq req);
}
