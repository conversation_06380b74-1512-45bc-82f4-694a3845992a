package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.FieldConfig;
import com.wosai.mc.model.app.req.FieldConfigReq;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

@Validated
@JsonRpcService("/rpc/app/field")
public interface AppFieldConfigService {

    List<FieldConfig> queryFieldConfigs(@Valid FieldConfigReq fieldConfigReq);

}
