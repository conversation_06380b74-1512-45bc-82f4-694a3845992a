package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.app.req.BindStoreReq;
import com.wosai.mc.model.app.req.MerchantQueryReq;
import com.wosai.mc.model.app.req.UnBindStoreReq;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

@Validated
@JsonRpcService(value = "rpc/app/merchant")
public interface AppMerchantService {

    /**
     * 解绑商户绑定的门店id,如果还带了地址信息,则可以更新地址信息
     */
    boolean unBindStoreId(UnBindStoreReq unBindStoreReq);


    /**
     * 绑定门店id,将地址信息同步到商户
     */
    boolean bindStoreId(@Valid BindStoreReq bindStoreReq);


    /**
     * 查询商户是否开通了间联扫码
     */
    boolean isOpenedPay(@Valid BaseAppReq req);

    String getMerchantName(@Valid MerchantQueryReq req);
}
