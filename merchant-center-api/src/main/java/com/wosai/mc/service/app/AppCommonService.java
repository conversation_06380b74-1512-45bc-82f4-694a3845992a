package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.app.LegalIdTypeModel;
import com.wosai.mc.model.app.LicenseTypeModel;
import com.wosai.mc.model.app.req.BusinessLicenseInfoQueryReq;
import com.wosai.mc.model.app.req.LegalIdTypesQueryReq;
import com.wosai.mc.model.app.req.LicenseTypeQueryReq;
import com.wosai.mc.model.app.resp.BusinessLicenseInfoQueryResp;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Validated
@JsonRpcService("/rpc/app/common")
public interface AppCommonService {

    /**
     * 查询商户支持的营业执照信息列表
     * @param appReq 请求参数
     * @return 营业执照列表
     */
    List<LicenseTypeModel> queryMerchantSupportLicenseTypes(@Valid BaseAppReq appReq);

    /**
     * 查询营业执照允许的法人证件类型
     * @param legalIdTypesQueryReq 请求参数
     * @return 法人证件类型列表
     */
    List<LegalIdTypeModel> queryLicenseSupportLegalIdTypes(@Valid LegalIdTypesQueryReq legalIdTypesQueryReq);

    /**
     * 查询商户支持的许可证类型列表
     * @param appReq 请求参数
     * @return 许可证列表
     */
    List<LicenseTypeModel> queryMerchantSupportTradeLicenseTypes(@Valid BaseAppReq appReq);

    /**
     * 根据证照号查询对应的营业执照类型
     * @param req
     * @return
     */
    Integer queryLicenseType(@Valid LicenseTypeQueryReq req);

    /**
     * 查询第三方营业执照信息
     * @param req 请求参数
     * @return 营业执照信息
     */
    BusinessLicenseInfoQueryResp queryBusinessLicenseInfo(@Valid BusinessLicenseInfoQueryReq req);

    /**
     * 查询营业执照许可证信息
     * @param baseAppReq 请求参数
     * @return 所有的许可证信息
     */
    List<LicenseTypeModel> queryAllTradeLicenses(@Valid BaseAppReq baseAppReq);

    /**
     * 查询字段规则配置
     * @param appReq 请求参数
     * @return 字段规则配置
     */
    Map<String, Object> queryFieldRuleConfig(@Valid BaseAppReq appReq);
}
