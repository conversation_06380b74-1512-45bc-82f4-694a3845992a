package com.wosai.mc.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.*;
import com.wosai.mc.model.resp.*;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.validation.validator.PropNotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 商户相关接口
 *
 * <AUTHOR>
 * @date 2020-08-04
 */
@JsonRpcService(value = "rpc/merchant")
@Validated
public interface MerchantService {

    /**
     * 创建商户完整信息
     *
     * @param merchantComplete
     * @return
     */
    CreateMerchantResp createMerchantComplete(@Valid MerchantComplete merchantComplete);

    /**
     * 更新商户完整信息
     *
     * @param merchantComplete
     * @return
     */
    CreateMerchantResp updateMerchantComplete(@Valid UpdateMerchantComplete merchantComplete);

    /**
     * 创建商户和门店信息的接口
     *
     * @param req
     * @return
     */
    CreateMerchantResp createMerchantAndStore(@Valid CreateMerchantAndStoreReq req);

    /**
     * 更新商户和门店信息的接口
     *
     * @param req
     * @return
     */
    CreateMerchantResp updateMerchantAndStore(@Valid UpdateMerchantAndStoreReq req);

    /**
     * 创建商户
     *
     * @param req
     * @return
     */
    MerchantInfo createMerchant(@Valid CreateMerchantReq req);

    /**
     * 更新商户,传devCode为更新到mc_pre表，不传更新到原表
     *
     * @param req
     * @param devCode
     * @return
     */
    int updateMerchant(@Valid UpdateMerchantReq req, String devCode);


    int updateMerchantV2(@Valid UpdateMerchantReq req, String devCode, LogReq logReq);


    /**
     * 查询商户
     *
     * @param merchantId
     * @return
     */
    MerchantInfo getMerchantById(@NotBlank(message = "商户id不能为空") String merchantId, String devCode);

    /**
     * 根据商户id列表查询商户列表
     *
     * @param merchantIds
     * @return
     */
    List<MerchantInfo> getMerchantListByMerchantIds(@NotEmpty(message = "商户id列表不能为空") List<String> merchantIds);

    /**
     * 根据id查询最近修改后的商户
     *
     * @param merchantId
     * @return
     */
    MerchantInfo getLatestMerchantById(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 查询商户
     *
     * @param merchantSn
     * @return
     */
    MerchantInfo getMerchantBySn(@NotBlank(message = "商户号不能为空") String merchantSn, String devCode);

    /**
     * 认证商户
     *
     * @param req
     * @return
     */
    int verifyMerchant(@Valid VerifyMerchantReq req);

    /**
     * 根据商户id获取商户字段认证状态
     *
     * @param merchantId 商户id
     * @return 商户认证字段状态
     */
    FieldStatusResp getMerchantVerifyFieldStatus(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 根据商户id删除商户
     *
     * @param merchantId
     * @return
     */
    int deleteMerchantByMerchantId(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 根据商户id迁移商户门店图片
     *
     * @param merchantId
     * @return
     */
    int moveStoreImageByMerchantId(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 根据商户id迁移中间表信息到商户表
     * 迁移商户表
     *
     * @param merchantId
     * @return
     */
    int copyMcInfoToMerchantByMerchantId(@NotBlank(message = "商户id不能为空") String merchantId, @NotBlank(message = "devcode不能为空") String devCode);

    /**
     * 根据商户id迁移中间表信息到商户表 + 商户日志
     * 迁移商户表
     *
     * @param merchantId
     * @return
     */
    int copyMcInfoToMerchantByMerchantIdWithLog(@NotBlank(message = "商户id不能为空") String merchantId, @NotBlank(message = "devcode不能为空") String devCode, LogParamsDto dto);


    /**
     * 根据商户id迁移中间表信息到原表
     * 迁移商户、营业执照、门店三张表
     *
     * @param merchantId
     * @return
     */
    int copyMcInfoToOriginalByMerchantId(@NotBlank(message = "商户id不能为空") String merchantId, @NotBlank(message = "devcode不能为空") String devCode);

    /**
     * 申诉通过后解除风控黑名单
     *
     * @param merchantId
     * @return
     */
    int appealSuccess(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 根据营业执照号或者是身份证号查询账号信息和商户信息
     *
     * @param req
     * @return
     */
    List<MerchantAndUcUserInfo> getMerchantAndUserInfo(GetMerchantAndLicenseReq req);

    /**
     * 通过证件号查询是否有可以绑定的账号信息
     *
     * @param identity 证件号
     * @return 可以绑定的账号信息列表
     */
    List<BindAccountResp> getBindAccountByIdentity(@NotBlank(message = "证件号不能为空") String identity);

    /**
     * 通过证件号查询是否有可以绑定的账号信息V2
     * 支持按组织过滤
     *
     * @param req 证件号
     * @return 可以绑定的账号信息列表
     */
    List<BindAccountResp> getBindAccountByIdentityV2(@Valid GetBindAccountByIdentityReq req);

    /**
     * 命中黑名单后将涉及不能修改的字段在extra中black标记为true
     *
     * @param merchantId
     * @return
     */
    int hitBlacklist(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 根据手机号查询是否有超级管理员对应的商户信息
     *
     * @param cellphone 手机号
     * @return 没有返回空数组
     */
    List<MerchantInfo> getMerchantInfoByCellphone(@NotBlank(message = "手机号不能为空") String cellphone);

    /**
     * 新增查询该手机号是否可以绑定
     * 变更查询该手机号是否可以绑定
     *
     * @param merchantId 商户id
     * @param cellphone  手机号
     * @param identity   证件号
     * @return 是否可以绑定
     */
    boolean allowBindAccount(String merchantId, @NotBlank(message = "手机号不能为空") String cellphone, @NotBlank(message = "证件号不能为空") String identity);

    /**
     * 新增查询该手机号是否可以绑定
     * 变更查询该手机号是否可以绑定
     * 支持按组织过滤
     *
     * @param req
     * @return 是否可以绑定
     */
    boolean allowBindAccountV2(@Valid AllowBindAccountReq req);

    /**
     * 店铺照片补充/更新审批自动化  前置校验（商户认证通过、商户状态正常、商户间连扫码正常,门店是否与商户匹配）
     *
     * @param params
     * @return 校验不通过时, 抛出异常
     */
    void storePhotoPreCheckStatus(Map params);

    /**
     * 更新商户类型(老板角色才可以修改)
     *
     * @param params
     */
    void updateMerchantTypeByApp(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id"),
            @PropNotEmpty(value = "merchant_user_id"),
            @PropNotEmpty(value = "type")
    }) Map params);

    /**
     * 获取商户真实性审核驳回的字段、原因和建议
     *
     * @param merchantId 商户id
     * @return 驳回字段、原因和建议的列表
     */
    List<RejectFieldAndSuggest> getRejectFieldAndSuggest(@NotBlank(message = "商户id不能为空") String merchantId);

    /**
     * 商户省市区信息为空或者错误，可通过此接口尝试修复，但不保证能修复
     * 取第一家门店信息修复，若第一家门店信息也不全，则无法修复
     * 门店省市区数据修复请看 StoreService.fillStoreDistrict
     *
     * @param merchantSn
     * @return
     */
    void fillMerchantDistrict(@NotBlank(message = "商户号不能为空") String merchantSn);

    /**
     * 校验能否切换收单机构-商户是否有附属关系
     *
     * @param request 请求参数
     * @return 是否允许
     */
    ChangeAcquirerResp checkMerchantAffiliation(@Valid @NotNull(message = "请求参数不能为空") ChangeAcquirerReq request);


    /**
     * 删除商户中间表数据
     */
    void delMcMerchant(@NotBlank(message = "商户id不能为空") String merchantId);


    /**
     * 根据merchantId关闭商户 + 商户日志
     *
     * @param merchantId
     */
    void closeMerchantWithLog(
            @NotNull(message = "商户ID不可为空")
            String merchantId, LogParamsDto dto);

    void disableMerchantWithLog(@NotNull(message = "商户ID不可为空")
                                String merchantId, LogParamsDto dto);

    /**
     * 根据merchantId启用商户 + 商户日志
     *
     * @param merchantId
     */
    void enableMerchantWithLog(@NotNull(message = "商户ID不可为空") String merchantId, LogParamsDto dto);

    /**
     * 是否可以提交对公账户审核
     * 如果不可以，抛出异常（适配审批中心接口标准）
     *
     * @param paramsMap 参数
     */
    void isCanSubmitCorporateReceiptAudit(Map paramsMap);

    /**
     * 是否可以提交营业执照公示网截图审核
     * 如果不可以，抛出异常（适配审批中心接口标准）
     *
     * @param paramsMap 参数
     */
    void isCanSubmitLicensePubScreenshotAudit(Map paramsMap);
}
