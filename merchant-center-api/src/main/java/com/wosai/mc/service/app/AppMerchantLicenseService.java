package com.wosai.mc.service.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.app.resp.MerchantLicenseQueryResp;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Validated
@JsonRpcService("/rpc/app/merchantLicense")
public interface AppMerchantLicenseService {

    /**
     * 查询商户营业执照信息
     * @param req 请求参数
     * @return 营业执照信息
     */
    MerchantLicenseQueryResp queryMerchantLicense(@Valid BaseAppReq req);
}
