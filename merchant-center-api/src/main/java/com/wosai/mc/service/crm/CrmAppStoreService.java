package com.wosai.mc.service.crm;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.mc.model.app.ManagePassTokenReq;
import com.wosai.mc.model.app.resp.StoreBasicInfoQueryResp;
import com.wosai.mc.model.dto.req.MerchantIdReqDTO;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import javax.validation.Valid;
import java.util.Map;

/**
 * crm app 门店服务
 */
@JsonRpcService(value = "rpc/crm-app/store")
@Validated
public interface CrmAppStoreService {

    /**
     * 获取首家门店的基本信息
     *
     * @return 返回首家门店的基本信息
     */
    StoreBasicInfoQueryResp queryOldestStoreBasicInfo(@Valid ApiRequestParam<MerchantIdReqDTO, Map<String, Object>> req);

}
