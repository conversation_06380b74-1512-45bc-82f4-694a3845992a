package com.wosai.mc.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
public enum DraftTypeEnum {

    BUSINESS_LICENSE("business_license_info", "营业执照信息"),
    ID_CARD("id_card_info", "法人证件信息"),
    TRADE_LICENSE("trade_license_info", "营业执照信息"),
    OTHER("other_info", "其他信息"),
    CHANGE_APPLY("change_apply", "是否来自于申请变更")

    ;


    @Getter
    private final String type;
    @Getter
    private final String desc;


    DraftTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @JsonCreator
    public static DraftTypeEnum ofField(String field) {
        DraftTypeEnum[] fields = DraftTypeEnum.values();
        for (DraftTypeEnum cursorField : fields) {
            if (cursorField.getType().equalsIgnoreCase(field)) {
                return cursorField;
            }
        }
        return null;
    }
}
