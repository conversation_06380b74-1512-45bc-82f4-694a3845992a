package com.wosai.mc.constants;

import java.util.Arrays;
import java.util.List;

public class MergeStoreConstant {


    /**
     * 选择智慧经营的
     */
    public static final Integer SMART = 1;
    /**
     * 选择商户中心的
     */
    public static final Integer CENTER = 2;


    /**
     * 外卖是否活跃
     */
    public static final Integer TAKEOUT_NOT_ACTIVE = 0;
    public static final Integer TAKEOUT_ACTIVE = 1;


    public static final Integer NOT_MERGE = 0;
    public static final Integer MERGED = 1;
    public static final Integer NO_NEED_MERGE = 2;
    public static final Integer NO_NEED_MERGE_HAD_APPLY = 3;
    public static final Integer NO_NEED_MERGE_HAD_EMOJI = 4;
    public static final Integer NO_NEED_MERGE_REG_FAIL = 5;
    public static final Integer NO_NEED_MERGE_EMPTY_ITUDE = 6;
    public static final List<Integer> MERGED_OR_NO_NEED_MERGE = Arrays.asList(MERGED, NO_NEED_MERGE, NO_NEED_MERGE_HAD_APPLY, NO_NEED_MERGE_HAD_EMOJI, NO_NEED_MERGE_REG_FAIL, NO_NEED_MERGE_EMPTY_ITUDE);

}
