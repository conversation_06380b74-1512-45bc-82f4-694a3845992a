package com.wosai.mc.constants;

/**
 * 结算账户类型
 */
public enum BankAccountEnum {
    PERSONAL("个人账户", 1),
    BUSINESS("企业账户", 2);
    private String name;

    private int code;

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    BankAccountEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (BankAccountEnum option : BankAccountEnum.values()) {
            if (option.getCode() == code) {
                return option.getName();
            }
        }
        return null;
    }
}
