package com.wosai.mc.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.Photo;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 营业执照DTO
 *
 * <AUTHOR>
 * @date 2025/1/15 11:26
 */
@Data
public class BusinessLicenseDTO {


    public static final Integer STATUS_UNCONFIRMED = 0;

    public static final Integer STATUS_CONFIRMED = 1;

    /**
     * 平台
     */
    private String platform;


    /**
     * 0-未确认  1-已确认
     */
    private Integer status;

    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    @JSONField(name = "merchant_id")
    private String merchantId;

    /**
     * 商户经营名称
     */
    @JsonProperty("business_name")
    @JSONField(name = "business_name")
    private String businessName;

    /**
     * 营业证照类型
     */
    private Integer type;

    /**
     * 营业证照照片
     */
    @JsonProperty("photo")
    @JSONField(name = "photo")
    @Photo
    private String licensePhoto;

    /**
     * 统一社会信用代码
     */
    private String number;

    /**
     * 营业证照名称
     */
    private String name;

    /**
     * 营业证照上的法人姓名
     */
    @JsonProperty("registered_legal_person_name")
    @JSONField(name = "registered_legal_person_name")
    private String legalPersonName;

    /**
     * 注册地址
     */
    @JsonProperty("address")
    @JSONField(name = "address")
    private String registerAddress;

    /**
     * 有效期
     */
    private String validity;

    /**
     * 法人证件类型
     */
    @JsonProperty("legal_person_id_type")
    @JSONField(name = "legal_person_id_type")
    private Integer legalPersonCertificateType;

    /**
     * 法人证件正面照
     */
    @JsonProperty("legal_person_id_card_front_photo")
    @JSONField(name = "legal_person_id_card_front_photo")
    @Photo
    private String legalPersonCertificateFrontPhoto;

    /**
     * 法人证件反面照
     */
    @JsonProperty("legal_person_id_card_back_photo")
    @JSONField(name = "legal_person_id_card_back_photo")
    @Photo
    private String legalPersonCertificateBackPhoto;

    /**
     * 法人证件号码
     */
    @JsonProperty("legal_person_id_number")
    @JSONField(name = "legal_person_id_number")
    private String legalPersonCertificateNumber;

    /**
     * 法人证件姓名
     */
    @JsonProperty("legal_person_name")
    @JSONField(name = "legal_person_name")
    private String legalPersonCertificateName;

    /**
     * 法人证件有效期
     */
    @JsonProperty("id_validity")
    @JSONField(name = "id_validity")
    private String legalPersonCertificateValidity;

    /**
     * 法人证件地址
     */
    @JsonProperty("legal_person_id_card_address")
    @JSONField(name = "legal_person_id_card_address")
    private String legalPersonCertificateAddress;

    /**
     * 法人证件签发机关
     */
    @JsonProperty("legal_person_id_card_issuing_authority")
    @JSONField(name = "legal_person_id_card_issuing_authority")
    private String legalPersonCertificateIssuingAuthority;

    /**
     * 辅助证明材料
     */
    @JsonProperty("auxiliary_proof_materials")
    @JSONField(name = "auxiliary_proof_materials")
    @Photo
    private String auxiliaryProofMaterials;

    /**
     * 备注
     */
    private String remark;


}
