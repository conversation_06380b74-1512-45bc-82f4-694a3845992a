package com.wosai.mc.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@Data
@Accessors(chain = true)
//@MappedJdbcTypes(JdbcType.VARCHAR)
//@MappedTypes(String.class)
public class FieldConfig {
    private Long id;

    private String table_name;

    private String filed_name;

    private String type;

    private String filed_type;

    private String regx;

    private String description;

    private String example;

    private String front_name;

    private String fail_msg;

    private String place_holder;

    private String explain_msg;
}