package com.wosai.mc.model.req;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class ChangeAcquirerReq {
    /**
     * 商户ID
     */
    private String merchant_id;
    /**
     * 商户SN
     */
    @NotBlank(message = "商户SN不能为空")
    private String merchant_sn;
    /**
     * 原收单机构
     */
    private String source_acquirer;
    /**
     * 目标收单机构
     */
    private String target_acquirer;
}
