package com.wosai.mc.model.dto.rsp;

import lombok.Data;

/**
 * 校验编辑营业执照token结果
 *
 * <AUTHOR>
 * @date 2025/1/15 14:03
 */
@Data
public class CheckEditBusinessLicenseTokenResultRspDTO {

    /**
     * 是否有效
     */
    private Boolean valid;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 1-token有效  2-token过期  3-token已变更
     */
    private Integer status;

    public static CheckEditBusinessLicenseTokenResultRspDTO tokenValid() {
        CheckEditBusinessLicenseTokenResultRspDTO rsp = new CheckEditBusinessLicenseTokenResultRspDTO();
        rsp.setValid(true);
        rsp.setStatus(1);
        return rsp;
    }

    public static CheckEditBusinessLicenseTokenResultRspDTO tokenExpired() {
        CheckEditBusinessLicenseTokenResultRspDTO rsp = new CheckEditBusinessLicenseTokenResultRspDTO();
        rsp.setValid(false);
        rsp.setStatus(2);
        rsp.setMessage("token已过期");
        return rsp;
    }

    public static CheckEditBusinessLicenseTokenResultRspDTO tokenChanged() {
        CheckEditBusinessLicenseTokenResultRspDTO rsp = new CheckEditBusinessLicenseTokenResultRspDTO();
        rsp.setValid(false);
        rsp.setStatus(3);
        rsp.setMessage("token已变更");
        return rsp;
    }

}
