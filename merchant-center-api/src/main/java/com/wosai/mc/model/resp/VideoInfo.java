package com.wosai.mc.model.resp;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class VideoInfo {
    private String requestId;
    private List<VideoInfo.PlayInfo> playInfoList;
    private VideoInfo.VideoBase videoBase;

    @Data
    @Accessors(chain = true)
    public static class PlayInfo {
        private Long width;
        private Long height;
        private Long size;
        private String playURL;
        private String bitrate;
        private String definition;
        private String duration;
        private String format;
        private String fps;
        private Long encrypt;
        private String plaintext;
        private String complexity;
        private String streamType;
        private String rand;
        private String jobId;
        private String preprocessStatus;
        private String watermarkId;
        private String status;
        private String creationTime;
        private String modificationTime;
        private String encryptType;
        private String narrowBandType;
        private String specification;
    }

    @Data
    @Accessors(chain = true)
    public static class VideoBase {
        private String outputType;
        private String coverURL;
        private String duration;
        private String status;
        private String title;
        private String videoId;
        private String mediaType;
        private String creationTime;
        private String transcodeMode;
        private List<VideoInfo.VideoBase.Thumbnail> thumbnailList;

        @Data
        @Accessors(chain = true)
        public static class Thumbnail {
            private String uRL;
        }
    }
}
