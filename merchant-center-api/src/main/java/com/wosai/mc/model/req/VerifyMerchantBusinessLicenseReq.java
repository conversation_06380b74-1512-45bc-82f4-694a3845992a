package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-12
 */
@Data
@Accessors(chain = true)
public class VerifyMerchantBusinessLicenseReq {

    @NotBlank(message = "商户id不能为空")
    private String merchantId;

    /**
     * 认证状态  2认证成功  3认证失败
     */
    @NotBlank(message = "认证状态不能为空")
    private String verifyStatus;
    /**
     * 认证的字段名称
     */
    @NotNull(message = "认证的字段名称不能为空")
    private List<String> verifyParams;

}
