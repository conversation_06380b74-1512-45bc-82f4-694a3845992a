package com.wosai.mc.model.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.AppCommonFieldDTO;
import com.wosai.mc.model.dto.BankAccountDTO;
import com.wosai.mc.model.dto.BusinessLicenseDTO;

/**
 * 营业执照变更校验换卡请求
 *
 * <AUTHOR>
 * @date 2025/3/16 10:22
 */
public class LicenseUpdateCheckBankChangeReqDTO extends AppCommonFieldDTO {


    @JsonProperty("change_type")
    @JSONField(name = "change_type")
    private String changeType;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("operator")
    private String operator;

    /**
     * 营业执照
     */
    private BusinessLicenseDTO businessLicense;

    /**
     * 结算信息
     */
    private BankAccountDTO bankAccount;

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public BusinessLicenseDTO getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(BusinessLicenseDTO businessLicense) {
        this.businessLicense = businessLicense;
    }

    public BankAccountDTO getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(BankAccountDTO bankAccount) {
        this.bankAccount = bankAccount;
    }
}
