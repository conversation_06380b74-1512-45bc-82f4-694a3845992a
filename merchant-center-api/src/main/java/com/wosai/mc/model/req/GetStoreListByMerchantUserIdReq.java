package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
public class GetStoreListByMerchantUserIdReq {
    @NotEmpty(message = "商户用户id不可为空")
    private String merchant_user_id;
//    @NotEmpty(message = "商户id不可为空")
//    private String merchant_id;

    /**
     * 门店状态
     */
    private List<Integer> status_list;

    private Boolean manual_order = false;

    /**
     * 商户门店号
     */
    private String client_sn;

    /**
     * 门店号
     */
    private String store_sn;

    /**
     * 门店名称
     */
    private String store_name;

    /**
     * 指定门店id集合
     */
    private List<String> designated_store_ids;


}
