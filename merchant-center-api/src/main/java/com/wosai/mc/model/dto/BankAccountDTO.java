package com.wosai.mc.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.Photo;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 银行账户信息DTO
 *
 * <AUTHOR>
 * @date 2025/1/15 11:26
 */
@Data
public class BankAccountDTO {

    public static final Integer STATUS_UNCONFIRMED = 0;

    public static final Integer STATUS_CONFIRMED = 1;

    /**
     * 0-未确认  1-已确认
     */
    private Integer status;


    /**
     * 银行卡照片
     */
    @JsonProperty("bank_card_image")
    @JSONField(name = "bank_card_image")
    @Photo
    private String bankCardPhoto;

    /**
     * 账户名称
     */
    @JsonProperty("holder")
    @JSONField(name = "holder")
    private String holder;

    /**
     * 银行卡号
     */
    @JsonProperty("number")
    @JSONField(name = "number")
    private String accountNumber;

    /**
     * 开户银行
     */
    @JsonProperty("bank_name")
    @JSONField(name = "bank_name")
    private String openingBank;

    /**
     * 开户城市
     */
    @JsonProperty("city")
    @JSONField(name = "city")
    private String openingCity;

    /**
     * 开户支行
     */
    @JsonProperty("branch_name")
    @JSONField(name = "branch_name")
    private String openingBranch;

    /**
     * 开户支行号
     */
    @JsonProperty("opening_number")
    @JSONField(name = "opening_number")
    private String openingNumber;

    /**
     * 清算行号
     */
    @JsonProperty("clearing_number")
    @JSONField(name = "clearing_number")
    private String clearingNumber;

    /**
     * 银行卡有效期
     */
    @JsonProperty("card_validity")
    @JSONField(name = "card_validity")
    private String cardValidity;

    /**
     * 证件类型
     */
    @JsonProperty("id_type")
    @JSONField(name = "id_type")
    private Integer certificateType;

    /**
     * 证件正面照片
     */
    @JsonProperty("holder_id_front_photo")
    @JSONField(name = "holder_id_front_photo")
    @Photo
    private String certificateFrontPhoto;

    /**
     * 证件反面照片
     */
    @JsonProperty("holder_id_back_photo")
    @JSONField(name = "holder_id_back_photo")
    @Photo
    private String certificateBackPhoto;

    /**
     * 证件号码
     */
    @JsonProperty("identity")
    @JSONField(name = "identity")
    private String certificateNumber;

    /**
     * 证件有效期
     */
    @JsonProperty("id_validity")
    @JSONField(name = "id_validity")
    private String certificateValidity;

    /**
     * 证件地址
     */
    @JsonProperty("holder_id_card_address")
    @JSONField(name = "holder_id_card_address")
    private String certificateAddress;

    /**
     * 证件签发机关
     */
    @JsonProperty("holder_id_card_issuing_authority")
    @JSONField(name = "holder_id_card_issuing_authority")
    private String certificateIssuingAuthority;

    /**
     * 授权函
     */
    @JsonProperty("letter_of_authorization")
    @JSONField(name = "letter_of_authorization")
    @Photo
    private String letterOfAuthorization;

    /**
     * 法人手持授权函或关系证明（法人和授权函的合照）
     */
    @JsonProperty("hand_letter_of_authorization")
    @JSONField(name = "hand_letter_of_authorization")
    @Photo
    private String handLetterOfAuthorization;

    // 清算行

    // 以下字段在extra中


    /**
     * 证件姓名
     */
    @JsonProperty("certificate_name")
    @JSONField(name = "certificate_name")
    private String certificateName;

    /**
     * 结算账户类型
     */
    @JsonProperty("settlement_account_type")
    @JSONField(name = "settlement_account_type")
    private Integer settlementAccountType;

    /**
     * 授权对公账户证明
     */
    @JsonProperty("authorize_corp_acc_proof")
    @JSONField(name = "authorize_corp_acc_proof")
    @Photo
    private String authorizeCorpAccProof;

    /**
     * 授权对公关系证明
     */
    @JsonProperty("authorize_corp_rel_proof")
    @JSONField(name = "authorize_corp_rel_proof")
    @Photo
    private String authorizeCorpRelProof;

    /**
     * 结算账户营业证照法人证件号
     */
    @JsonProperty("sett_acc_lic_lp_certificate_num")
    @JSONField(name = "sett_acc_lic_lp_certificate_num")
    private String settAccLicLpCertificateNum;

    /**
     * 结算账户营业证照法人证件正面照片
     */
    @JsonProperty("sett_acc_lic_lp_certificate_front_photo")
    @JSONField(name = "sett_acc_lic_lp_certificate_front_photo")
    @Photo
    private String settAccLicLpCertificateFrontPhoto;

    /**
     * 结算账户营业证照法人证件反面照片
     */
    @JsonProperty("sett_acc_lic_lp_certificate_back_photo")
    @JSONField(name = "sett_acc_lic_lp_certificate_back_photo")
    @Photo
    private String settAccLicLpCertificateBackPhoto;

    /**
     * 辅助证明材料
     */
    @JsonProperty("auxiliary_proof_materials")
    @JSONField(name = "auxiliary_proof_materials")
    @Photo
    private String auxiliaryProofMaterials;

    /**
     * 其他照片
     */
    @JsonProperty("other_photos")
    @JSONField(name = "other_photos")
    @Photo
    private String otherPhotos;

}
