package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.app.TradeLicenseInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VerifiedStoreTradeLicenseQueryResp extends TradeLicenseInfo {

    @JsonProperty("mask_fields")
    private List<String> maskFields = Arrays.asList("license_photo", "license_number", "license_name");
}
