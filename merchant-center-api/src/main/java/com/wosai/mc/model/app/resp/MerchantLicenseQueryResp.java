package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Data
@Accessors(chain = true)
public class MerchantLicenseQueryResp {

    private Integer type;
    private String name;
    @JsonProperty("merchant_id")
    private String merchantId;
    @JsonProperty("legal_person_name")
    private String legalPersonName;
}
