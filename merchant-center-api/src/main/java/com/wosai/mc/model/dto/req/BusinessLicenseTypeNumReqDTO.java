package com.wosai.mc.model.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.AppCommonFieldDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 营业执照类型和编号请求
 *
 * <AUTHOR>
 * @date 2025/2/6 17:10
 */
public class BusinessLicenseTypeNumReqDTO extends AppCommonFieldDTO {

    @NotNull(message = "营业执照类型不能为空")
    private Integer licenseType;

    /**
     * 营业执照编号
     */
    @JsonProperty("number")
    @JSONField(name = "number")
    @NotBlank(message = "营业执照编号不能为空")
    private String number;


    public Integer getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(Integer licenseType) {
        this.licenseType = licenseType;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }
}
