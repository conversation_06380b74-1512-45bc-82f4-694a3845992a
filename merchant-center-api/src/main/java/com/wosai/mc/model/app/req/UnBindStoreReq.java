package com.wosai.mc.model.app.req;

import com.wosai.mc.model.app.BaseAppReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = true)
public class UnBindStoreReq extends BaseAppReq {

    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;

    //地区码,coreB会通过 省市区 反查 (都不为空时)
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;


    /**
     * 街道地址
     */
    private String street_address;

    /**
     * 街道地址备注说明
     */
    private String street_address_desc;


}
