package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.MaskData;
import com.wosai.mc.annotation.MaskDataEnum;
import com.wosai.mc.model.crmPlatform.CrmApply;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@Accessors(chain = true)
public class StoreBasicInfoQueryResp {

    private String id;
    private String sn;
    private String name;
    private String province;
    private String city;
    private String district;
    @JsonProperty("district_code")
    private String districtCode;
    @JsonProperty("street_address")
    private String streetAddress;
    @JsonProperty("street_address_desc")
    private String streetAddressDesc;
    @MaskData(MaskDataEnum.NAME)
    @JsonProperty("contact_name")
    private String contactName;
    private String contact_name_value;
    @MaskData(MaskDataEnum.CELLPHONE)
    @JsonProperty("contact_cellphone")
    private String contactCellphone;
    private String contact_cellphone_value;
    @JsonProperty("mask_fields")
    private List<String> maskFields = Arrays.asList("contact_name", "contact_cellphone", "contact_email");
    private String longitude;
    private String latitude;
    @MaskData(MaskDataEnum.EMAIL)
    @JsonProperty("contact_email")
    private String contactEmail;
    private String contact_email_value;
    private String industry;

    private Map storePhotosQueryResp;

    private List<CrmApply> crm_status;

    private Map<String, Object> industry_data;

    private Map extra;
    /**
     * poi_name
     */
    @JsonProperty("poi_name")
    private String poiName;
    /**
     * poi_simple_address
     */
    @JsonProperty("poi_simple_address")
    private String poiSimpleAddress;
    @JsonProperty("is_only_open_dbb")
    private boolean isOnlyOpenDbb;
}
