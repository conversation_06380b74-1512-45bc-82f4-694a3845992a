package com.wosai.mc.model.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.MaskData;
import com.wosai.mc.annotation.MaskDataEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@Accessors(chain = true)
public class IdCardInfo {

    @NotNull(message = "法人证件类型不能为空")
    @JsonProperty("legal_person_id_type")
    private Integer legalPersonIdType;

    @MaskData(MaskDataEnum.NAME)
    @NotEmpty(message = "法人证件姓名不能为空")
    @JsonProperty("legal_person_name")
    private String legalPersonName;

    @MaskData(MaskDataEnum.NUMBER)
    @NotEmpty(message = "法人证件号不能为空")
    @JsonProperty("legal_person_id_number")
    private String legalPersonIdNumber;

    @Pattern(regexp = "\\d{8}-\\d{8}", message = "证件有效期格式错误")
    @JsonProperty("id_validity")
    private String idValidity;

    @MaskData(MaskDataEnum.PHOTO)
    @NotEmpty(message = "法人证件正面照片不能为空")
    @JsonProperty("legal_person_id_card_front_photo")
    private String legalPersonIdCardFrontPhoto;

    @MaskData(MaskDataEnum.PHOTO)
    @JsonProperty("legal_person_id_card_back_photo")
    private String legalPersonIdCardBackPhoto;

    @JsonProperty("legal_person_id_card_address")
    private String legalPersonIdCardAddress;

    @JsonProperty("legal_person_id_card_issuing_authority")
    private String legalPersonIdCardIssuingAuthority;
}
