package com.wosai.mc.model.resp;

import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.wosai.mc.model.PhotoInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * Auto-generated: 2020-10-21 9:53:57
 */
@Data
@Accessors(chain = true)
public class StoreAllInfo {

    private String contact_phone;
    private String solicitor_id;
    private String merchant_sn;
    private String business_hour;
    private long mtime;
    private int type;
    private String province;
    private String operation_contents;
    private String rank;
    private long ctime;
    private String around_type;
    private int verify_status;
    private String id;
    private String longitude;
    private int store_ext_id;
    private String merchant_name;
    private int version;
    private String district;
    private String vendor_id;
    private String name;
    private int status;
    private String street_address;
    private String street_address_desc;
    private String store_area;
    private String city;
    private String latitude;
    private String industry;
    private String merchant_id;
    private String video;
    private String client_sn;
    private String contact_email;
    private String extra;
    private Map extra_map;
    private String average_consumption_time;
    private String sn;
    private String store_id;
    private int table_count;
    private String contact_name;
    private int room_count;
    private String contact_cellphone;
    private boolean deleted;
    private int open_account_way;
    private String district_code;
    private String poi_name;
    private String poi_simple_address;
    /**
     * 门头合照
     */
    private PhotoInfo brand_photo;

    /**
     * 门头照
     */
    private PhotoInfo brand_only_scene_photo;

    /**
     * 室内物料照
     */
    private PhotoInfo indoor_material_photo;

    /**
     * 内景照
     */
    private PhotoInfo indoor_only_scene_photo;

    /**
     * 收银台照（室外物料照)
     */
    private PhotoInfo outdoor_material_photo;

    /**
     * 外景照
     */
    private PhotoInfo outdoor_only_scene_photo;

    /**
     * 价目表照片
     */
    private PhotoInfo product_price;

    /**
     * 点单价目表
     */
    private List<PhotoInfo> order_price_photo;

    /**
     * 审核图片
     */
    private PhotoInfo audit_picture;
    /**
     * 其他照片
     */
    private List<PhotoInfo> other_photo;
    /**
     * 视频播放地址信息
     */
    private GetPlayInfoResponse video_info;
}