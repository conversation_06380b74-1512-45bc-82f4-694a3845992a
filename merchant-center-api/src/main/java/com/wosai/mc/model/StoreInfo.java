package com.wosai.mc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-08-12
 */
@Data
@Accessors(chain = true)
public class StoreInfo {

    private String id;

    /**
     * sn 对应1.0中的wosai_store_id
     */
    private String sn;

    /**
     * 名称
     */
    private String name;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 状态 0关闭  1正常  2禁用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer rank;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 街道地址
     */
    private String street_address;

    /**
     * 街道地址备注说明
     */
    private String street_address_desc;

    /**
     * 联系人姓名
     */
    private String contact_name;

    /**
     * 联系固话
     */
    private String contact_phone;

    /**
     * 联系手机号
     */
    private String contact_cellphone;

    /**
     * 联系邮箱
     */
    private String contact_email;

    /**
     * 商户外部门店号
     */
    private String client_sn;

    /**
     * 商户id
     */
    private String merchant_id;

    private String solicitor_id;

    private String vendor_id;

    /**
     * 经营内容
     */
    private String operation_contents;

    /**
     * 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    private Integer verify_status;

    /**
     * 类型 1固定门店  2移动门店
     */
    private Integer type;
    /**
     * /1实地开户，2异地开户
     */
    private Integer open_account_way;


    /**
     * 扩展字段
     */
    private Map extra;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private String district_code;

    /**
     * poi_name
     */
    private String poi_name;
    /**
     * poi_simple_address
     */
    private String poi_simple_address;
}
