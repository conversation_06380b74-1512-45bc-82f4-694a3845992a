package com.wosai.mc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class License {
    private String id;

    @JsonProperty("business_license_id")
    private String business_license_id;

    @JsonProperty("license_name")
    private String license_name;

    @JsonProperty("license_number")
    private String license_number;

    @JsonProperty("license_validity")
    private String license_validity;

    @JsonProperty("license_photo")
    private String license_photo;

    @JsonProperty("license_type")
    private String license_type;

    private Integer verify_status;

    private Map extra;

    private Long ctime;

    private Long mtime;

    private Long version;

    private boolean deleted;
}

