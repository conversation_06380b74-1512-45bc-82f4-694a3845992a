package com.wosai.mc.model.dto.rsp;

import lombok.Data;

/**
 * 申请编辑营业执照token结果
 *
 * <AUTHOR>
 * @date 2025/1/15 14:03
 */
@Data
public class ApplyEditBusinessLicenseTokenResultRspDTO {

    /**
     * 是否申请成功
     */
    private Boolean applySuccess;

    /**
     * 提示信息
     */
    private String message;

    /**
     * token
     */
    private String token;

    /**
     * 1-成功  2-token已存在 3-系统异常
     */
    private Integer applyStatus;

    public static ApplyEditBusinessLicenseTokenResultRspDTO SUCCESS(String token) {
        ApplyEditBusinessLicenseTokenResultRspDTO result = new ApplyEditBusinessLicenseTokenResultRspDTO();
        result.setApplySuccess(true);
        result.setToken(token);
        result.setApplyStatus(1);
        return result;
    }

    public static ApplyEditBusinessLicenseTokenResultRspDTO tokenExisted(String message) {
        ApplyEditBusinessLicenseTokenResultRspDTO result = new ApplyEditBusinessLicenseTokenResultRspDTO();
        result.setApplySuccess(false);
        result.setMessage(message);
        result.setApplyStatus(2);
        return result;
    }


    public static ApplyEditBusinessLicenseTokenResultRspDTO systemError(String message) {
        ApplyEditBusinessLicenseTokenResultRspDTO result = new ApplyEditBusinessLicenseTokenResultRspDTO();
        result.setApplySuccess(false);
        result.setMessage(message);
        result.setApplyStatus(3);
        return result;
    }


}
