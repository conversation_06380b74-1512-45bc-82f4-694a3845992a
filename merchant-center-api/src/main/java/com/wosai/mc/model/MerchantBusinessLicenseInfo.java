package com.wosai.mc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.MaskData;
import com.wosai.mc.annotation.MaskDataEnum;
import com.wosai.mc.annotation.Photo;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-08-12
 */
@Data
@Accessors(chain = true)
public class MerchantBusinessLicenseInfo {

    public static final String AUXILIARY_PROOF_MATERIALS_KEY = "auxiliary_proof_materials";

    public static final String BUSINESS_LICENSE_VERIFY_V2_KEY = "business_license_verify_v2";


    private String id;

    /**
     * 商户号
     */
    private String merchant_id;

    /**
     * 0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     */
    private Integer type;

    /**
     * 照片
     */
    @Photo
    private String photo;

    /**
     * 注册号
     */
    private String number;

    /**
     * 名称
     */
    private String name;

    /**
     * 经营范围
     */
    private String business_scope;

    /**
     * 有效期
     */
    private String validity;

    private String address;

    /**
     * 授权函
     */
    @Photo
    private String letter_of_authorization;

    /**
     * 行业许可证
     */
    @Photo
    @JsonProperty("trade_license")
    private String tradeLicense;

    /**
     * 法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     */
    private Integer legal_person_id_type;

    /**
     * 法人身份证正面照
     */
    @Photo
    private String legal_person_id_card_front_photo;

    /**
     * 法人身份证反面照
     */
    @Photo
    private String legal_person_id_card_back_photo;

    /**
     * 法人姓名
     */
    @MaskData(MaskDataEnum.NAME)
    private String legal_person_name;

    /**
     * 法人证件号码
     */
    @MaskData(MaskDataEnum.NUMBER)
    private String legal_person_id_number;

    /**
     * 法人手机号
     */
    @Photo
    private String legal_person_contact_phone;

    /**
     * 身份证有效期
     */
    private String id_validity;

    /**
     * 法人身份证住址
     */
    private String legal_person_id_card_address;

    /**
     * 法人身份证签发机关
     */
    private String legal_person_id_card_issuing_authority;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;
    /**
     * 扩展字段
     */
    private Map extra;
    /**
     * 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    private Integer verify_status;

    private List<License> trade_license_list;

    /**
     * 营业执照上法人姓名
     */
    private String registered_legal_person_name;

    /**
     * 辅助证明材料
     */
    @Photo
    @JsonProperty("auxiliary_proof_materials")
    private String auxiliaryProofMaterials;

    public String exactAuxiliaryProofMaterialsFromExtra() {
        if (MapUtils.isEmpty(this.extra)) {
            return null;
        }
        return MapUtils.getString(this.extra, AUXILIARY_PROOF_MATERIALS_KEY);
    }
}
