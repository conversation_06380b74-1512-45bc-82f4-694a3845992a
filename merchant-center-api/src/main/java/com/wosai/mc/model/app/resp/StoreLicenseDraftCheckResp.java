package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.CommonResultResp;
import com.wosai.mc.model.CommonStatusResp;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@Accessors(chain = true)
public class StoreLicenseDraftCheckResp {

    @JsonProperty("business_license_info")
    private CommonStatusResp businessLicense;

    @JsonProperty("id_card_info")
    private CommonStatusResp idCard;

    @JsonProperty("trade_license_info")
    private CommonStatusResp tradeLicense;

    @JsonProperty("other_info")
    private CommonStatusResp other;
}
