package com.wosai.mc.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class StoreFilterConfig {
    /**
     * 门店筛选状态
     */
    private List<Integer> status;
    /**
     * 约定:名称升序/降序  1,2   创建时间 升序/降序 3,4   手动 5
     */
    private Integer order;

    /**
     * 手动排序时有效,有序的门店id
     */
    private List<String> manual;


    @JsonIgnore
    public void addPageInfoOrder(PageInfo pageInfo) {
        if (pageInfo == null) {
            return;
        }
        if (Objects.equals(this.order, StoreConfigConstant.NAME_ASC)) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy("name", OrderBy.OrderType.ASC)));
        } else if (Objects.equals(this.order, StoreConfigConstant.NAME_DESC)) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy("name", OrderBy.OrderType.DESC)));
        } else if (Objects.equals(this.order, StoreConfigConstant.CTIME_ASC)) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));
        } else if (Objects.equals(this.order, StoreConfigConstant.CTIME_DESC)) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        }
//        if (Objects.equals(this.order, StoreConfigConstant.RANK_ASC)) {
//            pageInfo.setOrderBy(Arrays.asList(new OrderBy("rank", OrderBy.OrderType.ASC)));
//        }
    }

}
