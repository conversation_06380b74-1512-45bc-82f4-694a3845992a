package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.app.IdCardInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VerifiedStoreLicenseIdCardQueryResp extends IdCardInfo {

    @JsonProperty("mask_fields")
    private List<String> maskFields = Arrays.asList("legal_person_name", "legal_person_id_number", "legal_person_id_card_front_photo", "legal_person_id_card_back_photo");
}
