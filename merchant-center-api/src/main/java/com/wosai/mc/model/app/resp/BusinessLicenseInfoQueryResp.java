package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/21
 */
@Data
@Accessors(chain = true)
public class BusinessLicenseInfoQueryResp {

    /**
     * 营业执照号
     */
    private String number;
    /**
     * 营业执照名称
     */
    private String name;
    /**
     * 营业执照经营范围
     */
    @JsonProperty("business_scope")
    private String businessScope;
    /**
     * 地址
     */
    private String address;
    /**
     * 营业执照法人姓名
     */
    @JsonProperty("legal_person_name")
    private String legalPersonName;
    /**
     * 营业执照营业开始时间
     */
    @JsonProperty("start_validity")
    private String startValidity;
    /**
     * 营业执照营业结束时间
     */
    @JsonProperty("end_validity")
    private String endValidity;

}
