package com.wosai.mc.model;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BusinessLicenseAuditInfo {

    private Integer id;
    private String merchant_id;
    private String merchant_sn;
    private String merchant_name;
    private String merchant_contact_name;
    private String photo;
    private String number;
    private String name;
    private String validity;
    private String legal_person_name;
    private String legal_person_id_card_front_photo;
    private String legal_person_id_card_back_photo;
    private String legal_person_id_number;
    private String legal_person_id_card_address;
    private String legal_person_id_card_issuing_authority;
    private String id_validity;
    private String letter_of_authorization;
    private String apply_remark;
    private List<String> proof_assistant_photos;
    private String submitor;
    private String submitor_name;
    private String submit_platform;
    private String submit_organization_path;
    private String auditor;
    private String auditor_name;
    private String audit_platform;
    private Integer business_license_status;
    private List<Map> status_change_details;
    private String remark;
    private String description;
    private Integer source;
    private Integer id_type;
    private Integer license_type;
    private String license_address;
}
