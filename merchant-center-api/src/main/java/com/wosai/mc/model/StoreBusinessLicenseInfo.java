package com.wosai.mc.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class StoreBusinessLicenseInfo {
    private String id;

    /**
     * 商户号
     */
    private String merchant_id;

    /**
     * 0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     */
    private Integer type;

    /**
     * 照片
     */
    private String photo;

    /**
     * 注册号
     */
    private String number;

    /**
     * 名称
     */
    private String name;

    /**
     * 经营范围
     */
    private String business_scope;

    /**
     * 有效期
     */
    private String validity;

    private String address;

    /**
     * 法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     */
    private Integer legal_person_id_type;

    /**
     * 法人身份证正面照
     */
    private String legal_person_id_card_front_photo;

    /**
     * 法人身份证反面照
     */
    private String legal_person_id_card_back_photo;

    /**
     * 法人姓名
     */
    private String legal_person_name;

    /**
     * 法人证件号码
     */
    private String legal_person_id_number;

    /**
     * 身份证有效期
     */
    private String id_validity;

    /**
     * 法人身份证住址
     */
    private String legal_person_id_card_address;

    /**
     * 法人身份证签发机关
     */
    private String legal_person_id_card_issuing_authority;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;
    /**
     * 扩展字段
     */
    private Map extra;
    /**
     * 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    private Integer verify_status;

    // -----  门店维度营业执照特有信息
    private String store_id;

    List<License> trade_license_list;

    private boolean use_merchant_business_license;

}
