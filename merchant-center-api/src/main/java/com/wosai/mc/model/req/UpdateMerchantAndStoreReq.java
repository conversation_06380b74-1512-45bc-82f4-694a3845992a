package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/1/20
 */
@Data
@Accessors(chain = true)
public class UpdateMerchantAndStoreReq {

    /**
     * 商户信息
     */
    @Valid
    @NotNull(message = "merchant不能为空")
    private UpdateMerchantReq merchant;

    /**
     * 营业执照信息
     */
    @Valid
    @NotNull(message = "license不能为空")
    private UpdateMerchantBusinessLicenseReq license;

    /**
     * 账号信息
     */
    private AccountReq accountReq;

    /**
     * 门店信息
     */
    @Valid
    @NotNull(message = "store不能为空")
    private UpdateStoreReq store;


    /**
     * 业务方 devCode
     */
    @JsonProperty("dev_code")
    private String devCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作平台
     */
    private String platform;
}
