package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.PhotoInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 门店信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@Accessors(chain = true)
public class UpdateStorePicturesReq {
    /**
     * 门店id
     */
    @NotBlank(message = "storeId不可为空")
    private String storeId;
    /**
     * 审核图片
     */
    @JsonProperty("audit_picture")
    private PhotoInfo auditPicture;

    /**
     * 门头合照
     */
    @JsonProperty("brand_photo")
    private PhotoInfo brandPhoto;

    /**
     * 门头照
     */
    @JsonProperty("brand_only_scene_photo")
    private PhotoInfo brandOnlyScenePhoto;

    /**
     * 室内物料照
     */
    @JsonProperty("indoor_material_photo")
    private PhotoInfo indoorMaterialPhoto;

    /**
     * 内景照
     */
    @JsonProperty("indoor_only_scene_photo")
    private PhotoInfo indoorOnlyScenePhoto;

    /**
     * 收银台照（室外物料照)
     */
    @JsonProperty("outdoor_material_photo")
    private PhotoInfo outdoorMaterialPhoto;

    /**
     * 外景照
     */
    @JsonProperty("outdoor_only_scene_photo")
    private PhotoInfo outdoorOnlyScenePhoto;

    /**
     * 价目表照片
     */
    @JsonProperty("product_price")
    private PhotoInfo productPrice;

    /**
     * 点单价目表
     */
    @JsonProperty("order_price_photo")
    private List<PhotoInfo> orderPricePhoto;

    /**
     * 其他照片
     */
    @JsonProperty("other_photo")
    private List<PhotoInfo> otherPhoto;

    private String operator;

    private String platform;

}
