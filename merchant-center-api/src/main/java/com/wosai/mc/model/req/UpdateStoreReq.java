package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.Validators.UpdateStoreReqCheck;
import com.wosai.mc.model.PhotoInfo;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 门店信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@Accessors(chain = true)
@UpdateStoreReqCheck
public class UpdateStoreReq {
    /**
     * id
     */
    @NotBlank(message = "id不可为空")
    private String id;
    /**
     * 名称
     */
    @Size(max = 128, message = "name不能超过{max}字符")
    private String name;

    /**
     * 经度
     */
    @Pattern(regexp = "\\d{1,3}\\.\\d{1,6}", message = "经度为小数，小数点前后分别最多6位")
    private String longitude;

    /**
     * 纬度
     */
    @Pattern(regexp = "\\d{1,3}\\.\\d{1,6}", message = "纬度为小数，小数点前后分别最多6位")
    private String latitude;

    /**
     * 省
     */
    @Size(max = 32, message = "province不能超过{max}字符")
    private String province;

    /**
     * 市
     */
    @Size(max = 32, message = "city不能超过{max}字符")
    private String city;

    /**
     * 区
     */
    @Size(max = 32, message = "district必须在{max}以内")
    private String district;

    /**
     * 地区码
     */
    @JsonProperty("district_code")
    private String districtCode;

    /**
     * 街道地址
     */
    @Size(max = 255, message = "street_address长度必须在{max}以内")
    @JsonProperty("street_address")
    private String streetAddress;

    /**
     * 街道地址备注说明
     */
    @Size(max = 255, message = "street_address_desc不能超过{max}字符")
    //@Pattern(regexp = ".*[\\u4e00-\\u9fa5].*", message = "门店联系地址备注说明中必须包含至少一个中文")
    @JsonProperty("street_address_desc")
    private String streetAddressDesc;

    /**
     * 联系人姓名
     */
    @Size(max = 32, message = "contact_name长度必须在{max}以内")
    @JsonProperty("contact_name")
    private String contactName;

    /**
     * 联系固话
     */
    @Size(max = 32, message = "contact_phone长度必须在{max}以内")
    @JsonProperty("contact_phone")
    private String contactPhone;

    /**
     * 联系手机号
     */
    @Size(max = 32, message = "contact_cellphone长度必须在{max}以内")
    @JsonProperty("contact_cellphone")
    private String contactCellphone;

    /**
     * 服务商UUID
     */
    @Size(max = 37, message = "vendor_id长度必须在{max}以内")
    @JsonProperty("vendor_id")
    private String vendorId;

    /**
     * 推广者UUID
     */
    @Size(max = 37, message = "solicitor_id长度必须在{max}以内")
    @JsonProperty("solicitor_id")
    private String solicitorId;

    /**
     * 经营内容
     */
    @JsonProperty("operation_contents")
    private String operationContents;

    /**
     * 外部编号
     */
    @Size(max = 50, message = "client_sn长度必须在{max}以内")
    @JsonProperty("client_sn")
    private String clientSn;
    /**
     * 邮箱
     */
    @Size(max = 64, message = "contact_email长度必须在{max}以内")
    @JsonProperty("contact_email")
    private String contactEmail;

    /**
     * 开户方式
     */
    @JsonProperty("open_account_way")
    private Integer openAccountWay;

    @JsonProperty("dev_code")
    private String devCode;

    @JsonProperty("dev_codes")
    private List<String> devCodes;

    /**
     * 审核图片
     */
    @JsonProperty("audit_picture")
    private PhotoInfo auditPicture;

    /**
     * 门头合照
     */
    @JsonProperty("brand_photo")
    private PhotoInfo brandPhoto;

    /**
     * 门头照
     */
    @JsonProperty("brand_only_scene_photo")
    private PhotoInfo brandOnlyScenePhoto;

    /**
     * 室内物料照
     */
    @JsonProperty("indoor_material_photo")
    private PhotoInfo indoorMaterialPhoto;

    /**
     * 内景照
     */
    @JsonProperty("indoor_only_scene_photo")
    private PhotoInfo indoorOnlyScenePhoto;

    /**
     * 收银台照（室外物料照)
     */
    @JsonProperty("outdoor_material_photo")
    private PhotoInfo outdoorMaterialPhoto;

    /**
     * 外景照
     */
    @JsonProperty("outdoor_only_scene_photo")
    private PhotoInfo outdoorOnlyScenePhoto;

    /**
     * 价目表照片
     */
    @JsonProperty("product_price")
    private PhotoInfo productPrice;

    /**
     * 点单价目表
     */
    @JsonProperty("order_price_photo")
    private List<PhotoInfo> orderPricePhoto;

    /**
     * 其他照片
     */
    @JsonProperty("other_photo")
    private List<PhotoInfo> otherPhoto;

    /**
     * 门店环境视频
     */
    private String video;

    /**
     * 营业时间
     */
    @JsonProperty("business_hour")
    private String businessHour;

    /**
     * 门店面积
     */
    @JsonProperty("store_area")
    private String storeArea;

    /**
     * 包间数
     */
    @JsonProperty("room_count")
    private Integer roomCount;

    /**
     * 桌数
     */
    @JsonProperty("table_count")
    private Integer tableCount;

    /**
     * 顾客平均消费时间
     */
    @JsonProperty("average_consumption_time")
    private String averageConsumptionTime;

    /**
     * 店铺周边,比如学校周边、商场周边，枚举值可以多选
     */
    @JsonProperty("around_type")
    private String aroundType;

    private String operator;

    private String platform;

    private String industry;

    /**
     * 扩展字段
     */
    private Map extra;

    /**
     * poi_name
     */
    @JsonProperty("poi_name")
    private String poiName;
    /**
     * poi_simple_address
     */
    @JsonProperty("poi_simple_address")
    private String poiSimpleAddress;

    /**
     * 本字段不是store表内字段,用于校验逻辑
     */
    @JsonProperty("is_crm")
    private boolean is_crm;

    public void setClientSn(String clientSn) {
        if (StringUtils.isBlank(clientSn)) {
            //库内该字段属于唯一索引,业务方传空字符可能触发索引异常. 手动设为NULL
            this.clientSn = null;
            return;
        }
        this.clientSn = clientSn;
    }

    public UpdateStorePicturesReq buildUpdateStorePicturesReq() {
        if (auditPicture == null && brandPhoto == null && brandOnlyScenePhoto == null
                && indoorMaterialPhoto == null && indoorOnlyScenePhoto == null
                && outdoorMaterialPhoto == null && outdoorOnlyScenePhoto == null
                && productPrice == null && orderPricePhoto == null
                && otherPhoto == null) {
            return null;
        }
        return new UpdateStorePicturesReq()
                .setStoreId(id)
                .setAuditPicture(auditPicture)
                .setBrandPhoto(brandPhoto)
                .setBrandOnlyScenePhoto(brandOnlyScenePhoto)
                .setIndoorMaterialPhoto(indoorMaterialPhoto)
                .setIndoorOnlyScenePhoto(indoorOnlyScenePhoto)
                .setOutdoorMaterialPhoto(outdoorMaterialPhoto)
                .setOutdoorOnlyScenePhoto(outdoorOnlyScenePhoto)
                .setProductPrice(productPrice)
                .setOrderPricePhoto(orderPricePhoto)
                .setOtherPhoto(otherPhoto);

    }

    /**
     * 门店环境
     */
    public StoreExtReq buildStoreExtReq() {
        if (video == null && businessHour == null && storeArea == null && roomCount == null && tableCount == null
                && averageConsumptionTime == null && aroundType == null) {
            return null;
        }
        return new StoreExtReq()
                .setStoreId(id)
                .setVideo(video)
                .setBusinessHour(businessHour)
                .setStoreArea(storeArea)
                .setRoomCount(roomCount)
                .setTableCount(tableCount)
                .setAverageConsumptionTime(averageConsumptionTime)
                .setAroundType(aroundType);
    }

}
