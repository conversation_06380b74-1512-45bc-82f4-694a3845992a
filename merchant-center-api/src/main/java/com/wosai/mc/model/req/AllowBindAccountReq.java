package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/2/12
 */
@Data
@Accessors(chain = true)
public class AllowBindAccountReq {

    private String merchantId;

    @NotBlank(message = "手机号不能为空")
    private String cellphone;

    private String identity;

    /**
     * crm组织编号，用来区分调用方是不是拉卡拉组织
     */
    @JsonProperty("crm_org_code")
    private String crmOrgCode;
}
