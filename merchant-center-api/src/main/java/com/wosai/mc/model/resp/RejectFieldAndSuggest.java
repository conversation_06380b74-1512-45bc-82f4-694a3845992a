package com.wosai.mc.model.resp;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/10/14
 */
@Data
@Accessors(chain = true)
public class RejectFieldAndSuggest {

    public static final int TYPE_MERCHANT = 1;

    public static final int TYPE_STORE = 2;

    /**
     * 驳回字段
     */
    private String rejectField;

    /**
     * 驳回字段名称
     */
    private String rejectFieldName;

    /**
     * 字段属于哪个表
     */
    private String parent;
    /**
     * 驳回原因
     */
    private String reason;
    /**
     * 修改建议
     */
    private String suggest;

    private int type;

}
