package com.wosai.mc.model.resp;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CheckBankAccountResp {

    private Boolean allow;

    private Integer code;

    private String result;

    public static CheckBankAccountResp success() {
        return new CheckBankAccountResp().setAllow(true).setCode(11000).setResult("success");
    }

    public static CheckBankAccountResp FAIL(String result) {
        return new CheckBankAccountResp().setAllow(false).setCode(11099).setResult(result);
    }
}
