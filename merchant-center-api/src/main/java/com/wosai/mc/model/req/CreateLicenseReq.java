package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
public class CreateLicenseReq {

    @NotBlank(message = "许可证必须关联一个营业执照id")
    @JsonProperty("business_license_id")
    private String businessLicenseId;

    @JsonProperty("license_name")
    private String licenseName;

    @JsonProperty("license_number")
    private String licenseNumber;

    @JsonProperty("license_validity")
    private String licenseValidity;

    @JsonProperty("license_photo")
    private String licensePhoto;

    @JsonProperty("license_type")
    private String licenseType;

    private Integer verify_status;

    public CreateLicenseReq setLicensePhoto(String licensePhoto) {
        this.licensePhoto = PhotoUtils.baseUrl(licensePhoto);
        return this;
    }
}
