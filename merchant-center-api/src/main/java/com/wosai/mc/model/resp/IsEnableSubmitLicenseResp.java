package com.wosai.mc.model.resp;

import com.wosai.mc.model.BusinessLicenseAuditInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IsEnableSubmitLicenseResp {

    /**
     * 商户号
     */
    private String merchant_sn;
    /**
     * 商户名
     */
    private String merchant_name;
    /**
     * 是否允许提交
     */
    private Boolean enable_submit_license;
    /**
     * 营业执照信息
     */
    private MerchantBusinessLicenseResp merchant_business_license;
    /**
     * 营业执照审核信息
     */
    private BusinessLicenseAuditInfo business_license_audit;

    @Data
    @Accessors(chain = true)
    public static  class MerchantBusinessLicenseResp {
        private String id;
        private String merchant_id;
        private String photo;
        private String number;
        private String name;
        private String validity;
        private String registered_legal_person_name;
        private String letter_of_authorization;
        private String trade_license;
        private String legal_person_id_number;
        private String legal_person_id_card_front_photo;
        private String legal_person_id_card_back_photo;
        private String id_validity;
        private String legal_person_name;
        private String id_type;
        private String license_address;
        private String license_type;
        private Integer business_license_status;
        private String legal_person_id_card_address;
        private String legal_person_id_card_issuing_authority;
        private Integer verify_status;
        private Map extra;

    }

}
