package com.wosai.mc.model.app;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.constants.MerchantConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
public class BaseAppReq {

    @NotEmpty(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    @NotEmpty(message = "用户ID不能为空")
    @JsonProperty("uc_user_id")
    private String ucUserId;

    @NotEmpty(message = "商户用户ID不能为空")
    @JsonProperty("merchant_user_id")
    private String merchantUserId;

    @NotEmpty(message = "商户用户角色不能为空")
    @JsonProperty("role")
    private String role;

    @JsonIgnore
    public boolean isSuperAdmin() {
        return MerchantConstant.SUPER_ADMIN.equals(role);
    }
}
