package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-27
 * @Description:
 */

@Data
@Accessors(chain = true)
public class McPreReq {

    @NotBlank(message = "devCode不能为空")
    private String devCode;

    private boolean forceUpdate;

    @NotEmpty
    @Valid
    private List<Mcpre> mcpreList;

    @Data
    public static class Mcpre {

        @NotBlank(message = "tableName不能为空")
        private String tableName;
        @NotBlank(message = "bizId不能为空")
        private String bizId;
        @NotNull
        private Map data;

    }


}
