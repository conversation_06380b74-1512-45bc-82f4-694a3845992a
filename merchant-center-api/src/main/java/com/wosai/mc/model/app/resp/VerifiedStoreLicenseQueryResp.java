package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.app.BusinessLicenseInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class VerifiedStoreLicenseQueryResp extends BusinessLicenseInfo {

    @JsonProperty("mask_fields")
    private List<String> maskFields = Arrays.asList("photo", "number", "name", "legal_person_name");
}
