package com.wosai.mc.model.req;

import com.wosai.mc.model.StoreFilterConfig;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class CreateOrUpdateStoreFilterConfigReq {

    @NotEmpty(message = "商户用户id不可为空")
    private String merchant_user_id;

    @NotNull(message = "配置信息不可为空")
    private StoreFilterConfig config;
}
