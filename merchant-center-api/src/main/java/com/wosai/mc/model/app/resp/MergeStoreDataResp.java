package com.wosai.mc.model.app.resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class MergeStoreDataResp {

    private long count;

    private List<DataDetails> data;


    @Data
    public static class DataDetails {
        private String store_id;

        /**
         * 该门店外卖是否活跃
         */
        private boolean takeout_active;

        /**
         * 门店名称,智慧经营的在前,商户中心的在后
         */
        private List<String> name;


        /**
         * 地址信息,顺序同上
         */
        private List<Address> address;

        /**
         * 到小数点后5位 经纬度是否不同
         */
        private boolean diff_itude;
    }

    @Data
    public static class Address {
        private String poi_name;
        private String poi_simple_address;
        private String poi_address;
        private String street_address;
        private String street_address_desc;
    }
}
