package com.wosai.mc.model.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.MaskData;
import com.wosai.mc.annotation.MaskDataEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@Accessors(chain = true)
public class BusinessLicenseInfo {

    @NotNull(message = "营业证照类型不能为空")
    private Integer type;

    @MaskData(MaskDataEnum.PHOTO)
    @NotEmpty(message = "营业证照照片不能为空")
    private String photo;

    @MaskData(MaskDataEnum.NUMBER)
    @NotEmpty(message = "营业证照号不能为空")
    private String number;

    @MaskData(MaskDataEnum.NAME)
    @NotEmpty(message = "营业证照名称不能为空")
    private String name;

    @MaskData(MaskDataEnum.NAME)
    @NotEmpty(message = "法人姓名不能为空")
    @JsonProperty("legal_person_name")
    private String legalPersonName;

    @NotEmpty(message = "营业执照注册地址不能为空")
    private String address;

    @JsonProperty("business_scope")
    private String businessScope;

    @Pattern(regexp = "\\d{8}-\\d{8}", message = "营业执照有效期格式错误")
    @NotEmpty(message = "营业执照有效期不能为空")
    private String validity;

    @JsonProperty("use_merchant_business_license")
    private int useMerchantBusinessLicense;
}
