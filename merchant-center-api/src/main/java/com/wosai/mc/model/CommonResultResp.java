package com.wosai.mc.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Data
@Accessors(chain = true)
public class CommonResultResp {

    private Boolean result;
    private String message;

    public static CommonResultResp SUCCESS() {
        return new CommonResultResp().setResult(true);
    }

    public static CommonResultResp SUCCESS(String message) {
        return new CommonResultResp().setResult(true).setMessage(message);
    }

    public static CommonResultResp FAIL(String message) {
        return new CommonResultResp().setResult(false).setMessage(message);
    }
}
