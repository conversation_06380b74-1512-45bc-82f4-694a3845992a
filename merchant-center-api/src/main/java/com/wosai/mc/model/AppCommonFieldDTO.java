package com.wosai.mc.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;

/**
 * crm/收钱吧 app公共字段dto
 *
 * <AUTHOR>
 * @date 2025/1/15 09:52
 */
public class AppCommonFieldDTO {

    public static final String PLATFORM_CRM_APP = "CRM_APP";

    public static final String PLATFORM_APP = "APP";

    public static final String PLATFORM_CRM_WEB = "CRM_WEB";

    public static final String PLATFORM_MSP = "MSP";

    public static final String PLATFORM_SPA = "SPA";

    @JSONField(name = "merchant_id")
    @JsonProperty("merchant_id")
    @NotBlank(message = "商户id不能为空")
    private String merchantId;

    /**
     * 平台标识
     *  APP:收钱吧app
     *  CRM_APP:crm app
     */
    @NotBlank(message = "平台标识不能为空")
    private String platform;

    /**
     * 登录用户id
     */
    @JSONField(name = "uc_user_id")
    @JsonProperty("uc_user_id")
    private String userId;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 是否来自销售端
     *
     * @return true:是 false:否
     */
    public boolean fromCrm() {
        return StringUtils.equalsIgnoreCase(platform, PLATFORM_CRM_APP)
                || StringUtils.equalsIgnoreCase(platform, PLATFORM_CRM_WEB);
    }

    /**
     * 是否来自商户端
     *
     * @return true:是 false:否
     */
    public boolean fromAppOrMsp() {
        return StringUtils.equalsIgnoreCase(platform, PLATFORM_APP)
                || StringUtils.equalsIgnoreCase(platform, PLATFORM_MSP);
    }

    /**
     * 是否来自MSP
     */
    public boolean fromMsp() {
        return StringUtils.equalsIgnoreCase(platform, PLATFORM_MSP);
    }
}
