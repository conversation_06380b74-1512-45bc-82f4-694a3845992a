package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 商户完整信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
public class UpdateMerchantComplete {

    /**
     * 商户信息
     */
    private UpdateMerchantReq merchant;

    /**
     * 营业执照信息
     */
    private UpdateMerchantBusinessLicenseReq license;

    /**
     * 账号信息
     */
    private AccountReq account;


    /**
     * 业务方 devCode
     */
    @JsonProperty("dev_code")
    private String devCode;

    /**
     * 操作人
     */
    private String operator;

    @JsonProperty("dev_codes")
    private List<String> devCodes;

    /**
     * 操作平台
     */
    private String platform;

}
