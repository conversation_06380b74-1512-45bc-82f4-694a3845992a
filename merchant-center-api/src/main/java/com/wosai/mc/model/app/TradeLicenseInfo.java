package com.wosai.mc.model.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.annotation.MaskData;
import com.wosai.mc.annotation.MaskDataEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@Accessors(chain = true)
public class TradeLicenseInfo {

    @NotNull(message = "许可证类型不能为空")
    @JsonProperty("license_type")
    private Integer licenseType;

    @MaskData(MaskDataEnum.PHOTO)
    @NotEmpty(message = "许可证照片不能为空")
    @JsonProperty("license_photo")
    private String licensePhoto;

    @MaskData(MaskDataEnum.NUMBER)
    @NotEmpty(message = "许可证号不能为空")
    @JsonProperty("license_number")
    private String licenseNumber;

    @MaskData(MaskDataEnum.NAME)
    @NotEmpty(message = "许可证名称不能为空")
    @JsonProperty("license_name")
    private String licenseName;

    @Pattern(regexp = "\\d{8}-\\d{8}", message = "许可证有效期格式错误")
    @NotEmpty(message = "许可证有效期不能为空")
    @JsonProperty("license_validity")
    private String licenseValidity;
}
