package com.wosai.mc.model.dto.rsp;

import com.wosai.mc.model.dto.BankAccountDTO;
import com.wosai.mc.model.dto.BusinessLicenseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 营业执照认证编辑草稿rsp
 *
 * <AUTHOR>
 * @date 2025/1/15 11:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessLicenseAuditEditDraftDTO {

    /**
     * 营业执照
     */
    private BusinessLicenseDTO businessLicense;

    /**
     * 结算账户
     */
    private BankAccountDTO bankAccount;

}
