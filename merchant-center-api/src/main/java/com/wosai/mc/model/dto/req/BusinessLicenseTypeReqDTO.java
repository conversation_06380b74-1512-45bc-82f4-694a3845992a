package com.wosai.mc.model.dto.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.AppCommonFieldDTO;

import javax.validation.constraints.NotNull;

/**
 * 营业执照类型请求
 *
 * <AUTHOR>
 * @date 2025/2/6 17:10
 */
public class BusinessLicenseTypeReqDTO extends AppCommonFieldDTO {

    @NotNull(message = "营业执照类型不能为空")
    private Integer licenseType;

    /**
     * 法人证件类型
     */
    @JsonProperty("legal_person_id_type")
    @JSONField(name = "legal_person_id_type")
    private Integer legalPersonCertificateType;


    public Integer getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(Integer licenseType) {
        this.licenseType = licenseType;
    }

    public Integer getLegalPersonCertificateType() {
        return legalPersonCertificateType;
    }

    public void setLegalPersonCertificateType(Integer legalPersonCertificateType) {
        this.legalPersonCertificateType = legalPersonCertificateType;
    }
}
