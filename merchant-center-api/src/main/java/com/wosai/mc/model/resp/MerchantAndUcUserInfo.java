package com.wosai.mc.model.resp;

import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/4
 */
@Data
@Accessors(chain = true)
public class MerchantAndUcUserInfo {

    /**
     * 商户信息
     */
    private MerchantInfo merchantInfo;

    private Map ucUserInfo;
    /**
     * 营业执照信息
     */
    private Map license;
}
