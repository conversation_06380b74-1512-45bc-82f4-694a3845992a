package com.wosai.mc.model.resp;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/16
 */
@Data
public class FieldStatusResp {

    private List<String> verifying;

    private List<String> verifySuccess;

    private List<String> verifyFailed;

    private List<String> blackField;

    public FieldStatusResp build(Map extra) {
        List<String> verifying = (List<String>) extra.get("1");
        List<String> verifySuccess = (List<String>) extra.get("2");
        List<String> verifyFailed = (List<String>) extra.get("3");
        this.setVerifying(verifying);
        this.setVerifySuccess(verifySuccess);
        this.setVerifyFailed(verifyFailed);
        return this;
    }
}
