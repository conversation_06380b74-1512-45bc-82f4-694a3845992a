package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class CreateLicenseWithBusinessReq {

    @JsonProperty("license_name")
    private String licenseName;

    @JsonProperty("license_number")
    private String licenseNumber;

    @JsonProperty("license_validity")
    private String licenseValidity;

    @JsonProperty("license_photo")
    private String licensePhoto;

    @JsonProperty("license_type")
    private String licenseType;

    private Integer verify_status;

    public CreateLicenseWithBusinessReq setLicensePhoto(String licensePhoto) {
        this.licensePhoto = PhotoUtils.baseUrl(licensePhoto);
        return this;
    }
}
