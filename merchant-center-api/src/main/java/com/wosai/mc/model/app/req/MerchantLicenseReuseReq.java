package com.wosai.mc.model.app.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/10/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MerchantLicenseReuseReq extends StoreIdOpReq{

    @NotNull(message = "是否复用不能为空")
    private Boolean reuse;

    @NotNull(message = "是否来源于申请变更不能为空")
    @JsonProperty("from_change_apply")
    private Boolean fromChangeApply;
}
