package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Data
@Accessors(chain = true)
public class CreateMerchantAndStoreReq {

    /**
     * 商户信息
     */
    @Valid
    @NotNull(message = "{merchant不能为空}")
    private CreateMerchantReq merchant;

    /**
     * 营业执照信息
     */
    @Valid
    @NotNull(message = "{license不能为空}")
    private CreateMerchantBusinessLicenseReq license;

    /**
     * 账号
     */
    @Valid
    @NotNull(message = "{account不能为空}")
    private AccountReq account;

    @Valid
    @NotNull(message = "{store不能为空}")
    private CreateStoreReq store;

    /**
     * 是否是lkl商户
     */
    private Boolean isLakala;

    /**
     * 操作平台
     */
    private String platform;

    /**
     * 是否需要发送短信
     * 传null或者true表示要发送
     * false表示不发送
     */
    private Boolean needSendSms;
}
