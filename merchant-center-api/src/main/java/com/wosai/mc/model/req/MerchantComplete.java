package com.wosai.mc.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 商户完整信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
public class MerchantComplete {

    /**
     * 商户信息
     */
    @NotNull(message = "{merchant不能为空}")
    private CreateMerchantReq merchant;

    /**
     * 营业执照信息
     */
    @NotNull(message = "{license不能为空}")
    private CreateMerchantBusinessLicenseReq license;

    /**
     * 账号
     */

    @NotNull(message = "{account不能为空}")
    private AccountReq account;
    /**
     * 是否是lkl商户
     */
    private Boolean isLakala;

    /**
     * 操作平台
     */
    private String platform;

    /**
     * 是否需要发送短信
     * 传null或者true表示要发送
     * false表示不发送
     */
    private Boolean needSendSms;

}
