package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/3/4
 */
@Data
@Accessors(chain = true)
public class GetMerchantAndLicenseReq {

    /**
     * 法人证件号
     */
    @JsonProperty("legal_person_id_number")
    private String legalPersonIdNumber;

    /**
     * 营业执照号
     */
    private String number;

    /**
     * crm组织编号，用来区分调用方是不是拉卡拉组织
     */
    @JsonProperty("crm_org_code")
    private String crmOrgCode;
}
