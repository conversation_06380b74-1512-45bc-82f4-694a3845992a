package com.wosai.mc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@Accessors(chain = true)
public class PagingModel {

    /**
     * 当前页
     */
    @NotNull(message = "页数不能为空")
    @Min(value = 1, message = "页数格式错误")
    @JsonProperty("current_page")
    private Integer currentPage;

    /**
     * 每页大小
     */
    @NotNull(message = "查询数量不能为空")
    @Min(value = 1, message = "查询数量格式错误")
    @JsonProperty("page_size")
    private Integer pageSize;

}
