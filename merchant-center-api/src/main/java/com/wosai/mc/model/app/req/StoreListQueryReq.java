package com.wosai.mc.model.app.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.mc.model.PagingModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreListQueryReq extends BaseAppReq {

    @JsonProperty("store_name")
    private String storeName;

    @Valid
    @NotNull(message = "分页参数不能为空")
    private PagingModel paging;

    @JsonIgnore
    @AssertTrue(message = "查询数量超出限制")
    public boolean isValidPageSize() {
        if (Objects.nonNull(paging) && Objects.nonNull(paging.getPageSize())) {
            return paging.getPageSize() <= 10;
        }
        return true;
    }
}


