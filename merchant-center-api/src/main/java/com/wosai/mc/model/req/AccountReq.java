package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020-08-11
 */
@Data
@Accessors(chain = true)
public class AccountReq {

    /**
     * 账号类型 1：手机号  2：邮箱
     */
    @NotNull(message = "identity_type不能为空")
    @JsonProperty("identity_type")
    private int identityType;

    /**
     * 账号标识
     */
    @NotBlank(message = "identifier不能为空")
    private String identifier;

    @JsonProperty("uc_user_id")
    private String ucUserId;

    /**
     * 超级管理员证件类型 1身份证
     */
    @JsonProperty("id_type")
    private int idType;

    /**
     * 超级管理员姓名
     */
    private String name;
    /**
     * 超级管理员证件号
     */
    @JsonProperty("id_number")
    private String idNumber;
    /**
     * 证件正面照片
     */
    @JsonProperty("id_card_front_photo")
    private String idCardFrontPhoto;
    /**
     * 证件反面照片
     */
    @JsonProperty("id_card_back_photo")
    private String idCardBackPhoto;

    /**
     * 证件有效期
     */
    @JsonProperty("id_card_validity")
    private String idCardValidity;

    /**
     * 证件地址
     */
    @JsonProperty("id_card_address")
    private String idCardAddress;

    /**
     * 证件签发机关
     */
    @JsonProperty("id_card_issuing_authority")
    private String idCardIssuingAuthority;

    public AccountReq setIdCardFrontPhoto(String idCardFrontPhoto) {
        this.idCardFrontPhoto = PhotoUtils.baseUrl(idCardFrontPhoto);
        return this;
    }

    public AccountReq setIdCardBackPhoto(String idCardBackPhoto) {
        this.idCardBackPhoto = PhotoUtils.baseUrl(idCardBackPhoto);
        return this;
    }
}
