package com.wosai.mc.model.app.resp;

import lombok.Data;
import lombok.experimental.Accessors;
import com.wosai.mc.model.crmPlatform.CrmApply;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@Accessors(chain = true)
public class StoreListQueryResp {

    private String id;
    private String name;
    private String sn;
    private String province;
    private String city;
    private String district;
    private String logo;

    private List<CrmApply> crm_status;


}
