package com.wosai.mc.model.app.req;

import com.wosai.mc.constants.DraftTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreLicenseDraftSaveReq extends StoreIdOpReq {

    @Valid
    @NotNull(message = "草稿信息不能为空")
    private Draft draft;

    @Data
    public static class Draft {
        @NotNull(message = "草稿类型不能为空")
        private DraftTypeEnum type;
        @NotNull(message = "草稿信息不能为空")
        private Object data;
    }
}
