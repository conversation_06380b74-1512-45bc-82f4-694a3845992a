package com.wosai.mc.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-08-12
 */
@Data
@Accessors(chain = true)
public class MerchantInfo {

    /**
     * UUID
     */
    private String id;

    /**
     * sn
     */
    private String sn;

    /**
     * 商户名
     */
    private String name;

    /**
     * 商户别名
     */
    private String alias;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 状态 0关闭  1正常  2禁用
     */
    private Integer status;

    /**
     * 商户信用等级，初始值从推广者继承过来
     */
    private Integer rank;

    /**
     * 类型  0自营  1加盟
     */
    private Integer type;

    /**
     * 1：人工提现；2：智能提现，其他值根据业务定义
     */
    private Integer withdraw_mode;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 街道地址
     */
    private String street_address;

    /**
     * 街道地址备注说明
     */
    private String street_address_desc;

    /**
     * 联系人姓名
     */
    private String contact_name;

    /**
     * 联系固话
     */
    private String contact_phone;

    /**
     * 联系手机号
     */
    private String contact_cellphone;

    /**
     * 联系邮箱
     */
    private String contact_email;

    /**
     * 联系人身份证正面照片
     */
    private String concat_id_card_front_photo;

    /**
     * 法人类型 1:个人 2:企业
     */
    private Integer legal_person_type;

    /**
     * 法人姓名
     */
    private String legal_person_name;

    /**
     * 法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；
     */
    private Integer legal_person_id_type;

    /**
     * 法人证件号码
     */
    private String legal_person_id_number;

    /**
     * 法人身份证正面照
     */
    private String legal_person_id_card_front_photo;

    /**
     * 法人身份证反面照
     */
    private String legal_person_id_card_back_photo;

    /**
     * 营业执照注册号/个体户注册号
     */
    private String legal_person_register_no;

    /**
     * 营业执照照片
     */
    private String business_license_photo;

    /**
     * 经营内容
     */
    private String business;

    /**
     * 商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY
     */
    private String currency;

    /**
     * 所有人姓名
     */
    private String owner_name;

    /**
     * 所有人手机号
     */
    private String owner_cellphone;

    /**
     * 客服电话
     */
    private String customer_phone;

    /**
     * 商户logo
     */
    private String logo;

    /**
     * 卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败
     */
    private Integer bank_account_verify_status;

    /**
     * 外部商户号
     */
    private String client_sn;

    /**
     * 服务商UUID
     */
    private String vendor_id;

    /**
     * 推广者UUID
     */
    private String solicitor_id;

    /**
     * 商户所属平台platform: 0: 收钱吧 1: 拉卡拉
     */
    private Integer platform;

    /**
     * 商户经营名称
     */
    private String business_name;

    /**
     * 联系人身份证号
     */
    private String concat_identity;

    /**
     * 个人0、个体1、组织2
     */
    private Integer merchant_type;

    private Integer merchant_scale;

    /**
     * 认证状态 0未知  1未认证  2认证成功  3认证失败
     */
    private Integer verify_status;

    /**
     * 扩展字段
     */
    private Map extra;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private String district_code;

    private String binded_store_id;
}
