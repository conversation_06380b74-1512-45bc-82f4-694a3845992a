package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@Accessors(chain = true)
public class VerifyStoreBusinessLicenseReq {

    @NotBlank(message = "门店id不能为空")
    private String storeId;

    /**
     * 认证状态  2认证成功  3认证失败
     */
    @NotBlank(message = "认证状态不能为空")
    private String verifyStatus;
    /**
     * 认证的字段名称
     */
    @NotNull(message = "认证的字段名称不能为空")
    private List<String> verifyParams;

}
