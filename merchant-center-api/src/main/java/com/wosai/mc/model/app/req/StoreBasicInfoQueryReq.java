package com.wosai.mc.model.app.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.model.app.ManagePassTokenReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreBasicInfoQueryReq extends ManagePassTokenReq {

    @JsonProperty("query_store_id")
    @NotEmpty(message = "查询门店ID不能为空")
    private String queryStoreId;
}
