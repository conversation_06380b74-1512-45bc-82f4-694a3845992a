package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
public class UpdateStoreRankReq {
    @NotEmpty(message = "商户用户id不可为空")
    private String merchant_user_id;
    @NotNull(message = "门店id参数不可为空")
    private List<String> manual;
}
