package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * @Author: l<PERSON><PERSON>qiang
 * @Date: 2020-08-28
 * @Description:
 */


@Data
@Accessors(chain = true)
public class StoreExtReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 门店id
     */
    @NotNull(message = "store_id不能为空")
    private String storeId;

    /**
     * 门头照，photo_info.id
     */
    private String brandPhotoId;

    /**
     * 内景照（室内物料照)，photo_info.id
     */
    private String indoorMaterialPhotoId;

    /**
     * 收银台照（室外物料照)，photo_info.id
     */
    private String outdoorMaterialPhotoId;
    /**
     * 审核图片
     */
    private String auditPictureId;
    /**
     * 其他照片，多张用逗号隔开,photo_info.id
     */
    private String otherPhotoId;

    /**
     * 商品价目表照片，photo_info.id
     */
    private String productPriceId;

    /**
     * 门店环境视频
     */
    private String video;

    /**
     * 营业时间
     */
    private String businessHour;

    /**
     * 门店面积
     */
    private String storeArea;

    /**
     * 包间数
     */
    private Integer roomCount;

    /**
     * 桌数
     */
    private Integer tableCount;

    /**
     * 平均消费时间
     */
    private String averageConsumptionTime;

    /**
     * 店铺周边,比如学校周边、商场周边，枚举值可以多选
     */
    private String aroundType;

    /**
     * 扩展信息
     */
    private String extra;

    private String operator;

    private String platform;

}
