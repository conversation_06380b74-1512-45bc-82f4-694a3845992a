package com.wosai.mc.model;

import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 照片信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@Accessors(chain = true)
public class PhotoInfo {

    /**
     * url
     */
    private String url;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 表id
     */
    private String id;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 地址
     */
    private String address;
    /**
     * 拍摄时间
     */
    private Long time;
    /**
     * 平台版本
     */
    private String system_version;
    /**
     * 平台
     */
    private String system;
    /**
     * 用户id
     */
    private String user_id;
    /**
     *
     */
    private String position;
    /**
     *
     */
    private String location_from;
    /**
     * 业务标识
     */
    private String dev_code;

}
