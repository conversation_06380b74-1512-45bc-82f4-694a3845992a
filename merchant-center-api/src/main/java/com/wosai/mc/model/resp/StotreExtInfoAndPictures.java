package com.wosai.mc.model.resp;

import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wosai.mc.model.PhotoInfo;
import com.wosai.mc.model.StoreExtInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: lishuangqiang
 * @Date: 2020-09-08
 * @Description:
 */

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class StotreExtInfoAndPictures extends StoreExtInfo {

    /**
     * 门头合照
     */
    private PhotoInfo brandPhoto;

    /**
     * 门头照
     */
    private PhotoInfo brandOnlyScenePhoto;

    /**
     * 室内物料照
     */
    private PhotoInfo indoorMaterialPhoto;

    /**
     * 内景照
     */
    private PhotoInfo indoorOnlyScenePhoto;

    /**
     * 收银台照（室外物料照)
     */
    private PhotoInfo outdoorMaterialPhoto;

    /**
     * 外景照
     */
    private PhotoInfo outdoorOnlyScenePhoto;

    /**
     * 价目表照片
     */
    private PhotoInfo productPrice;

    /**
     * 点单价目表
     */
    private List<PhotoInfo> orderPricePhoto;

    /**
     * 审核图片
     */
    private PhotoInfo auditPicture;
    /**
     * 其他照片
     */
    private List<PhotoInfo> otherPhoto;
    /**
     * 视频播放地址信息
     */
    private GetPlayInfoResponse videoInfo;
}
