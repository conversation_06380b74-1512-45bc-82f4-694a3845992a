package com.wosai.mc.model.dto.req;

import com.wosai.mc.model.AppCommonFieldDTO;
import com.wosai.mc.model.dto.BankAccountDTO;
import com.wosai.mc.model.dto.BusinessLicenseDTO;

/**
 * 营业执照认证编辑草稿req
 *
 * <AUTHOR>
 * @date 2025/1/15 11:23
 */
public class BusinessLicenseAuditEditDraftReqDTO extends AppCommonFieldDTO {

    private String token;

    /**
     * 营业执照
     */
    private BusinessLicenseDTO businessLicense;

    /**
     * 结算账户
     */
    private BankAccountDTO bankAccount;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public BusinessLicenseDTO getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(BusinessLicenseDTO businessLicense) {
        this.businessLicense = businessLicense;
    }

    public BankAccountDTO getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(BankAccountDTO bankAccount) {
        this.bankAccount = bankAccount;
    }
}
