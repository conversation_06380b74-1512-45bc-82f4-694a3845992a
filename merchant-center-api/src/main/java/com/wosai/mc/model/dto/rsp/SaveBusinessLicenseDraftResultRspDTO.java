package com.wosai.mc.model.dto.rsp;

import lombok.Data;

/**
 * 营业执照草稿保存结果
 *
 * <AUTHOR>
 * @date 2025/1/15 14:03
 */
@Data
public class SaveBusinessLicenseDraftResultRspDTO {

    /**
     * 是否成功
     */
    private boolean result;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 1-成功
     * 2-token过期  3-token已变更
     * 4-系统异常
     * 5-校验失败
     * 6-警告(也成功，只是前端需要抛出提示）
     */
    private Integer status;

    // warn
    public static SaveBusinessLicenseDraftResultRspDTO WARN(String message) {
        SaveBusinessLicenseDraftResultRspDTO rsp = new SaveBusinessLicenseDraftResultRspDTO();
        rsp.setResult(true);
        rsp.setStatus(6);
        rsp.setMessage(message);
        return rsp;
    }

    public static SaveBusinessLicenseDraftResultRspDTO success() {
        SaveBusinessLicenseDraftResultRspDTO rsp = new SaveBusinessLicenseDraftResultRspDTO();
        rsp.setResult(true);
        rsp.setStatus(1);
        return rsp;
    }

    public static SaveBusinessLicenseDraftResultRspDTO tokenExpired() {
        SaveBusinessLicenseDraftResultRspDTO rsp = new SaveBusinessLicenseDraftResultRspDTO();
        rsp.setResult(false);
        rsp.setStatus(2);
        rsp.setMessage("token已过期");
        return rsp;
    }

    public static SaveBusinessLicenseDraftResultRspDTO tokenChanged() {
        SaveBusinessLicenseDraftResultRspDTO rsp = new SaveBusinessLicenseDraftResultRspDTO();
        rsp.setResult(false);
        rsp.setStatus(3);
        rsp.setMessage("token已变更");
        return rsp;
    }

    public static SaveBusinessLicenseDraftResultRspDTO SYSTEM_ERROR() {
        SaveBusinessLicenseDraftResultRspDTO rsp = new SaveBusinessLicenseDraftResultRspDTO();
        rsp.setResult(false);
        rsp.setStatus(4);
        rsp.setMessage("系统异常");
        return rsp;
    }

    public static SaveBusinessLicenseDraftResultRspDTO CHECK_FAIL(String message) {
        SaveBusinessLicenseDraftResultRspDTO rsp = new SaveBusinessLicenseDraftResultRspDTO();
        rsp.setResult(false);
        rsp.setStatus(5);
        rsp.setMessage(message);
        return rsp;
    }
}
