package com.wosai.mc.model.req;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SaveOrUpdateLicenseReq {

    private String id;
    private String auditor_name;
    private String audit_platform;
    private String auditor;
    private String audit_id;
    private String msp_account_id;
    private String role;
    @NotEmpty(message = "license_address不能为空")
    private String license_address;
    private String cash_store_id;
    @NotEmpty(message = "merchant_id不能为空")
    private String merchant_id;
    private String uc_user_id;
    @NotEmpty(message = "number不能为空")
    private String number;
    private String merchant_user_id;
    private Integer id_type;
    private Integer business_license_status;
    private String legal_person_id_card_back_photo;
    private String legal_person_id_card_front_photo;
    private String legal_person_id_card_address;
    private String legal_person_id_card_issuing_authority;
    private String store_id;
    private String letter_of_authorization;
    private List<String> proof_assistant_photos;
    @NotNull(message = "license_type不能为空")
    private Integer license_type;
    @NotEmpty(message = "photo不能为空")
    private String photo;
    private String legal_person_id_number;
    private String account_id;
    private String trade_license;
    @NotEmpty(message = "legal_person_name不能为空")
    private String legal_person_name;
    @NotEmpty(message = "name不能为空")
    private String name;
    @NotEmpty(message = "validity不能为空")
    private String validity;
    private String id_validity;
    private String submitor;
    private String submit_platform;
    private String submitor_name;
    private String apply_remark;

    /**
     * 只有来自app的请求才会携带token
     */
    private String token;

    public boolean fromApp() {
        return StringUtils.isNotBlank(token);
    }

    public boolean fromCrm() {
        return StringUtils.isBlank(token);
    }

}
