package com.wosai.mc.model.app.req;


import com.wosai.mc.model.app.BaseAppReq;
import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChangeDataReq extends BaseAppReq {


    @Valid
    private List<Req> reqs;


    @Data
    public static class Req {
        @NotEmpty(message = "门店id不可为空")
        private String store_id;
        /**
         * 1智慧经营  2商户中心     NULL没选中
         */
        private Integer choosed_name;

        private Integer choosed_address;

    }
}
