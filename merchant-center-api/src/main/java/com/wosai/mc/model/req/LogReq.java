package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class LogReq {
    /**
     * 以下是记日志使用的字段
     */
    //日志模板code
    @JsonProperty("log_template_code")
    private String logTemplateCode;

    //操作人用户id
    @JsonProperty("op_user_id")
    private String opUserId;

    //操作人用户名称
    @JsonProperty("op_user_name")
    private String opUserName;

    //操作人用户名称
    @JsonProperty("remark")
    private String remark;

    private String platformCode;

    public boolean isValid() {
        return !StringUtils.isAnyEmpty(opUserId, opUserName, logTemplateCode);
    }
}
