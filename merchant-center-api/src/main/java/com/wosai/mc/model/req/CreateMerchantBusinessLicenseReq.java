package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 营业执照信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@Accessors(chain = true)
public class CreateMerchantBusinessLicenseReq {

    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    //  =========== 营业证照信息 ===========
    /**
     * 0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
     */
    private Integer type;

    /**
     * 照片
     */
    private String photo;

    /**
     * 注册号
     */
    private String number;

    /**
     * 名称
     */
    private String name;

    /**
     * 经营范围
     */
    @JsonProperty("business_scope")
    private String businessScope;

    /**
     * 有效期
     */
    private String validity;

    /**
     * 注册地址
     */
    private String address;

    //  =========== 法人或经营者信息 ===========

    /**
     * 法人证件类型：1 身份证；2 外国护照； 3 台胞证； 4 港澳通行证；5.中国护照
     */
    @JsonProperty("legal_person_id_type")
    private Integer legalPersonIdType;

    /**
     * 法人身份证正面照
     */
    @JsonProperty("legal_person_id_card_front_photo")
    private String legalPersonIdCardFrontPhoto;

    /**
     * 法人身份证反面照
     */
    @JsonProperty("legal_person_id_card_back_photo")
    private String legalPersonIdCardBackPhoto;

    /**
     * 法人姓名
     */
    @JsonProperty("legal_person_name")
    private String legalPersonName;

    /**
     * 法人证件号码
     */
    @JsonProperty("legal_person_id_number")
    private String legalPersonIdNumber;

    /**
     * 身份证有效期
     */
    @JsonProperty("id_validity")
    private String idValidity;

    /**
     * 法人身份证住址
     */
    @JsonProperty("legal_person_id_card_address")
    private String legalPersonIdCardAddress;

    /**
     * 法人身份证签发机关
     */
    @JsonProperty("legal_person_id_card_issuing_authority")
    private String legalPersonIdCardIssuingAuthority;

    //   =========== 其他信息 ===========

    /**
     * 授权函
     */
    @JsonProperty("letter_of_authorization")
    private String letterOfAuthorization;

    /**
     * 行业许可证
     */
    @JsonProperty("trade_license")
    private String tradeLicense;

    private List<CreateLicenseWithBusinessReq> trade_license_list;


    public CreateMerchantBusinessLicenseReq setPhoto(String photo) {
        this.photo = PhotoUtils.baseUrl(photo);
        return this;
    }

    public CreateMerchantBusinessLicenseReq setLegalPersonIdCardFrontPhoto(String legalPersonIdCardFrontPhoto) {
        this.legalPersonIdCardFrontPhoto = PhotoUtils.baseUrl(legalPersonIdCardFrontPhoto);
        return this;
    }

    public CreateMerchantBusinessLicenseReq setLegalPersonIdCardBackPhoto(String legalPersonIdCardBackPhoto) {
        this.legalPersonIdCardBackPhoto = PhotoUtils.baseUrl(legalPersonIdCardBackPhoto);
        return this;
    }
}
