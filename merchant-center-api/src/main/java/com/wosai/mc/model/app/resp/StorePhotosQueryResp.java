package com.wosai.mc.model.app.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
@Data
@Accessors(chain = true)
public class StorePhotosQueryResp {

    @JsonProperty("brand_photo_url")
    private String brandPhotoUrl;
    @JsonProperty("indoor_material_url")
    private String indoorMaterialUrl;
    @JsonProperty("outdoor_material_url")
    private String outdoorMaterialUrl;
    @JsonProperty("other_photos")
    private List<String> otherPhotos;
    @JsonProperty("other_photo")
    private List<String> otherPhoto;
}
