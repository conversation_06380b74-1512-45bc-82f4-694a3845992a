package com.wosai.mc.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.mc.Validators.UpdateMerchantReqCheck;
import com.wosai.mc.utils.PhotoUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Map;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@Accessors(chain = true)
@UpdateMerchantReqCheck
public class UpdateMerchantReq {

    /**
     * 商户id
     */
    @NotBlank(message = "id不可为空")
    private String id;

    /**
     * 商户名
     */
    @Size(max = 60, message = "name不能超过{max}字符")
    private String name;

    /**
     * 商户别名，不传默认等于name
     */
    @Size(max = 60, message = "alias不能超过{max}字符")
    private String alias;

    /**
     * 商户经营名称
     */
    @Size(max = 60, message = "business_name不能超过{max}字符")
    @JsonProperty("business_name")
    private String businessName;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 类型  0自营  1加盟
     */
    private Integer type;

    /**
     * 提现方式
     */
    private String withdraw_mode;

    /**
     * 经度
     */
    @Pattern(regexp = "\\d{1,3}\\.\\d{1,6}", message = "经度为小数，小数点前后分别最多6位")
    private String longitude;

    /**
     * 纬度
     */
    @Pattern(regexp = "\\d{1,3}\\.\\d{1,6}", message = "经度为小数，小数点前后分别最多6位")
    private String latitude;

    /**
     * 省
     */
    @Size(max = 32, message = "province不能超过{max}字符")
    private String province;

    /**
     * 市
     */
    @Size(max = 32, message = "city不能超过{max}字符")
    private String city;

    /**
     * 区
     */
    @Size(max = 32, message = "district不能超过{max}字符")
    private String district;

    /**
     * 地区码
     */
    @JsonProperty("district_code")
    private String districtCode;

    /**
     * 街道地址
     */
    @Size(max = 255, message = "street_address不能超过{max}字符")
    @Pattern(regexp = ".*[\\u4e00-\\u9fa5].*", message = "商户联系地址中必须包含至少一个中文")
    @JsonProperty("street_address")
    private String streetAddress;

    /**
     * 街道地址备注说明
     */
    @Size(max = 255, message = "street_address_desc不能超过{max}字符")
    //@Pattern(regexp = ".*[\\u4e00-\\u9fa5].*", message = "商户联系地址备注说明中必须包含至少一个中文")
    @JsonProperty("street_address_desc")
    private String streetAddressDesc;

    /**
     * 联系人姓名
     */
    @Size(max = 40, message = "contact_name不能超过{max}字符")
    @Pattern(regexp = "^((?![\\u3000-\\u3009\\u3012-\\u303F])[ \\u2E80-\\uFE4Fa-zA-Z【·】]){2,40}$", message = "联系人只支持中文及【·】或英文及空格")
    @JsonProperty("contact_name")
    private String contactName;

    /**
     * 联系手机号
     */
    @Size(max = 32, message = "contact_cellphone不能超过{max}字符")
    @JsonProperty("contact_cellphone")
    private String contactCellphone;

    /**
     * 客服电话
     */
    @Size(max = 32, message = "customer_phone不能超过{max}字符")
    @JsonProperty("customer_phone")
    private String customerPhone;

    /**
     * 联系邮箱
     */
    @Size(max = 30, message = "contact_email不能超过{max}字符")
    @JsonProperty("contact_email")
    private String contactEmail;
    /**
     * 法人身份证正面照
     */
    @Size(max = 255, message = "legal_person_id_card_front_photo不能超过{max}字符")
    @JsonProperty("legal_person_id_card_front_photo")
    private String legalPersonIdCardFrontPhoto;
    /**
     * 法人身份证反面照
     */
    @Size(max = 255, message = "legal_person_id_card_back_photo不能超过{max}字符")
    @JsonProperty("legal_person_id_card_back_photo")
    private String legalPersonIdCardBackPhoto;
    /**
     * 营业执照照片
     */
    @Size(max = 255, message = "business_license_photo不能超过{max}字符")
    @JsonProperty("business_license_photo")
    private String businessLicensePhoto;
    /**
     * 营业执照照片
     */
    @Size(max = 255, message = "business不能超过{max}字符")
    private String business;
    /**
     * 所有人姓名
     */
    @Size(max = 50, message = "owner_name不能超过{max}字符")
    @JsonProperty("owner_name")
    private String ownerName;
    /**
     * 所有人姓名
     */
    @Size(max = 32, message = "owner_cellphone不能超过{max}字符")
    @JsonProperty("owner_cellphone")
    private String ownerCellphone;

    @JsonProperty("merchant_type")
    private Integer merchantType;

    /**
     * 商户规模
     */
    @JsonProperty("merchant_scale")
    private Integer merchantScale;
    /**
     * 门店logo
     */
    @Size(max = 255, message = "logo不能超过{max}字符")
    private String logo;
    /**
     * 商户外部商户号
     */
    @Size(max = 50, message = "client_sn不能超过{max}字符")
    @JsonProperty("client_sn")
    private String clientSn;

    private String operator;

    private String platform;

    /**
     * 扩展字段
     */
    private Map extra;

    @JsonProperty("binded_store_id")
    private String bindedStoreId;


    /**
     * 本字段不是merchant表内字段,用于校验逻辑
     */
    @JsonProperty("is_crm")
    private boolean is_crm;

    public UpdateMerchantReq setLegalPersonIdCardFrontPhoto(String legalPersonIdCardFrontPhoto) {
        this.legalPersonIdCardFrontPhoto = PhotoUtils.baseUrl(legalPersonIdCardFrontPhoto);
        return this;
    }

    public UpdateMerchantReq setLegalPersonIdCardBackPhoto(String legalPersonIdCardBackPhoto) {
        this.legalPersonIdCardBackPhoto = PhotoUtils.baseUrl(legalPersonIdCardBackPhoto);
        return this;
    }

    public UpdateMerchantReq setBusinessLicensePhoto(String businessLicensePhoto) {
        this.businessLicensePhoto = PhotoUtils.baseUrl(businessLicensePhoto);
        return this;
    }
}
