package com.wosai.mc.Validators;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Constraint(validatedBy = CreateStoreReqValidator.class)
public @interface CreateStoreReqCheck {
	
	// 校验未通过时的返回信息
    String message() default "经纬度必须同时有值";

	// 以下两行为固定模板
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

}
