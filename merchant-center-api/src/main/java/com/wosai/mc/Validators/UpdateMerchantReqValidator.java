package com.wosai.mc.Validators;

import com.wosai.mc.model.req.UpdateMerchantReq;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-19
 * @Description:
 */


public class UpdateMerchantReqValidator implements ConstraintValidator<UpdateMerchantReqCheck, UpdateMerchantReq> {

    @Override
    public boolean isValid(UpdateMerchantReq updateMerchantReq, ConstraintValidatorContext constraintValidatorContext) {
        String latitude = updateMerchantReq.getLatitude();
        String longitude = updateMerchantReq.getLongitude();
        if (StringUtils.isEmpty(latitude) && StringUtils.isEmpty(longitude)) {
            return true;
        } else if (StringUtils.isNotEmpty(latitude) && StringUtils.isNotEmpty(longitude)) {
            return true;
        } else {
            return false;
        }
    }
}
