package com.wosai.mc.Validators;

import com.wosai.mc.model.req.UpdateStoreReq;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-19
 * @Description:
 */


public class UpdateStoreReqValidator implements ConstraintValidator<UpdateStoreReqCheck, UpdateStoreReq> {


    @Override
    public boolean isValid(UpdateStoreReq updateStoreReq, ConstraintValidatorContext constraintValidatorContext) {
        String latitude = updateStoreReq.getLatitude();
        String longitude = updateStoreReq.getLongitude();
        if (StringUtils.isEmpty(latitude) && StringUtils.isEmpty(longitude)) {
            return true;
        } else if (StringUtils.isNotEmpty(latitude) && StringUtils.isNotEmpty(longitude)) {
            return true;
        } else {
            return false;
        }
    }
}
