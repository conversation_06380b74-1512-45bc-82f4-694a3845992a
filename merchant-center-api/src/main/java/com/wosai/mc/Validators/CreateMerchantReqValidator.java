package com.wosai.mc.Validators;

import com.wosai.mc.model.req.CreateMerchantReq;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-19
 * @Description:
 */


public class CreateMerchantReqValidator implements ConstraintValidator<CreateMerchantReqCheck, CreateMerchantReq> {

    @Override
    public boolean isValid(CreateMerchantReq createMerchantReq, ConstraintValidatorContext constraintValidatorContext) {
        String latitude = createMerchantReq.getLatitude();
        String longitude = createMerchantReq.getLongitude();
        if (StringUtils.isEmpty(latitude) && StringUtils.isEmpty(longitude)) {
            return true;
        } else if (StringUtils.isNotEmpty(latitude) && StringUtils.isNotEmpty(longitude)) {
            return true;
        } else {
            return false;
        }
    }
}
