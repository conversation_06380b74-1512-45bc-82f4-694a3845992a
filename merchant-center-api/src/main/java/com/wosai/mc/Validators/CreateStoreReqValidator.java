package com.wosai.mc.Validators;

import com.wosai.mc.model.req.CreateStoreReq;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-19
 * @Description:
 */


public class CreateStoreReqValidator implements ConstraintValidator<CreateStoreReqCheck, CreateStoreReq> {

    @Override
    public boolean isValid(CreateStoreReq createStoreReq, ConstraintValidatorContext constraintValidatorContext) {
        String latitude = createStoreReq.getLatitude();
        String longitude = createStoreReq.getLongitude();
        if (StringUtils.isEmpty(latitude) && StringUtils.isEmpty(longitude)) {
            return true;
        } else if (StringUtils.isNotEmpty(latitude) && StringUtils.isNotEmpty(longitude)) {
            return true;
        } else {
            return false;
        }
    }
}
