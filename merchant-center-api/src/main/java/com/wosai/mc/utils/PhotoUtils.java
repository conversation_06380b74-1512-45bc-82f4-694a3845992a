package com.wosai.mc.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/9/15
 */
public class PhotoUtils {

    /**
     * 临时授权地址返回成基础地址
     *
     * @return
     */
    public static String baseUrl(String encryptUrl) {
        //不带private的地址不解析
        if (StringUtils.isEmpty(encryptUrl) || !encryptUrl.contains("private-")) {
            return encryptUrl;
        }
        String[] split = encryptUrl.split(",http");
        StringJoiner joiner = new StringJoiner(",http");
        for(String url : split) {
            joiner.add(doBaseUrl(url));
        }
        return joiner.toString();
    }

    /**
     * 处理单个url
     * @param encryptUrl
     * @return
     */
    private static String doBaseUrl(String encryptUrl) {
        //不带private的地址不解析
        if (StringUtils.isNotEmpty(encryptUrl) && encryptUrl.contains("private-")) {
            encryptUrl = encryptUrl.replaceAll("&amp;", "&");
            //&或者?开头 ，不区分大小写的expires,ossaccesskeyid,signature,security-token 4个授权属性 写一个&,?前结束（此处把?也判断进去防止前面拼的时候就是错误的）
            encryptUrl = encryptUrl.replaceAll("([&|?]((?i)expires|(?i)ossaccesskeyid|(?i)signature|(?i)security-token)=[^&|^?]*)", "");
            if (encryptUrl.contains("&") && !encryptUrl.contains("?")) {
                encryptUrl = encryptUrl.replaceFirst("&", "?");
            }
        }
        return encryptUrl;
    }
}
